// -*- C++ -*-
//
// summit development team
// massachusetts institute of technology
// (c) 2012-2013 all rights reserved
//

#include <algorithm>
#include <iostream>
#include <stdexcept>

#include "../materials/material.h"
#include "../weakforms/region.h"
#include "../weakforms/mechanics_weak_form.h"
#include "../weakforms/singlephase_flow_weak_form.h"
#include "../weakforms/transport_manifold_weak_form.h"
#include "../weakforms/poisson_manifold_weak_form.h"
#include "../weakforms/reaction_diffusion_weak_form.h"

#include "matrix_free_system.h"

summit::MatrixFreeSystem::MatrixFreeSystem(FunctionSpace& function_space,
                                           const MaterialLibrary& material_lib,
                                           const summit::REGION rtype)
  : NonlinearSystem(function_space), _proxy(false), _rtype(rtype)
{
    if (_rtype == MECHANICS) {
        // build the weak form and fill the attribute of the base class
        _weak_form = new MechanicsWeakForm(function_space, material_lib);
        _displacement = new NodalField<real>("displacement");
        _displacement_prev = new NodalField<real>("displacement_previous");
    }
    else if (_rtype == POISSONMANIFOLD || _rtype == EXTENDED_POISSONMANIFOLD) {
        // build the weak form and fill the attribute of the base class
        _weak_form = new PoissonManifoldWeakForm(_rtype, function_space, material_lib);
        _displacement = new NodalField<real>("pressure");
        _displacement_prev = new NodalField<real>("pressure_previous");
    }
    else if (_rtype == REACTION_DIFFUSION) {
        // build the weak form and fill the attribute of the base class
        _weak_form = new ReactionDiffusionWeakForm(function_space, material_lib);
        _displacement = new NodalField<real>("Primal");
        _displacement_prev = new NodalField<real>("Primal Previous");
    }
    else {
        // complain
        throw std::logic_error("Unknown WeakForm type in MatrixFreeSystem");
    }

    // resize _displacement
    _displacement->resize(_weak_form->numberNodalUnknowns(), _weak_form->numberUnknownsEntities());
    _displacement_prev->resize(_weak_form->numberNodalUnknowns(),
                               _weak_form->numberUnknownsEntities());

    // register fields
    this->RegisterField(*_displacement);
    this->RegisterField(*_displacement_prev);

    return;
}

summit::MatrixFreeSystem::MatrixFreeSystem(FunctionSpace& function_space,
                                           const MaterialLibrary& material_lib,
                                           const real alpha,
                                           const summit::REGION rtype)
  : NonlinearSystem(function_space), _proxy(false), _rtype(rtype)
{
    if (_rtype == TRANSPORT) {
        // build the weak form and fill the attribute of the base class
        _weak_form = new TransportManifoldWeakForm(_rtype, function_space, material_lib, alpha);
        _displacement = new NodalField<real>("concentration");
        _displacement_prev = new NodalField<real>("pressure_previous");
    }
    else {
        // complain
        throw std::logic_error("Unknown WeakForm type in MatrixFreeSystem");
    }

    // resize _displacement
    _displacement->resize(_weak_form->numberNodalUnknowns(), _weak_form->numberUnknownsEntities());
    _displacement_prev->resize(_weak_form->numberNodalUnknowns(),
                               _weak_form->numberUnknownsEntities());

    // register fields
    this->RegisterField(*_displacement);
    this->RegisterField(*_displacement_prev);

    return;
}

summit::MatrixFreeSystem::MatrixFreeSystem(FunctionSpace& function_space,
                                           const MaterialLibrary& bulk_medium_material_library,
                                           FunctionSpaceInternal& function_space_internal,
                                           const MaterialLibrary& crack_medium_material_library,
                                           const summit::REGION rtype,
                                           const summit::PHASE phase,
                                           const Functor<real>* gravityTerm)
  : NonlinearSystem(function_space), _proxy(false), _rtype(rtype)
{
    if (_rtype == SINGLEPHASE_FLOW) {
        // build the weak form and fill the attribute of the base class
        _weak_form = new SinglePhaseFlowWeakForm(function_space, bulk_medium_material_library,
                                                 function_space_internal,
                                                 crack_medium_material_library, phase, gravityTerm);
        if (phase == OIL || phase == WATER) {
            _displacement = new NodalField<real>("pressure");
            _displacement_prev = new NodalField<real>("pressure_previous");
        }
        else if (phase == GAS) {
            _displacement = new NodalField<real>("pressure squared");
            _displacement_prev = new NodalField<real>("pressure squared_previous");
        }
        else {
            // complain
            throw std::logic_error("Unknown PHASE type in MatrixFreeSystem");
        }
    }
    else {
        // complain
        throw std::logic_error("Unknown WeakForm type in MatrixFreeSystem");
    }

    // resize _displacement
    _displacement->resize(_weak_form->numberNodalUnknowns(), _weak_form->numberUnknownsEntities());
    _displacement_prev->resize(_weak_form->numberNodalUnknowns(),
                               _weak_form->numberUnknownsEntities());

    // register fields
    this->RegisterField(*_displacement);
    this->RegisterField(*_displacement_prev);

    return;
}

summit::MatrixFreeSystem::MatrixFreeSystem(FunctionSpace& function_space,
                                           WeakForm* wform,
                                           NodalField<real>* unknown)
  : NonlinearSystem(function_space), _displacement(unknown), _proxy(true)
{
    _weak_form = wform;

    return;
}


summit::MatrixFreeSystem::MatrixFreeSystem(Checkpoint* checkpoint, const char* name)
  : NonlinearSystem(checkpoint, name), _proxy(checkpoint->read<int>("proxy"))
{
    Group* weak_form = checkpoint->OpenGroup("weakform");
    std::string wf_type = weak_form->tag();
    _weak_form = Factory<WeakForm>::create(wf_type)(checkpoint, "weakform");
    checkpoint->MoveUp();

    if (_proxy) {
        return;
    }

    summit::Message::Info(
      "Warning in MatrixFreeSystem::MatrixFreeSystem: The nodal field are called 'displacement' "
      "and 'displacementPrev' no matter what the region type is! To be fixed in the future.");

    _displacement = new NodalField<real>(checkpoint, "displacement");
    checkpoint->set_reference("DisplacementField", _displacement);
    _displacement_prev = new NodalField<real>(checkpoint, "displacementPrev");

    this->RegisterField(*_displacement);
    this->RegisterField(*_displacement_prev);
}

summit::MatrixFreeSystem::~MatrixFreeSystem()
{
    if (_proxy) return;
    delete _displacement;
    delete _weak_form;
    delete _displacement_prev;
}

void summit::MatrixFreeSystem::AllocateFields()
{
    _displacement->resize(_weak_form->numberNodalUnknowns(), _weak_form->numberUnknownsEntities());
    _displacement_prev->resize(_weak_form->numberNodalUnknowns(),
                               _weak_form->numberUnknownsEntities());

    // set initial values for nodal fields
    std::fill(_displacement->begin(), _displacement->end(), 0.);
    std::fill(_displacement_prev->begin(), _displacement_prev->end(), 0.);

    return;
}

summit::real summit::MatrixFreeSystem::StableTimestep(const CommunicationManager& commManager)
{
    // return the stable time-step computed in the weak form
    return _weak_form->StableTimeStep(_function_space.coordinates(), *_displacement, commManager);
}

bool summit::MatrixFreeSystem::CheckResetDamage(const CommunicationManager& commManager)
{
    // resets the damage bool, and also returns if we had any damage at all
    return _weak_form->CheckResetDamage( commManager);
}

summit::real summit::MatrixFreeSystem::StableTimestepMassScale(
  const CommunicationManager& commManager, const real mass_scale_dt)
{
    // return the stable time-step computed in the weak form
    return _weak_form->StableTimeStepMassScale(_function_space.coordinates(), *_displacement,
                                               commManager, mass_scale_dt);
}

void summit::MatrixFreeSystem::AssembleStiffness(Stiffness& stiffness, NodalField<real>& r, real dt)
{
    // complain
    throw std::runtime_error("You try to compute the stiffness matrix of a matrix free system...");
}

void summit::MatrixFreeSystem::AssembleMass(NodalField<real>& mass,
                                            const CommunicationManager& commManager,
                                            const bool doIreduce)
{
    // call the AssembleMass method of the weak form
    _weak_form->AssembleMass(mass, commManager, doIreduce);

    // end of method
    return;
}

void summit::MatrixFreeSystem::AssembleMass(NodalField<real>& mass)
{
    // call the AssembleMass method of the weak form
    _weak_form->AssembleMass(mass);

    // end of method
    return;
}

void summit::MatrixFreeSystem::AssembleScaledMass(const real mass_scale_dt, NodalField<real>& mass)
{
    // call the AssembleMass method of the weak form
    static_cast<MechanicsWeakForm*>(_weak_form)->AssembleScaledMass(_function_space.coordinates(), *_displacement, mass_scale_dt, mass);
    // end of method
    return;
}

void summit::MatrixFreeSystem::AssembleScaledMass(const real mass_scale_dt, NodalField<real>& mass,
                                                  const CommunicationManager& commManager)
{
    // call the AssembleMass method of the weak form
    static_cast<MechanicsWeakForm*>(_weak_form)->AssembleScaledMass(_function_space.coordinates(), *_displacement, mass_scale_dt, mass, commManager);

    // end of method
    return;
}


void summit::MatrixFreeSystem::ComputeAccelerationWithBlockMassMatrix(
  NodalField<real> const& residual,
  NodalField<int> const& boundaryConditionType,
  NodalField<real>& acceleration,
  const CommunicationManager& commManager,
  const real gammaDt)
{
    // call the ComputeAccelerationWithBlockMassMatrix method of the weak form
    _weak_form->ComputeAccelerationWithBlockMassMatrix(residual, boundaryConditionType,
                                                       acceleration, commManager, gammaDt);

    // end of method
    return;
}


void summit::MatrixFreeSystem::ComputeInertiaIntegralFromAcceleration(NodalField<real> const& acceleration,
                                                                      NodalField<real>& residual){
    static_cast<MechanicsWeakForm*>(_weak_form)->ComputeInertiaIntegralFromAcceleration(acceleration,residual);
    return;
}

void summit::MatrixFreeSystem::AssembleResidual(NodalField<real>& r,
                                                const real dt,
                                                const CommunicationManager& commManager,
                                                const bool update,
                                                const bool doIreduce)
{
    // call the residual method of the weak form
    _weak_form->Residual(*_displacement, /*dummy input*/ *_displacement, dt, update, r,
                         commManager, doIreduce);

    // end of method
    return;
}

void summit::MatrixFreeSystem::AssembleDampedResidual(NodalField<real>& residual,
                                                      const summit::real dt,
                                                      const CommunicationManager& commManager,
                                                      const bool update)
{
    std::cout << "The method MatrixFreeSystem::AssembleDampedResidual has been called. "
              << "This probably means that you tried to call this method on a System that is not"
              << " a DynamicsSystem. DynamicsSystem is the only class in the inheritance of"
              << " MatrixFreeSystemSystem that has a meaningful implementation!"
              << " Nothing has been done." << std::endl;

    // throw an exception
    throw std::logic_error("Not implemented");
}

void summit::MatrixFreeSystem::AssembleBulkResidual(NodalField<real> const& u,
                                                    NodalField<real> const& u0,
                                                    NodalField<real>& r,
                                                    const real dt,
                                                    const CommunicationManager& commManager,
                                                    const bool update)
{
    // call the residual method of the weak form
    _weak_form->BulkResidual(u, u0, dt, update, r, commManager);

    // end of method
    return;
}

const summit::NodalField<summit::real>& summit::MatrixFreeSystem::GetNodalField(
  std::string const& name, CommunicationManager const& commManager) const
{
    if (name == "displacement") return *_displacement;
    return NonlinearSystem::GetNodalField(name, commManager);
}


void summit::MatrixFreeSystem::DisplacementNextStep()
{
    std::cout
      << "Error:  method MatrixFreeSystem::DisplacementNextStep has not been implemented yet"
      << std::endl;
    exit(1);
    return;
}


void summit::MatrixFreeSystem::WriteForRestart(Checkpoint* checkpoint,
                                               const char* name,
                                               const char* tag) const
{
    assert(tag != nullptr);
    NonlinearSystem::WriteForRestart(checkpoint, name, tag);

    // create a new group for the weakform in the binary file
    std::string groupNameWeakform = "weakform";
    checkpoint->CreateGroup(groupNameWeakform.c_str());
    _weak_form->WriteForRestart(checkpoint, groupNameWeakform.c_str());
    // Move up from "weakform"
    checkpoint->MoveUp();

    checkpoint->write<int>("proxy", (int)_proxy);

    _displacement->WriteForRestart(checkpoint, "displacement");

    _displacement_prev->WriteForRestart(checkpoint, "displacementPrev");

    return;
}
// end of file
