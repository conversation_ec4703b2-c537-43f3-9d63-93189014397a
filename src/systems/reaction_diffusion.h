// -*- C++ -*-
//
// summit development team
// massachusetts institute of technology
// (c) 2012-2013 all rights reserved
//

#ifndef SUMMIT_REACTION_DIFFUSION_MODEL_SYSTEM_H
#define SUMMIT_REACTION_DIFFUSION_MODEL_SYSTEM_H

#include "../fem/function_space.h"
#include "matrix_free_system.h"

namespace summit {

/**
 * Forward declaration
 */
class MaterialLibrary;
class CommunicationManager;

class ReactionDiffusionSystem : public MatrixFreeSystem {
  public:
    /**
     * Constructor
     */
    ReactionDiffusionSystem(FunctionSpace& function_space,
                       const MaterialLibrary& material_library);

    /**
     * Destructor
     */
    virtual ~ReactionDiffusionSystem();

  private:
    /**
     * Default constructor
     */
    ReactionDiffusionSystem();

  public:
    /**
     * Method to query memory allocation for the fields that are attributes of the class
     */
    virtual void AllocateFields();

    /**
     * Set the names of the primal components for inclusion in VTK output
     */
    void setPrimalComponentNames(const std::vector<std::string>& names);

    /**
     * Method to calculate the stable time step
     * @param[in] Communication Manager
     */
    real StableTimestep(const CommunicationManager& commManager);

    /**
     * Method to Assemble the Mass matrix.
     * @param[in/out] mass the global nodal field in which the mass matrix is written.
     * @param[in] commManager a CommunicationManager
     */
    virtual void AssembleMass(NodalField<real>& mass, const CommunicationManager& commManager);

    /**
     * Method to assemble the residual
     */
    virtual void AssembleResidual(NodalField<real>& residual,
                                      const real dt,
                                      const CommunicationManager& commManager,
                                      const bool update = false);

    /**
     * Method to assemble the stiffness which is used for DG case
     * @param[in,out] stiffness stiffness matrix
     * @param[in,out] residual residual vector
     * @param[in] dt time step sometime needed, the solve method in classes derived from
     *        StaticSolver passes a default value for this parameter
     * @param[in] commManager communication manager
     */
    virtual void AssembleStiffness(Stiffness& stiffness,
                                   NodalField<real>& residual,
                                   real dt,
                                   const CommunicationManager& commManager);


    virtual void AssembleStiffness(Stiffness& stiffness,
                                   NodalField<real>& residual,
                                   real dt);

    /**
     * Mutator to field of unknown
     * @return a reference to the field of unknown
     */
    virtual NodalField<real>& u();

    /**
     * Mutator to field of unknown at previous time step
     * @return a reference to the field of unknown at previous time step
     */
    virtual NodalField<real>& u0() { return *_chemicals_prev; }

    /**
     * Method to update a single internal variable in the system
     * @param[in] name a string containing the name of the internal variable to update
     * @param[in] commManager a communication manager
     */
    void Update(const CommunicationManager& commManager, real dt);
    
    void Update(std::string const& name, const CommunicationManager& commManager, real dt);

    void Update(const CommunicationManager& commManager) override;
    
    void Update(std::string const& name, const CommunicationManager& commManager) override;

    /**
     */
    NodalField<real>& c() { return *_chemicals; }
    NodalField<real>& c0() { return *_chemicals_prev; }
    NodalField<real>& dcdt() { return *_chemicals_time_derivative; }

    /**
     */
    virtual const NodalField<real>& u() const { return *_chemicals; }
    virtual const NodalField<real>& c() const { return *_chemicals; }
    virtual const NodalField<real>& c0() const { return *_chemicals_prev; }
    virtual const NodalField<real>& dcdt() const { return *_chemicals_time_derivative; }

    /**
     */
    virtual void TemperatureNextStep();

    /**
     * Method to compute the acceleration
     * @param[in] residual the global nodal field in which the residual is stored
     * @param[in] boundaryConditionType the global nodal field in which the type of boundary
     *            conditions is stored
     * @param[out] acceleration the global nodal field in which the acceleration is stored
     * @param[in] commManager a CommunicationManager
     * @param[in] gammaDt Newmark's gamma parameter multiplied by the time step
     */
    void ComputeAccelerationWithBlockMassMatrix(NodalField<real> const& residual,
                                                NodalField<real>& acceleration);

  protected:
    /**
     * Nodal Fields
     */
    NodalField<real>* _chemicals;
    NodalField<real>* _chemicals_prev;
    NodalField<real>* _chemicals_time_derivative;
};
}  // namespace summit

#endif  // SUMMIT_REACTION_DIFFUSION_MODEL

// end of file
