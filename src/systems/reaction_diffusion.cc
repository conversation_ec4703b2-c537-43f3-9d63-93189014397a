// -*- C++ -*-
//
// summit development team
// massachusetts institute of technology
// (c) 2012-2013 all rights reserved
//

#include <algorithm>
#include <iostream>
#include <stdexcept>

#include "../materials/material.h"
#include "../weakforms/region.h"
#include "../weakforms/reaction_diffusion_weak_form.h"
#include "../solvers/stiffness/stiffnessPETSc.h"
#include "reaction_diffusion.h"

summit::ReactionDiffusionSystem::ReactionDiffusionSystem(FunctionSpace& function_space,
                                               const MaterialLibrary& bulk_medium_material_library)
  : MatrixFreeSystem(function_space, bulk_medium_material_library, REACTION_DIFFUSION)
{
    // MatrixFreeSystem constructor has already created _weak_form, _displacement, and _displacement_prev
    // We need to set up our specific field pointers to point to the base class fields
    _chemicals = _displacement;
    _chemicals_prev = _displacement_prev;

    // Create the time derivative field
    _chemicals_time_derivative = new NodalField<real>("Primal Rate");
    _chemicals_time_derivative->resize(_weak_form->numberNodalUnknowns(),
                                              _weak_form->numberUnknownsEntities());
    std::fill(_chemicals_time_derivative->begin(), _chemicals_time_derivative->end(), 0.);

    // register the time derivative field
    this->RegisterField(*_chemicals_time_derivative);

    // all done
    return;
}

summit::ReactionDiffusionSystem::~ReactionDiffusionSystem()
{
    // _chemicals and _chemicals_prev are now owned by MatrixFreeSystem base class
    // Only delete the time derivative field that we created
    delete _chemicals_time_derivative;

    // _weak_form is also owned by MatrixFreeSystem base class

    // all done
    return;
}

void summit::ReactionDiffusionSystem::AllocateFields()
{
    // Call base class AllocateFields to handle _chemicals and _chemicals_prev
    MatrixFreeSystem::AllocateFields();

    // Handle our specific time derivative field
    _chemicals_time_derivative->resize(_weak_form->numberNodalUnknowns(),
                              _weak_form->numberUnknownsEntities());
    std::fill(_chemicals_time_derivative->begin(), _chemicals_time_derivative->end(), 0);

    // all done
    return;
}

void summit::ReactionDiffusionSystem::setPrimalComponentNames(const std::vector<std::string>& names)
{
    _chemicals->set_component_names(names);
    _chemicals_prev->set_component_names(names);
    _chemicals_time_derivative->set_component_names(names);
    return; // All done
}


summit::real summit::ReactionDiffusionSystem::StableTimestep(const CommunicationManager& commManager)
{
    // check whether the weakform is a ReactionDiffusionWeakForm
    ReactionDiffusionWeakForm* weakForm = dynamic_cast<ReactionDiffusionWeakForm*>(_weak_form);

    // if it is
    if (weakForm) {
        // return the stable time-step computed in the weak form
        return weakForm->StableTimeStep(_function_space.coordinates(), *_chemicals, commManager);
    }

    // for everybody else
    throw std::logic_error("The current weak form is not a ReactionDiffusionWeakForm");
}

void summit::ReactionDiffusionSystem::AssembleMass(NodalField<real>& mass,
                                                  const CommunicationManager& commManager)
{
    // check whether the weakform is a ReactionDiffusionWeakForm
    ReactionDiffusionWeakForm* weakForm = dynamic_cast<ReactionDiffusionWeakForm*>(_weak_form);

    // if it is
    if (weakForm) {
        // call the AssembleMass method of the weak form
        weakForm->AssembleMass(mass, commManager);
    }
    else {
        // for everybody else
        throw std::logic_error("The current weak form is not a ReactionDiffusionWeakForm");
    }

    // all done
    return;
}

void summit::ReactionDiffusionSystem::Update(const CommunicationManager& commManager, real dt)
{
    // ask the weakform to update
    _weak_form->Update(*_chemicals, *_chemicals_prev, /*static = long time*/ dt, commManager);

    // end of method
    return;
}

void summit::ReactionDiffusionSystem::Update(std::string const& name, CommunicationManager const& commManager, real dt)
{
    // ask the weakform to update
    _weak_form->Update(*_chemicals, *_chemicals_prev, /*static = long time*/ dt, name,
                       commManager);

    // end of method
    return;
}

void summit::ReactionDiffusionSystem::Update(const CommunicationManager& commManager)
{
    // ask the weakform to update
    _weak_form->Update(*_chemicals, *_chemicals_prev, /*static = long time*/ 1.0, commManager);

    // end of method
    return;
}

void summit::ReactionDiffusionSystem::Update(std::string const& name, CommunicationManager const& commManager)
{
    // ask the weakform to update
    _weak_form->Update(*_chemicals, *_chemicals_prev, /*static = long time*/ 1.0, name,
                       commManager);

    // end of method
    return;
}

void summit::ReactionDiffusionSystem::AssembleResidual(NodalField<real>& residual,
                                                      const real dt,
                                                      const CommunicationManager& commManager,
                                                      const bool update)
{
    // check whether the weakform is a ReactionDiffusionWeakForm
    ReactionDiffusionWeakForm* weakForm = dynamic_cast<ReactionDiffusionWeakForm*>(_weak_form);

    // if it is
    if (weakForm) {
        // call the residual method of the weak form
        // real norm = 0.0;
        // for (int a = 0; a < residual.nodes(); ++a) {
        //     for (int i = 0; i < residual.dim(); ++i) {
        //         norm += residual(a, i) * residual(a, i);
        //     }
        // }
        // std::cout << "system dimension is: " << residual.dim() <<std::endl;
        // std::cout << "system size is: " << residual.nodes() <<std::endl;
        // std::cout << "Location D is: " << sqrt(norm) <<std::endl;


        weakForm->Residual(*_chemicals, *_chemicals_prev, dt, update,
                               residual, commManager);

        // norm = 0.0;
        // for (int a = 0; a < residual.nodes(); ++a) {
        //     for (int i = 0; i < residual.dim(); ++i) {
        //         norm += residual(a, i) * residual(a, i);
        //     }
        //}
        //std::cout << "Location E is: " << sqrt(norm) <<std::endl;

    }
    else {
        // for everybody else
        throw std::logic_error("The current weak form is not a ReactionDiffusionWeakForm");
    }

    // all done
    return;
}

void summit::ReactionDiffusionSystem::AssembleStiffness(Stiffness& stiffness, NodalField<real>& r, real dt, const CommunicationManager& commManager)
{
    // ask the weakform to assemble the stiffness matrix and the residual
    _weak_form->ComputeStiffness(*_chemicals, *_chemicals_prev, dt, stiffness, r, commManager);

    // end of method
    return;
}

void summit::ReactionDiffusionSystem::AssembleStiffness(Stiffness& stiffness, NodalField<real>& r, real dt)
{
    // ask the weakform to assemble the stiffness matrix and the residual
    _weak_form->ComputeStiffness(*_chemicals, *_chemicals_prev, dt, stiffness, r);

    // end of method
    return;
}

void summit::ReactionDiffusionSystem::TemperatureNextStep()
{
    // copy current fields to previous
    std::copy(_chemicals->begin(), _chemicals->end(),_chemicals_prev->begin());

    // all done
    return;
}

summit::NodalField<real>& summit::ReactionDiffusionSystem::u()
{
    return *_chemicals;
}

void summit::ReactionDiffusionSystem::ComputeAccelerationWithBlockMassMatrix(
  NodalField<real> const& residual,
  NodalField<real>& acceleration)
{
    // call the ComputeAccelerationWithBlockMassMatrix method of the weak form
     static_cast<summit::ReactionDiffusionWeakForm*>(_weak_form)->ComputeAccelerationWithBlockMassMatrix(residual, acceleration);
    // end of method
    return;
}

// end of file
