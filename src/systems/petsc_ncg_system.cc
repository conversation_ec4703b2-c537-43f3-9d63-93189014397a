// -*- C++ -*-
//
// summit development team
// massachusetts institute of technology
// (c) 2012-2013 all rights reserved
//

#include "../weakforms/mechanics_weak_form.h"
#include "../weakforms/poisson_manifold_weak_form.h"
#include "petsc_ncg_system.h"
#include "../solvers/petsc_ncg_solver.h"
#include "../materials/material.h"
#include "../weakforms/region.h"
#include "../weakforms/common_interface_mechanics_region.h"

#include <algorithm>
#include <iostream>

summit::PETScNcgSystem::PETScNcgSystem(FunctionSpace& function_space,
                                       const MaterialLibrary& material_lib,
                                       const summit::REGION rtype)
  : MatrixFreeSystem(function_space, material_lib, rtype)
{
    return;
}

summit::PETScNcgSystem::PETScNcgSystem(FunctionSpace& function_space,
                                       const MaterialLibrary& material_lib,
                                       const summit::REG<PERSON> rtype,
                                       const real alpha)
  : MatrixFreeSystem(function_space, material_lib, alpha, rtype)
{
    return;
}

summit::PETScNcgSystem::PETScNcgSystem(FunctionSpace& function_space,
                                       const MaterialLibrary& bulk_medium_material_library,
                                       FunctionSpaceInternal& function_space_internal,
                                       const MaterialLibrary& crack_medium_material_library,
                                       const summit::REGION rtype,
                                       const summit::PHASE phase,
                                       const Functor<real>* gravityTerm)
  : MatrixFreeSystem(function_space, bulk_medium_material_library, function_space_internal,
                     crack_medium_material_library, rtype, phase, gravityTerm)
{
    return;
}

summit::PETScNcgSystem::PETScNcgSystem(FunctionSpace& function_space,
                                       WeakForm* wform,
                                       NodalField<real>* unknown)
  : MatrixFreeSystem(function_space, wform, unknown)
{
    return;
}

summit::PETScNcgSystem::~PETScNcgSystem() {}

void summit::PETScNcgSystem::Update(const CommunicationManager& commManager)
{
    return _weak_form->Update(*_displacement, *_displacement, /*static = long time*/ 1.0,
                              commManager);
}

void summit::PETScNcgSystem::Update(const CommunicationManager& commManager, real dt)
{
    return _weak_form->Update(*_displacement, *_displacement, dt, commManager);
}

void summit::PETScNcgSystem::Update(std::string const& name,
                                    CommunicationManager const& commManager)
{
    return _weak_form->Update(*_displacement, *_displacement, /*static = long time*/ 1.0, name,
                              commManager);
}

size_t summit::PETScNcgSystem::GetEnergies(std::vector<std::string>& vname) const
{
    // no energy computation for lubrication
    if (!_displacement->name().compare("pressure")) return 0;

    // look if an interface region can propagate fracture
    WeakForm::RegionConstIterator it;
    for (it = weak_form()->beginInterface(); it != weak_form()->endInterface(); ++it) {
        const commonInterfaceMechanicsRegion* ireg =
          static_cast<const commonInterfaceMechanicsRegion*>(*it);

        if (ireg->InterfaceEnergy()) break;
    }

    vname.push_back(std::string("Wext"));
    vname.push_back(std::string("Wint"));
    // Wext and Wint and possibly interface energy
    if (it == weak_form()->endInterface()) return 2;

    vname.push_back(std::string("Wfrac"));
    return 3;
}

void summit::PETScNcgSystem::ComputeEnergies(std::vector<real>& energies,
                                             const summit::CommunicationManager& ComMan) const
{
    // component 0: the Wext
    ComputeExternalWork(energies[0], ComMan);

    // component 1: the Wint
    ComputeInternalWork(energies[1], ComMan);

    // component 2: the interface energy if any
    if (energies.size() > 2) {
        ComputeInterfaceWork(energies[2], ComMan);
    }

    return;
}

void summit::PETScNcgSystem::AssembleJacobiPreconditioner(NodalField<real>& jacobi,
                                                          CommunicationManager const& commManager,
                                                          const real dt) const
{
    // check whether my weak form supports AssembleJacobiPreconditioner
    MechanicsWeakForm* mechWeakForm = dynamic_cast<MechanicsWeakForm*>(_weak_form);

    // if it is mechanics weakform
    if (mechWeakForm) {
        // apply the force
        mechWeakForm->AssembleJacobiPreconditioner(*_displacement, commManager, jacobi);
        // all done
        return;
    }
    else {
        PoissonManifoldWeakForm* poissonWeakForm =
          dynamic_cast<PoissonManifoldWeakForm*>(_weak_form);
        // if it's poisson weakform
        if (poissonWeakForm) {
            // apply the force
            poissonWeakForm->AssembleJacobiPreconditioner(*_displacement, commManager, jacobi, dt);
            // all done
            return;
        }
        else {
            // for everybody else
            throw std::logic_error(
              "the current weak form does not support AssembleJacobiPreconditioner");
        }
    }

    // all done
    return;
}

void summit::PETScNcgSystem::setSourceTerm(Functor<real>& bodyForce)
{
    // check whether my weak form supports body forces
    MechanicsWeakForm* weakForm = dynamic_cast<MechanicsWeakForm*>(_weak_form);

    // if it does
    if (weakForm) {
        // apply the force
        weakForm->setBodyForce(bodyForce);
        // all done
        return;
    }

    // for everybody else
    throw std::logic_error("the current weak form does not support body forces");
}


// end of file
