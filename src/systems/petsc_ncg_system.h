// -*- C++ -*-
//
// summit development team
// massachusetts institute of technology
// (c) 2012-2013 all rights reserved
//

#ifndef SUMMIT_PETSC_NCG_SYSTEM_H
#define SUMMIT_PETSC_NCG_SYSTEM_H

#include "../fem/function_space.h"
#include "matrix_free_system.h"

namespace summit {

/**
 * Forward declaration
 */
class MaterialLibrary;
class CommunicationManager;
class PetscNCGSolver;

class PETScNcgSystem : public MatrixFreeSystem {
  public:
    /**
     * Constructor
     */
    PETScNcgSystem(FunctionSpace& function_space,
                   const MaterialLibrary& material_lib,
                   const summit::REGION rtype = MECHANICS);

    /**
     * Constructor for reaction diffusion systems
     */
    PETScNcgSystem(FunctionSpace& function_space,
                   const MaterialLibrary& material_lib,
                   const summit::REGION rtype,
                   const real alpha);

    /**
     * Constructor for flow systems with fractures
     */
    PETScNcgSystem(FunctionSpace& function_space,
                   const MaterialLibrary& bulk_medium_material_library,
                   FunctionSpaceInternal& function_space_internal,
                   const MaterialLibrary& crack_medium_material_library,
                   const summit::REGION rtype,
                   const summit::PHASE phase,
                   const Functor<real>* gravityTerm = 0);

    /**
     * Proxy constructor
     */
    PETScNcgSystem(FunctionSpace& function_space, WeakForm* wform, NodalField<real>* unknown);


    /**
     * Destructor
     */
    virtual ~PETScNcgSystem();

  private:
    /**
     * Default constructor
     */
    PETScNcgSystem();

    /**
     * Copy constructor
     * @param[in] a const reference to a PETScNcgSystem object
     */
    PETScNcgSystem(PETScNcgSystem const&);

    /**
     * Assignment operator
     * @param[in] a const reference to a PETScNcgSystem object
     * @return a reference to a DynamicSystem object
     */
    PETScNcgSystem& operator=(PETScNcgSystem const&);

  public:
    /**
     * Method to update the system
     */
    void Update(const CommunicationManager& commManager) override;

    /**
     * Method to update the system
     */
    void Update(std::string const& name, CommunicationManager const& commManager) override;

    /**
     * Method to update the system and pass dt
     */
    virtual void Update(const CommunicationManager& commManager, real dt);

    virtual size_t GetEnergies(std::vector<std::string>& vname) const;

    /**
     * Method to compute the different energies present in the system
     * @param[in,out] a real for each energy to compute ADD the value
     * @param[in] summit::CommunicationManager
     */
    virtual void ComputeEnergies(std::vector<real>& energies,
                                 const summit::CommunicationManager& ComMan) const;

    /**
     * Method to assemble the jacobi preconditioner of the system
     * @param[in,out] jacobi a nodal field filled in within the method
     * @param[in] commManager a CommunicationManager
     */
    void AssembleJacobiPreconditioner(NodalField<real>& jacobi,
                                      CommunicationManager const& commManager,
                                      const real dt = 1.0) const;

    /**
     * Set body forces (only works for MechanicsWeakForm)
     * @param[in] bodyForce the body force functor
     */
    void setSourceTerm(Functor<real>& bodyForce);
};
}  // namespace summit

#endif  // PETSC_NCG_SYSTEM

// end of file
