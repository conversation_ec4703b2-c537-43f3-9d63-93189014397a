#ifndef SUMMIT_INTERFACE_REACTION_DIFFUSION_FULL_BUTTERFLY_H
#define SUMMIT_INTERFACE_REACTION_DIFFUSION_FULL_BUTTERFLY_H

#include "reaction_diffusion_interface_region.h"
#include "../elements/element_set_interface_two_sided.h"

namespace summit {

/**
 * Forward declarations
 */
class Checkpoint;

class Material;

class ReactionDiffusionInterfaceRegionFullButterfly : public ReactionDiffusionInterfaceRegion {
  public:
    /**
     * Typedefs
     */
    typedef ElementSet::field_t field_t;

    /**
     * Constructor
     * @param[in] es a pointer to a set of interface (DG) elements
     * @param[in] dof_node the number of degrees of freedom per node
     * @param[in] material interface material
     * @param[in] materialL material for the left element
     * @param[in] materialR material for the right element
     * @param[in] coordinates nodal coordinates
     */
    ReactionDiffusionInterfaceRegionFullButterfly(ElementSetInterfaceTwoSided const* es,
                             int dof_node,
                             InterfaceDG const& material,
                             ReactionDiffusionMaterial const& material_L,
                             ReactionDiffusionMaterial const& material_R,
                             NodalField<real> const& coordinates,
                             bool usingFullStabilizationTerm = false);

    /**
     * Constructor
     * @param[in] checkpoint object associated with restart
     * @param[in] name the name of the group within the checkpoint object
     */
    ReactionDiffusionInterfaceRegionFullButterfly(Checkpoint* checkpoint, const char* name);

    /**
     * Destructor
     */
    virtual ~ReactionDiffusionInterfaceRegionFullButterfly();

  private:
    /**
     * Default constructor
     */
    ReactionDiffusionInterfaceRegionFullButterfly();

    /**
     * Copy constructor
     * @param[in] a const reference to a ReactionDiffusionInterfaceRegionFullButterfly
     */
    ReactionDiffusionInterfaceRegionFullButterfly(ReactionDiffusionInterfaceRegionFullButterfly const&);

    /**
     * Assignment operator
     * @param[in] a const reference to a ReactionDiffusionInterfaceRegionFullButterfly
     * @return a reference to a ReactionDiffusionInterfaceRegionFullButterfly
     */
    ReactionDiffusionInterfaceRegionFullButterfly& operator=(ReactionDiffusionInterfaceRegionFullButterfly const&);

  public:
    /**
     * Method to assemble the Jacobi preconditioner. This method should eventually become purelly
     * virtual
     * @param[in] u nodal displacements
     * @param[in/out] jacobi the global nodal field in which the jacobi preconditioner matrix
     *                is written.
     * @param[in] dt the time that the load step took (default is set to 1 because that can be
     *                considered large for use with static problems)
     */
    virtual void AssembleJacobiPreconditioner(NodalField<real> const& u,
                                              NodalField<real>& jacobi,
                                              const real dt = 1.0) const;

    /**
     * Method to set all the bulk material parameters element by element in an inteface region
     * The implementation of this method should only provided for interface regions...
     * The design is not great and should be re-thought later
     * @param[in] sheet PropertySheet_t containing the material parameters
     * @param[in] commManager a CommunicationManager
     * @param[in] mesh a const reference to a mesh object essentially to allow lookups in the
     *            coboundary of any dim-1 elements.
     */
    virtual void SetMaterialParameters(PropertySheet_t const& sheet,
                                       CommunicationManager const& commManager,
                                       Mesh const& mesh);
    virtual IVsheet_t getSideInternalVariableFields(std::string const& name, SIDE side) const;
    virtual void setSideInternalVariableFields(Region::IVsheet_t const& ivField,
                                           std::string const& name, SIDE side);
    /**
     * Method to set a region's material parameter quadrature point by quadrature point using a
     * functor
     * @param[in] name material parameter name
     * @param[in] functor analytical function describing the material parameter
     * @param[in] commManager a CommunicationManager
     * @param[in] mesh a const reference to a mesh object essentially to allow lookups in the
     *            coboundary of any dim-1 elements.
     */
    virtual void SetMaterialParameters(std::string const& name,
                                       Functor<real> const& functor,
                                       CommunicationManager const& commManager,
                                       Mesh const& mesh);

    /**
     * Method to set a region's material parameter quadrature point by quadrature point using a
     * functor
     * @param[in] name material parameter name
     */
    virtual void SetMaterialParameters(std::string const& name);

    /**
     * method to get the global to local element index map
     * @param[in] dB_I an internal boundary where the internal variables should be set to a constant
     *            value
     * @param[out] globalToLocal map from the global to local element index
     */
    virtual void getGlobalToLocalElementMap(InternalBoundary const& dB_I,
                                            std::map<int, int>& globalToLocal) const;

    /**
     * Method to write for restart
     * @param[in] checkpoint object associated with restart
     * @param[in] name the name of the group within the checkpoint object
     * @param[in] tag the tag
     */
    virtual void WriteForRestart(Checkpoint* checkpoint,
                                 const char* name,
                                 const char* tag = nullptr) const;

  protected:
    /**
     * Method to perform an elementary constitutive step to compute average first Piola-Kirchhoff
     * stress and displacement jump
     * @param[in] e index of the element
     * @param[in] dt time step
     * @param[in] update boolean specifying whether the new stresses and the new internal
     *            variables should be saved
     * @param[in] ul local displacement vector
     * @param[in] u0l local displacement vector at the previous time-step
     * @param[out] TJ array of real containing
     *               - the average first Piola-Kirchoff stress projected onto the left normal
     *               - displacement jump per quadrature point per element
     *               - inradius for left and right elements
     *               - variables from the non-linear law at the interface
     *    Order [ T_gp1, uJump_gp1, inRadiusLeft_gp1, inRadiusRight_gp1, interfaceVariables_gp1,
     *            T_gp2, uJump_gp2, inRadiusLeft_gp2, inRadiusRight_gp2, interfaceVariables_gp2,
     *            ...]
     */
    virtual void ElementaryConstitutive(elem_t e,
                                        real dt,
                                        bool update,
                                        std::vector<real> const& ul,
                                        std::vector<real> const& u0l,
                                        real* TJ);


    /**
     * Method to interpolate from nodes to quadrature points
     * @param nodal_field, a NodalField of real
     * @param quad_field, a QuadratureField of real
     */
    virtual void Interpolate(NodalField<real> const& nodal_field,
                             ElementQuadratureField<real>& quad_field) const;

  protected:
    /**
     * Method to compute the elementary residual from average first Piola-Kirchhoff stress and
     * displacement jump
     * @param[in] e index of the element
     * @param[in] ul the current nodal displacements of the element e
     * @param[in] TJ_ElementaryResidual (only for one element) array of real containing:
     *               - the average first Piola-Kirchoff stress projected onto the left normal
     *                 (DG flux) per quadrature point: T_DG_gp
     *               - the displacement jump per quadrature point per element: uJump_gp
     *               - inradius for left and right elements
     *               - traction-separation-law (TSL: interface traction from cohesive law)
     *                 per quadrature point: T_TSL_gp
     *               - coefficient to activate the traction-separation-law (TSL) per quadrature
     *                 point: coeff_TSL_gp
     *               - coefficient to reactivate the contact under compression per quadrature
     *                 point: coeff_contact_gp
     *    Order [ T_DG_gp1, uJump_gp1, inRadiusLeft_gp1, inRadiusRight_gp1, T_TSL_gp1,
     *            coeff_TSL_gp1, coeff_contact_gp1, T_DG_gp2, uJump_gp2, inRadiusLeft_gp2,
     *            inRadiusRight_gp2, T_TSL_gp2, coeff_TSL_gp2, coeff_contact_gp2, ...]
     * @param[in,out] rl local residual vector
     */
    void ElementaryResidual(elem_t e,
                            const std::vector<real>& ul,
                            const real* TJ_ElementaryResidual,
                            std::vector<real>& rl,
                            const bool update = false) override;


    /**
     * Method to compute the contributtion of the quadrature point q to the elementary residual
     * from average first Piola-Kirchhoff stress and displacement jump
     * @param[in] e index of the element
     * @param[in] q index of the quadrature point
     * @param[in] alpha stabilization parameter
     * @param[in] jumpU jump in displacement at the quadrature point q
     * @param[in] left_traction average PK-I projected onto the left normal to the interface at
     *            the quadrature point q
     * @param[in] cohesive_traction traction computed with the interface cohesive material at
     *            the quadrature point q
     * @param[in] coefficients array with the coefficients coeff_TSL
     *            (activate the traction-separation-law)
     *            and coeff_contact (reactivate the contact under compression)
     * @param[out] rl local residual vector
     */
    virtual void QuadratureResidual(elem_t e,
                                    quad_t q,
                                    const std::vector<real>& ul,
                                    real alpha,
                                    std::vector<real> const& jumpU,
                                    std::vector<real> const& left_traction,
                                    std::vector<real> const& cohesive_traction,
                                    std::vector<real> const& coefficients,
                                    std::vector<real>& rl);

    /**
     * Method to compute the elementary stiffness matrix and residual vector
     * @param[in] e index of the element
     * @param[in] dt time step
     * @param[in] ul local displacement vector
     * @param[in] u0l local displacement vector at the previous time-step
     * @param[out] rl local residual vector
     * @param[out] Kl local stiffness matrix
     */
    void ElementaryStiffness(elem_t e,
                             real dt,
                             std::vector<real> const& ul,
                             std::vector<real> const& u0l,
                             std::vector<real>& rl,
                             std::vector<real>& Kl);

    /**
     * Method to compute the elementary stiffness matrix and residual vector
     * @param[in] e index of the element
     * @param[in] dt time step
     * @param[in] ul local displacement vector
     * @param[in] u0l local displacement vector at the previous time-step
     * @param[in] TJ_element (only for one element) array of real containing:
     *               - the average first Piola-Kirchoff stress projected onto the left normal
     *               - displacement jump per quadrature point per element
     *               - inradius for left and right elements
     *               - other variables computed from the non-linear law at the interface
     *    Order [ T_gp1, uJump_gp1, inRadiusRight_gp1, variablesComputedFromInterfaceLaw_gp1,
     *            T_gp2, uJump_gp2, inRadiusRight_gp2, variablesComputedFromInterfaceLaw_gp2, ...]
     * @param[out] rl local residual vector
     * @param[out] Kl local stiffness matrix
     */
    void ElementaryStiffness(elem_t e,
                             real dt,
                             std::vector<real> const& ul,
                             std::vector<real> const& u0l,
                             summit::ElementQuadratureField<real>& TJ_element,
                             std::vector<real>& rl,
                             std::vector<real>& Kl);

    /**
     * Method to do elementary update of the stesses and the internal variables
     * @param[in] e index of the element
     * @param[in] dt time step
     * @param[in] ul local displacement vector
     * @param[in] u0l local displacement vector at the previous time-step
     */
    virtual void  // GB: Like this I can use this code for my 3D-shell interface
    ElementaryPrepareUpdate(elem_t e,
                            real dt,
                            std::vector<real> const& ul,
                            std::vector<real> const& u0l,
                            ElementQuadratureField<real>* TJ);
    virtual void ElementaryComputeUpdate(elem_t e, ElementQuadratureField<real>* TJ, real dt);


    void ElementaryComputeUpdateSubIteration(elem_t e,
                                             ElementQuadratureField<real>* TJ,
                                             real dt) override;

    /**
     * Method to do elementary update a single internal variable
     * @param[in] e index of the element
     * @param[in,out] TJ the arrray with the values to exchange between the processors for this
     *                region
     * @param[in] dt time step
     * @param[in] name a string containing the name of the internal variable to update
     */
    virtual void ElementaryComputeUpdate(elem_t e,
                                         ElementQuadratureField<real>* TJ,
                                         real dt,
                                         std::string const& name);

    /**
     * Method to compute the right first Piola-Kirchhoff stress tensor and the right tangent
     * operator tensor from the constitutive update of the right element
     * @param[in] e index of the element
     * @param[in] q index of the quadarature point
     * @param[in] ul local displacement vector
     * @param[in] dt time-step
     * @param[out] P_R_new right first Piola-Kirchoff stress tensor
     * @param[out] F_R_new strains energetically conjugated to P_R_new
     * @param[out] internal_R_new right vector of internal variables
     * @param[out] C_R right tangent operator
     */
    virtual  // GB: Like this I can use this code for my 3D-shell interface
      void
      RightConstitutiveUpdate(elem_t e,
                              quad_t q,
                              std::vector<real> const& ul,
                              std::vector<real> const& u0l,
                              std::vector<real>& concentration0,
                              std::vector<real>& concentration,
                              real dt,
                              std::vector<real>& P_R_new,
                              std::vector<real>& f_R_new,
                              std::vector<real>& F_R_new,
                              std::vector<real>& internal_R_new,
                              real* C_R,
                              real* dPdU_R,
                              real* df_R);

    /**
     * Method to evaluate the failure criterion at a quadrature point of the interface for left
     * and right elements
     * @param[in] e index of the element
     * @param[in] q index of the quadarature point
     * @param[in] P_L_new left first Piola-Kirchoff stress tensor
     * @param[in] F_L_new strains energetically conjugated to P_L_new
     * @param[in] P_R_new right first Piola-Kirchoff stress tensor
     * @param[in] F_R_new strains energetically conjugated to P_R_new
     * @param[in] normalL normal to the left element
     * @param[out] interfaceMaterial_bufferForComm stores the information to decide if the cohesive
     *             law (traction separation law) has to be used or not
     */
    void InterfaceMaterialFailureCriterionEvaluation(
      elem_t e,
      quad_t q,
      std::vector<real>& P_L_new,
      std::vector<real>& F_L_new,
      std::vector<real>& P_R_new,
      std::vector<real>& F_R_new,
      const real* normalL,
      std::vector<real>& interfaceMaterial_bufferForComm);

    void _computeAveragedFULLStabilizationCoefficientRight();
    void _computeAveragedStabilizationCoefficientRight();

    /**
     * Method to store the quadrature data
     * @param[in] e index of the element
     * @param[in] q index of the quadarature point
     * @param[in] ul the local displacement vector of the element
     * @param[in] dt the time step
     * @param[in] P_L the first Piola-Kirchoff left stress tensor
     * @param[in] F_L the left strain tensor
     * @param[in] internal_L  vector of left internal variables
     * @param[in,out] stress_L storage container for left the stress
     * @param[in,out] stress_L storage container for left the strains
     * @param[in,out] internals_L storage container for the left internal variables
     * @param[in] P_R the first Piola-Kirchoff right stress tensor
     * @param[in] F_R right strain tensor
     * @param[in] internal_R  vector of right internal variables
     * @param[in,out] stress_R storage container for right the stress
     * @param[in,out] strain_R storage container for right the strains
     * @param[in,out] internals_R storage container for the right internal variables
     */
    virtual void StoreQuadraturePointsVariables(elem_t e,
                                                quad_t q,
                                                std::vector<real> const& ul,
                                                const real dt,
                                                std::vector<real> const& P_L,
                                                std::vector<real> const& F_L,
                                                std::vector<real> const& internal_data_L,
                                                ElementQuadratureField<real>* stress_L,
                                                ElementQuadratureField<real>* strain_L,
                                                ElementQuadratureField<real>* internals_L,
                                                std::vector<real> const& P_R,
                                                std::vector<real> const& F_R,
                                                std::vector<real> const& internal_data_R,
                                                ElementQuadratureField<real>* stress_R,
                                                ElementQuadratureField<real>* strain_R,
                                                ElementQuadratureField<real>* internals_R);

    /**
     * Method to compute the average traction (using the left normal)
     * @param[in] P_L_new left PK tensor
     * @param[in] P_R_new right PK tensor
     * @param[in] jac jacobian
     * @param[in] normalL left normal to the interface
     * @param[in,out] left_traction average (left) traction
     */
    virtual void AverageTraction(std::vector<real> const& P_L_new,
                                 std::vector<real> const& P_R_new,
                                 const real jac,
                                 const real* normalL,
                                 std::vector<real>& left_traction);

    /**
     * Accessor/mutator to the internal variable array, a purely virtual method in the base class
     * @return pointer to the internal variable field ElementQuadratureField *
     */
    virtual ElementQuadratureField<real>* internalVariables();

    // Attributes
  protected:
    /**
     * Reference to a material for the right element
     */
    ReactionDiffusionMaterial const& _material_R;

    /**
     * Stresses of the right element
     */
    ElementQuadratureField<real>* _stresses_R;

    /**
     * Stresses of the right element
     */
    ElementQuadratureField<real>* _F_R;

    /**
     * Strains of the right element
     */
    ElementQuadratureField<real>* _strains_R;

    /**
     * Internal variables for the behavior of the right element
     */
    ElementQuadratureField<real>* _internals_R;

    /**
     *  Right-hand part of stabilization coefficient for DG stabilization < C beta / h_s >
     *  contracted with the undeformed normals
     */
    ElementQuadratureField<real>* _avg_stab_coef_R;

    /**
     * Elastic tangent modulus which is the same for all the right element
     */
    ElementQuadratureField<real>* _CField_R;
    std::vector<real> _C_R;

    ElementQuadratureField<real>* _dPdUField_R;
    std::vector<real> _dPdU_R;

    ElementQuadratureField<real>* _dFField_R;
    std::vector<real> _dF_R;

    bulkTangent_t _bulkC_R;
    bulkTangent_t _bulkdPdU_R;
    bulkTangent_t _bulkdF_R;
    std::vector<real> _kappa_ref_R;



    /**
     * Compute or not the "right column" for 3D shell not computed
     */
    bool _computeRightPart;

  private:
    /**
     * Method to compute the contributtion of the quadrature point q to the elementary residual
     * from average first Piola-Kirchhoff stress and displacement jump
     * @param[in] e index of the element
     * @param[in] q index of the quadrature point
     * @param[in] alpha stabilization parameter
     * @param[in] beta_DG
     * @param[in] averg_hs_C
     * @param[in] jumpU jump in displacement at the quadrature point q
     * @param[in] left_traction average PK-I projected onto the left normal to the interface at
     *            the quadrature point q
     * @param[in] cohesive_traction traction computed with the interface cohesive material at
     *            the quadrature point q
     * @param[in] coefficients array with the coefficients coeff_TSL (activate the
     *            traction-separation-law) and coeff_contact (reactivate the contact under
     *            compression)
     * @param[in] normal vector containing the normal in the deformed configuration
     * @param[out] rl local residual vector
     */
    void _residualIntegrand(elem_t e,
                            quad_t q,
                            const std::vector<real>& ul,
                            const real* averg_hs_C,
                            const real* jumpU,
                            const real* dgTraction,
                            const real* cohesive_traction,
                            const real* coefficients,
                            const real* normal,
                            std::vector<real>& rl);

    void _uncrackedIntegrand(elem_t e,
                             quad_t q,
                             const std::vector<real>& ul,
                             const real* left_traction,
                             const real* jumpU,
                             real* interface_tangent,
                             const real* averg_hs_C,
                             std::vector<real>& rl,
                             std::vector<real>& Kl);

    void _crackedIntegrand(elem_t e,
                           quad_t q,
                           const real* cohesive_traction,
                           real* interface_tangent,
                           std::vector<real>& rl,
                           std::vector<real>& Kl);

  private:
    static Register<Region, ReactionDiffusionInterfaceRegionFullButterfly> reg;
};

}  // namespace summit

#endif  // SUMMIT_INTERFACE_REACTION_DIFFUSION_FULL_BUTTERFLY_H

// end of file
