// -*- C++ -*-
//
// summit development team
// massachusetts institute of technology
// (c) 2012-2013 all rights reserved
//

#include <vector>
#include <iostream>
#include <stdexcept>

#include "../fem/function_space.h"
#include "../fem/function_space_internal.h"
#include "../fem/nodal_field.h"
#include "../solvers/stiffness/stiffness.h"
#include "../elements/element_quadrature_field.h"

#include "../utils/math-util.h"
#include "../elements/element_set_bulk.h"

#include "../parallel/communication_manager.h"
#include "../parallel/communicator.h"

#include "reaction_diffusion_region.h"
#include "reaction_diffusion_weak_form.h"
#include "../io/summit_message.h"

#include "reaction_diffusion_interface_region.h"
#include "reaction_diffusion_interface_region_full_butterfly.h"



summit::ReactionDiffusionWeakForm::ReactionDiffusionWeakForm(
  FunctionSpace const& function_space,
  MaterialLibrary const& material_lib)
  : WeakForm(),
    _spatial_dim(function_space.dim()),
    _function_space(function_space),  // By default CG case and the time step factor will be 1
    _material_library(material_lib)
{
    // Check to make sure that the mesh has CG formulation
    //std::tuple<ElemType, FormulationType, int> typeFormulationOrder =
    //  (_function_space.mesh())->typeFormulationOrder(0);
    //if (std::get<1>(typeFormulationOrder) != FORMULATION_CG) {
    //    throw std::runtime_error(
    //      "ERROR in ReactionDiffusionWeakForm: only continuous Galerkin formulation is supported");
    //}

    // grab the element sets of the function space
    std::vector<ElementSetBody*> const& element_sets = _function_space.element_sets();
    // loop over the element sets
    for (size_t i = 0; i < element_sets.size(); ++i) {
        _regions.insert(element_sets[i]->newRegion(REACTION_DIFFUSION, material_lib,
                                                        _function_space.coordinates()));
    }

    _beginInterfaceRegion = this->FirstInterfaceRegion();
    _beginHalfButterflyRegion = this->FirstHalfButterflyRegion();

    // all done
    return;
}

summit::ReactionDiffusionWeakForm::~ReactionDiffusionWeakForm()
{
    // // loop over components of _VarForRes
    for (size_t i = 0; i < _VarForRes.size(); ++i) {
        // if memory has been allocated
        if (_VarForRes[i] != NULL) {
            // then delete it
            delete _VarForRes[i];
        }
        // simply to make sure that the pointer is indeed not pointing to any allocated space
        _VarForRes[i] = NULL;
    }


    // all done
    return;
}

void summit::ReactionDiffusionWeakForm::Residual(NodalField<real> const& T,
                                            NodalField<real> const& T0,
                                            real dt,
                                            bool update,
                                            NodalField<real>& residual,
                                            const CommunicationManager& commManager,
                                            const bool doIreduce)
{
    // compute the residual in the bulk regions
    for (RegionIterator it = this->beginBulk(); it != this->endBulk(); ++it) {
        // compute the residual in the current region
        (*it)->Residual(T, T0, dt, update, commManager, residual);
    }
    commManager.Reduce(residual);

    // varForRes
    std::vector<ElementQuadratureField<real>*> varForRes(_VarForRes.size(), NULL);
    size_t regId = 0;
    // if we update the regions
    if (update) {
        // then, let's loop over the regions
        for (RegionIterator it = this->beginInterface(); it != this->endInterface();
             ++it, ++regId) {
            // and grabe the pointer to variables needed to compute the residual
            varForRes[regId] = (*it)->VariablesForResidual();
        }
    }
    else {
        // then, let's loop over the regions
        for (RegionIterator it = this->beginInterface(); it != this->endInterface();
             ++it, ++regId) {
            // if no clone (at least the memory allocation...) of the variables needed to
            // compute
            // the residual have been made in the class
            if (_VarForRes[regId] == NULL) {
                // then, clone it
                _VarForRes[regId] = (*it)->CloneVariablesForResidual();
            }
            // then grab the pointer to the variables stored in the class
            varForRes[regId] = _VarForRes[regId];
        }
    }

    //  set the region index back to 0
    regId = 0;
    // loop over the regions
    for (RegionIterator it = this->beginInterface(); it != this->endInterface();
         ++it, ++regId) {
        (*it)->PrepareResidual(T, T0, dt, update, varForRes[regId]);
    }

    // reduce...
    commManager.Reduce(varForRes);

    //  set the region index back to 0
    regId = 0;
    // loop over the regions
    for (RegionIterator it = this->beginInterface(); it != this->endInterface();
         ++it, ++regId) {
        // compute the residual in the current region
        (*it)->ComputeResidual(T, varForRes[regId], residual, dt, update);
    }

    return;
}

void summit::ReactionDiffusionWeakForm::ComputeStiffness(NodalField<real> const& T,
                                                    NodalField<real> const& T0,
                                                    real dt,
                                                    Stiffness& stiffness,
                                                    NodalField<real>& residual,
                                                    const CommunicationManager& commManager)
{
    for (RegionIterator it = this->beginBulk(); it != this->endBulk(); ++it) {
        // compute the residual in the current region
        (*it)->ComputeStiffness(T, T0, dt, stiffness, residual);
    }
    if (this->beginInterface() != this->endInterface()) {
        // Part 2: compute the stiffness contribution from the FULL butterfly regions
        //---------------------------------------------------------------------------
        for (RegionIterator it = this->beginInterface(); it != this->beginHalfButterflyRegions();
             ++it) {
            // compute the stiffness matrix and the residual in the full butterfly region
            (*it)->ComputeStiffness(T, T0, dt, stiffness, residual);
        }
        if (this->beginHalfButterflyRegions() != this->endHalfButterflyRegions()) {
            // count for number of halfbutterfly interface regions
            int numHalfInterfaceReg = 0;
            for (RegionIterator it = this->beginHalfButterflyRegions();
                 it != this->endHalfButterflyRegions(); ++it) {
                ++numHalfInterfaceReg;
            }

            // declare a variable to hold the traction average, displacement jump, element sizes
            // (left, right) and nonlinear law for the interface
            // these informations are required for computing the residual, the traction and
            // displacement
            // need to be communicated between neighbors
            std::vector<ElementQuadratureField<real>*> TJ_elementsize_nonlinearLawVariables(
              numHalfInterfaceReg, NULL);

            // variable to store the shape and dshape function for the bulk element attached to the
            // interface element, this variable will be exchanged with the neighbor
            std::vector<ElementQuadratureField<real>*> shapeFunctionAndDeriv(numHalfInterfaceReg,
                                                                             NULL);

            // variable to store the Cijkl for the bulk element attached to the interface element
            // this varibale will be exchanged with the neighbor
            std::vector<ElementQuadratureField<real>*> Cijkl(numHalfInterfaceReg, NULL);

            // variable to store the Cijkl for the bulk element attached to the interface element
            // this varibale will be exchanged with the neighbor
            std::vector<ElementQuadratureField<real>*> dPdU(numHalfInterfaceReg, NULL);

            // declare a variable to hold the equation number for the missing wing of the
            // halfbutterfly
            // element for exchanging
            std::vector<ElementQuadratureField<int>*> halfButterflyMissingWingEQN(
              numHalfInterfaceReg, NULL);
            // declare a variable to hold the forces values for the missing wing of the
            // halfbutterfly
            // element for exchanging
            std::vector<ElementQuadratureField<real>*> halfButterflyMissingWingForces(
              numHalfInterfaceReg, NULL);

            // let's loop over the halfbutterfly interface regions and allocate the memory for
            // all the variables that will be communicated with the neighbor for computing stiffness
            // then prepare these variables
            size_t regId = 0;
            for (RegionIterator it = this->beginHalfButterflyRegions();
                 it != this->endHalfButterflyRegions(); ++it, ++regId) {
                // ask for the element set for this region the information about dim, number of
                // elemens, ...
                size_t dim = ((*it)->element_set())->dim();
                size_t nelem = ((*it)->element_set())->elements();
                size_t nen = ((*it)->element_set())->nodes_element();
                size_t nquads = ((*it)->element_set())->nquad();
                size_t dof_node = (*it)->dofsPerNode();

                // allocate memory for halfButterflyMissingWingEQN
                halfButterflyMissingWingEQN[regId] =
                  new ElementQuadratureField<int>(nelem, 1, nen * dof_node);
                // allocate memory for halfButterflyMissingWingForces
                halfButterflyMissingWingForces[regId] =
                  new ElementQuadratureField<real>(nelem, 1, nen * dof_node);

                // accessor to halfButterflyMissingWingEQN and halfButterflyMissingWingForces data
                int* halfButterflyMissingWingEQN_data = halfButterflyMissingWingEQN[regId]->data();
                real* halfButterflyMissingWingForces_data =
                  halfButterflyMissingWingForces[regId]->data();
                // fill up the halfButterflyMissingWingEqn by looping over all the element in the
                // element set
                for (size_t e = 0; e < nelem; ++e) {
                    const int* conn = ((*it)->element_set())->dof_map()->Connectivity(e);
                    for (size_t nod = 0; nod < nen; ++nod) {
                        // variable for nodeID
                        int nodeID = conn[nod];
                        // loop over the dimension
                        for (size_t i = 0; i < dof_node; ++i) {
                            halfButterflyMissingWingEQN_data[e * nen * dof_node + nod * dof_node +
                                                             i] =
                              stiffness.mapNodeToEqnNumber(nodeID, i);
                            halfButterflyMissingWingForces_data[e * nen * dof_node +
                                                                nod * dof_node + i] =
                              stiffness.getForces(nodeID, i);
                        }
                    }
                }

                // memory allocation for TJ_elementsize_nonlinearLawVariables is adopted from
                // Residual
                TJ_elementsize_nonlinearLawVariables[regId] = (*it)->CloneVariablesForResidual();

                // allocate memory for shapeFunctionAndDeriv of the dimension (nen) for shape
                // function
                // plus (nen*dim) for the derivative of the shape function
                shapeFunctionAndDeriv[regId] =
                  new ElementQuadratureField<summit::real>(nelem, nquads, nen + nen * dim);

                // allocate memory for she stiffness tensor Cijkl
                Cijkl[regId] = new ElementQuadratureField<summit::real>(
                  nelem, nquads, dim * dof_node * dim * dof_node);

                // allocate memory for the other stiffness tensor contributions
                dPdU[regId] = new ElementQuadratureField<summit::real>(
                  nelem, nquads, dim * dof_node * dof_node);

                // prepare information for the stiffness computation in the current region
                (dynamic_cast<summit::ReactionDiffusionInterfaceRegion*>(*it))
                  ->PrepareStiffness(T, T0, dt, TJ_elementsize_nonlinearLawVariables[regId],
                                     shapeFunctionAndDeriv[regId], Cijkl[regId], dPdU[regId]);
            }

            // reduce to communication traction and displacement between neighbors
            // Should it be communicated only traction and displacement, not the whole quadrature
            // field???
            // Just follow the similarity in computing residual, will see how will it go ...
            commManager.Reduce(TJ_elementsize_nonlinearLawVariables);

            // Exchange the shape funtion and stiffness Cijkl between the neighbor
            commManager.exchange(shapeFunctionAndDeriv);
            commManager.exchange(Cijkl);
            commManager.exchange(dPdU);
            // Exchange the halfButterflyMissingWingEQN and halfButterflyMissingWingForces
            // between the neighbor
            commManager.exchange(halfButterflyMissingWingEQN);
            commManager.exchange(halfButterflyMissingWingForces);

            //  set the region index back to 0 and  loop over the regions
            regId = 0;
            for (RegionIterator it = this->beginHalfButterflyRegions();
                 it != this->endHalfButterflyRegions(); ++it, ++regId) {
                // Ask for the halfbutterfly element SIDE
                summit::SIDE side =
                  (dynamic_cast<const summit::ElementSetInterfaceOneSided*>((*it)->element_set()))
                    ->side();
                // compute the stiffness in the current region
                (dynamic_cast<summit::ReactionDiffusionInterfaceRegion*>(*it))
                  ->ComputeStiffnessInterface(
                    T, T0, dt, *TJ_elementsize_nonlinearLawVariables[regId],
                    *shapeFunctionAndDeriv[regId], *Cijkl[regId], *dPdU[regId],
                    *halfButterflyMissingWingEQN[regId], *halfButterflyMissingWingForces[regId],
                    stiffness, residual, side);
            }

            // free memory
            for (int i = 0; i < numHalfInterfaceReg; ++i) {
                delete TJ_elementsize_nonlinearLawVariables[i];
                delete shapeFunctionAndDeriv[i];
                delete Cijkl[i];
                delete dPdU[i];
                delete halfButterflyMissingWingEQN[i];
                delete halfButterflyMissingWingForces[i];
            }
        }
    }

    // for (RegionIterator it = this->beginInterface(); it != this->beginHalfButterflyRegions();
    //          ++it) {
    //     // compute the stiffness matrix and the residual in the full butterfly region
    //     (*it)->ComputeStiffness(T, T0, dt, stiffness, residual);
    // }
    // all done
    return;
}

void summit::ReactionDiffusionWeakForm::ComputeStiffness(NodalField<real> const& T,
                                                    NodalField<real> const& T0,
                                                    real dt,
                                                    Stiffness& stiffness,
                                                    NodalField<real>& residual)
{
    for (RegionIterator it = _regions.begin(); it != _regions.end(); ++it) {
        // compute the residual in the current region
        (*it)->ComputeStiffness(T, T0, dt, stiffness, residual);
    }

    // all done
    return;
}

void summit::ReactionDiffusionWeakForm::Update(NodalField<real> const& T,
                                          NodalField<real> const& T0,
                                          real dt,
                                          const CommunicationManager& commManager)
{

    for (RegionIterator it = this->beginBulk(); it != this->endBulk(); ++it) {
        // update the current region
        (*it)->Update(T, T0, dt);
    }
    size_t regId = 0;
    std::vector<ElementQuadratureField<real>*> varForUpdate(_VarForRes.size(), NULL);
    for (RegionIterator it = this->beginInterface(); it != this->endInterface();
             ++it, ++regId) {
        // and grabe the pointer to variables needed to compute the residual
        varForUpdate[regId] = (*it)->VariablesForResidual();
        // update the current region
        (static_cast<commonInterfaceMechanicsRegion*>(*it))
          ->PrepareUpdate(T, T0, dt, varForUpdate[regId]);
    }

    // reduce...
    commManager.Reduce(varForUpdate);

    //  set the region index back to 0
    regId = 0;
    // loop over the regions
    for (RegionIterator it = this->beginInterface(); it != this->endInterface();
             ++it, ++regId) {
        // compute the residual in the current region
        (static_cast<commonInterfaceMechanicsRegion*>(*it))
          ->ComputeUpdate(varForUpdate[regId], dt);
    }

    // all done
    return;
}

summit::real summit::ReactionDiffusionWeakForm::StableTimeStep(NodalField<real> const& coordinates,
                                                          NodalField<real> const& T,
                                                          const CommunicationManager& commManager)
{
    // set the minimum time step to a huge value
    real rank_min_dt = SUMMIT_REAL_MAX;
    // loop over the regions
    for (RegionIterator it = _regions.begin(); it != _regions.end(); ++it) {
        // compute the smallest time-step in the current region
        real dt = (*it)->StableTimeStep(coordinates, T);
        // keep it if it is smaller than the current minimum time-step
        rank_min_dt = MathUtil<real>::min(rank_min_dt, dt);
    }

    // reduce
    real min_dt = commManager.ReduceMinimum(rank_min_dt);

    // return the smallest time-step
    return min_dt;
}

summit::real summit::ReactionDiffusionWeakForm::StableTimeStep(NodalField<real> const& coordinates,
                                                          NodalField<real> const& Tbulk,
                                                          NodalField<real> const& Tcrack,
                                                          const CommunicationManager& commManager)
{
    // set the minimum time step to a huge value
    real rank_min_dt = SUMMIT_REAL_MAX;
    // loop over the regions
    for (RegionIterator it = _regions.begin(); it != _regions.end(); ++it) {
        // compute the smallest time-step in the current region
        real dt = (*it)->StableTimeStep(coordinates, Tbulk);
        // keep it if it is smaller than the current minimum time-step
        rank_min_dt = MathUtil<real>::min(rank_min_dt, dt);
    }

    // reduce
    real min_dt = commManager.ReduceMinimum(rank_min_dt);

    // return the smallest time-step
    return min_dt;
}

void summit::ReactionDiffusionWeakForm::AssembleMass(NodalField<real>& mass,
                                                const CommunicationManager& commManager,
                                                const bool doIreduce)
{
    // loop over the regions
    for (RegionIterator it = _regions.begin(); it != _regions.end(); ++it) {
        // Assemble the mass in the current region
        (*it)->AssembleMass(mass);
    }

    // reduce...
    if(doIreduce){
        commManager.Reduce(mass);
    }
    // all done
    return;
}

void summit::ReactionDiffusionWeakForm::Interpolate(const NodalField<real>& nodal_field,
                                               ElementQuadratureField<real>& quad_field) const
{
    std::cout << "ERROR in "
              << "summit::ReactionDiffusionWeakForm::Interpolate()"
              << "   --> this method has not been implemented yet" << std::endl;
    exit(1);

    // end of method
    return;
}

// bool summit::ReactionDiffusionWeakForm::GetNodalField(std::string const& name,
//                                                  CommunicationManager const& commManager,
//                                                  int numNode,
//                                                  NodalField<real>& nodal_field) const
// {
//     // bool
//     bool found;

//     if (name == "opening") {
//         // create a nodal field of the weights
//         NodalField<real> weights(1, numNode);
//         // not sure if needed...
//         std::fill(weights.begin(), weights.end(), 0.0);

//         nodal_field.resize(1, numNode);
//         // not sure if needed...
//         std::fill(nodal_field.begin(), nodal_field.end(), 0.0);

//         // reduce...
//         commManager.Reduce(nodal_field);
//         commManager.Reduce(weights);

//         // scale by computed weighting (which can be zero due to elements with a visualization
//         // boolean set to false)
//         // this must be done at this level
//         // since element sets may overlap
//         for (int i = 0; i < nodal_field.nodes(); ++i) {
//             if (weights(i, 0) > 0) {
//                 nodal_field(i, 0) /= weights(i, 0);
//             }
//         }

//         // end of method
//         return true;
//     }
//     // create a nodal field of the weights
//     NodalField<real> weights(1, numNode);
//     // not sure if needed...
//     std::fill(weights.begin(), weights.end(), 0.0);

//     // loop over the regions
//     for (std::set<Region*>::iterator it = _regions.begin(); it != _regions.end(); ++it) {
//         found = (*it)->GetNodalField(name, numNode, weights, nodal_field);
//         if (!found) {
//             return found;
//         }
//     }

//     // reduce...
//     // commManager.Reduce(nodal_field);
//     // commManager.Reduce(weights);

//     // // scale by computed weighting (which can be zero due to elements with a visualization boolean
//     // // set to false)
//     // // this must be done at this level
//     // // since element sets may overlap
//     // for (int i = 0; i < nodal_field.nodes(); ++i) {
//     //     for (int j = 0; j < nodal_field.dim(); ++j) {
//     //         if (weights(i, 0) > 0) {
//     //             nodal_field(i, j) /= weights(i, 0);
//     //         }
//     //     }
//     // }

//     // end of method
//     return found;
// }

bool summit::ReactionDiffusionWeakForm::GetNodalField(std::string const& name,
                                     CommunicationManager const& commManager,
                                     int numNode,
                                     NodalField<real>& nodal_field) const
{

    // NOTE: THIS METHOD CHERRYPICKED FROM DANIEL AND CHRIS'S VERSIONS OF THE FILE. BEWARE!
    // bool
    bool found;

    // create a nodal field of the weights
    NodalField<real> weights(1, numNode);
    std::fill(weights.begin(), weights.end(), 0.0);

    // loop over the bulk regions
    for (RegionIterator it = beginBulk(); it != endBulk(); ++it) {
        found = (*it)->GetNodalField(name, numNode, weights, nodal_field);
        if (!found)
            return found;
    }

    if (name == "internal") {
        // get maximum dimension of the nodal field among all processors
        // the size of the internals depends on which interface material the
        // region is made of. In case of heterogeneities, we need to resize the
        // nodal field according to the maximum dimension, or the reduce will fail!
        size_t dim = commManager.ReduceMaximum(nodal_field.dim());
        nodal_field.resize(dim, nodal_field.nodes());
    } else if (name == "opening") {
        nodal_field.resize(1, numNode);
        std::fill(nodal_field.begin(), nodal_field.end(), 0.0);
    }
    
    // reduce...
    commManager.Reduce(nodal_field);
    commManager.Reduce(weights);

    // scale by computed weighting (which can be zero due to elements with a visualization boolean
    // set to false)
    // this must be done at this level
    // since element sets may overlap
    for (int i = 0; i < nodal_field.nodes(); ++i) {
        for (int j = 0; j < nodal_field.dim(); ++j) {
            if (weights(i, 0) > 0) {
                nodal_field(i, j) /= weights(i, 0);
            }
        }
    }
    
    return found;
}

void summit::ReactionDiffusionWeakForm::ComputeAccelerationWithBlockMassMatrix(
  NodalField<real> const& residual,
  NodalField<real>& acceleration)
{
    // compute the acceleration in the bulk regions
    for (RegionIterator it = this->beginBulk(); it != this->endBulk(); ++it) {
        // compute the acceleration in the current region
        // the below used to be a static_cast, and is now a dynamic_cast based on discussions with the group
        // future work may convert this to a dynamic_cast_or_fail or a dynamic_cast_or_continue implementation if desired
        dynamic_cast<summit::ReactionDiffusionRegion*>(*it)->ApplyMassMatrixInverse(residual, acceleration);
    }
    // all done
    return;
}

void summit::ReactionDiffusionWeakForm::ComputeAccelerationWithBlockMassMatrix(
      NodalField<real> const& residual,
      NodalField<int> const& boundaryConditionType,
      NodalField<real>& acceleration,
      const CommunicationManager& commManager,
      const real gammaDt)
{
    // compute the acceleration in the bulk regions
    // for (RegionIterator it = this->beginBulk(); it != this->endBulk(); ++it) {
    //     // compute the acceleration in the current region
    //     (*it)->ComputeAccelerationWithBlockMassMatrix(residual, boundaryConditionType, acceleration,
    //                                                   commManager, gammaDt);
    // }

    // all done
    return;
}

size_t summit::ReactionDiffusionWeakForm::numberNodalUnknowns() const
{
    int ndofs = -1;
    for (RegionConstIterator it = _regions.begin(); it != _regions.end(); ++it) {
        int rdofs = (*it)->dofsPerNode();
        if (ndofs == -1) {
            ndofs = rdofs;
        }
        if (ndofs != rdofs) {
            Message::Fatal(
              "Summit can only solve problems involving the same number of dofs inside a same "
              "WeakForm");
        }
    }
    // all done
    return (size_t)ndofs;
}

size_t summit::ReactionDiffusionWeakForm::numberUnknownsEntities() const
{
    // all done
    return _function_space.nodes();
}

std::vector<summit::Region::IVsheet_t>
summit::ReactionDiffusionWeakForm::getInterfaceInternalVariableFields(std::string const& name) const
{
    // returned vector of IVsheet_t
    std::vector<Region::IVsheet_t> sheetVec;
    // loop over the interface region
    for (std::set<Region*>::iterator it = _regions.begin(); it != _regions.end(); ++it) {
    //for (std::vector<summit::Region*>::const_iterator it = _regions.begin();
         //it != _regions.end(); ++it) {
        // append vector with IVsheet_t of current interface region
        sheetVec.push_back((*it)->getInternalVariableFields(name));
    }

    // all done
    return sheetVec;
}

std::vector<summit::Region::IVsheet_t> summit::ReactionDiffusionWeakForm::getInterfaceSideInternalVariableFields(
  std::string const& name, SIDE side) const
{
    // returned vector of IVsheet_t
    std::vector<Region::IVsheet_t> sheetVec;
    // loop over the interface region
    for (RegionIterator rIt = beginInterface(); rIt != endInterface(); ++rIt) {
        // append vector with IVsheet_t of current interface region
        sheetVec.push_back((*rIt)->getSideInternalVariableFields(name, side));
        
    }

    // all done
    return sheetVec;
}

void summit::ReactionDiffusionWeakForm::setInterfaceSideInternalVariableFields(std::vector<Region::IVsheet_t> const& ivField,
  std::string const& name, SIDE side)
  //,std::vector<transferMap_t> const& transMaps)
  {
    // region counter
    int regionCounter = 0;
    // loop over the interface region
    for (RegionIterator rIt = beginInterface(); rIt != endInterface(); ++rIt, ++regionCounter) {
        // set the internal variable value of the current interface mechanics region
        (*rIt)->setSideInternalVariableFields(ivField[regionCounter], name, side);//, transMaps[regionCounter]);
    }
  }

void summit::ReactionDiffusionWeakForm::extendContainers()
{
    Message::Fatal(
      "The extendContainers method have to be implemented for summit::ReactionDiffusionWeakForm");

    // end of method
    return;
}

void summit::ReactionDiffusionWeakForm::InterpolateAndSetPrimalVariableAsInternalVariable(
  NodalField<real> const& u)
{
    std::cout << "ERROR in "
              << "summit::ReactionDiffusionWeakForm::InterpolateAndSetPrimalVariableAsInternalVariable()"
              << "   --> this method has not been implemented yet" << std::endl;
    exit(1);

    // end of method
    return;
}

void summit::ReactionDiffusionWeakForm::SetMaterialParameters(std::string const& name,
                                                         Functor<real> const& functor,
                                                         CommunicationManager const& commManager)
{
    // loop over regions
    for (RegionIterator it = _regions.begin(); it != _regions.end(); ++it) {
        // set region's "name" property quadrature point by quadrature point using a functor
        (*it)->SetMaterialParameters(name, functor, commManager, _getMesh());
    }

    // all done
    return;
}

summit::Mesh const& summit::ReactionDiffusionWeakForm::_getMesh()
{
    // all done
    return *(_function_space.mesh());
}

summit::real summit::ReactionDiffusionWeakForm::Energy()
{
    std::cout << "ERROR in "
              << "summit::ReactionDiffusionWeakForm::Energy()"
              << "   --> this method has not been implemented yet" << std::endl;
    exit(1);

    // all done
    return 0;
}
// end of file
