#include <iostream>
#include <iomanip>
#include <cstring>
#include <fstream>
#include <stdexcept>

#include "../elements/element_set_interface_one_sided.h"
#include "../materials/material.h"
#include "../utils/util.h"

#include "../parallel/communication_manager.h"
#include "../parallel/communicator.h"

#include "reaction_diffusion_interface_region.h"
#include "../solvers/stiffness/stiffness.h"

#include "../restart/Group.h"

// to include the compatibility term in the calculations
#define IMR_COMPATIBILITY 1

// to save the data for fracture mode I
//#define SAVE_DATAFRACTURE_MODEI 1


summit::ReactionDiffusionInterfaceRegion::ReactionDiffusionInterfaceRegion(ElementSetInterfaceOneSided const* es,
                                                           int dof_node,
                                                           Material const& material,
                                                           ReactionDiffusionMaterial const& material_L,
                                                           NodalField<real> const& coordinates,
                                                           bool usingFullStabilizationTerm)
  :  // call the base class constructor
    commonInterfaceMechanicsRegion(es, dof_node, coordinates, usingFullStabilizationTerm),
    // interface material
    _material(*(static_cast<const InterfaceDG*>(&material))),
    // left material
    _material_L(material_L),
    // left stress
    _stresses_L(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(), _element_set->dim() * _dof_node)),
    _F_L(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(),  _element_set->dim() * _dof_node)),
    // left strain
    _strains_L(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(), _element_set->dim() * _dof_node)),
    // left internal variables
    _internals_L(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(), _material_L.nInt())),
    // avg internal variables
    _internals_avg(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(), _material_L.nInt())),
    // Elastic tangent modulus
    _CField_L(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(), _strains_L->dim() * _strains_L->dim())),
    // for convective terms we store the derivative with respect to the primal as well (DANIEL)
    _dPdUField_L(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(), _strains_L->dim() * dof_node)),
    _dFField_L(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(), _strains_L->dim() * dof_node)),
    //_dPdU_L(_strains_L->dim() * dof_node),
    // left tangent modulus
    _C_L(_strains_L->dim() * _strains_L->dim()),
    _dPdU_L(_strains_L->dim() * dof_node),
    _dF_L(_strains_L->dim() * dof_node),
    _bulkC_L(_element_set->side(), _CField_L),
    _bulkdPdU_L(_element_set->side(), _dPdUField_L),
    _bulkdF_L(_element_set->side(), _dFField_L),
    _kappa_ref_L(_strains_L->dim() * _strains_L->dim())
{
    // interface internal variables
    _internals = new ElementQuadratureField<real>(_element_set->elements(), _element_set->nquad(),
                                                  _material.nInt());

    if (_usingFullStabilizationTerm) {
        _avg_stab_coef_L = new ElementQuadratureField<real>(
          _element_set->elements(), _element_set->nquad(), _dof_node * _dof_node);
    }
    else {
        _avg_stab_coef_L =
          new ElementQuadratureField<real>(_element_set->elements(), _element_set->nquad(), _dof_node);
    }

    // We have to consider:
    //  --> left_traction.size()       : _dof_node
    //  --> JumpU.size()               : _dof_node
    //  inradius_left                  : 1
    //  inradius_right                 : 1
    //  --> interfaceMaterial.size()   : _material.materialBufferSizeForComm(_element_set->dim())
    //  --> _avg_stab_coef_L
    _bufferSizeForComm = _dof_node + _dof_node + 2 +
                         _material.materialBufferSizeForComm(_element_set->dim()) +
                         _avg_stab_coef_L->dim();

    // We have to consider:
    //  --> left_traction.size()         : _dof_node
    //  --> JumpU.size()                 : _dof_node
    //  inradius_left                    : 1
    //  inradius_right                   : 1
    //  --> cohesiveTraction.size()      : _dof_node
    //  coefficient to activate contact  : 1
    //  coefficient to activate TSL      : 1
    //  --> normalDeformedLeft.size()    : _element_set->dim()
    //  --> _avg_stab_coef_L
    _dimForTJElementaryResidual = 3 * _dof_node + 2 + 2 + _avg_stab_coef_L->dim() + _element_set->dim();

    // bundle of interface tractions and displacement jumps per quad point per elem
    _TJ = new ElementQuadratureField<real>(_element_set->elements(), _element_set->nquad(),
                                           _bufferSizeForComm);

    // register the field containing tractions and jumps
    RegisterField("tractions and jumps", _TJ);

    // register the internal variables for the interface behavior as a field
    RegisterField("internals", _internals);

    // register the left stresses as a field
    RegisterField("stresses_L", _stresses_L);
    RegisterField("f_L", _F_L);


    // register the left strains as a field
    RegisterField("strains_L", _strains_L);

    // register the left internal variables as a field
    RegisterField("internals_L", _internals_L);

    // Initialize the internals variables
    for (elem_t e(0); e < _element_set->elements(); ++e) {
        for (quad_t q(0); q < _element_set->nquad(); ++q) {
            _material_L.InitInternal(_internals_L->local(e, q));
            _material.InitInternal(_internals->local(e, q));
        }
    }

    // register the avg internal variables as a field
    RegisterField("internals_avg", _internals_avg);

    // register the Elastic tangent modulus as a field
    RegisterField("CField_L", _CField_L);
    RegisterField("dPdUField_L", _dPdUField_L);
    RegisterField("dfField_L", _dFField_L);
    // compute the elastic left tangent modulus
    std::vector<real> P_L_new(_strains_L->dim());
    std::vector<real> F_L_new(_strains_L->dim());
    std::vector<real> f_L_new(_F_L->dim());

    std::vector<real> concentration_L(_dof_node);
    std::vector<real> concentration_L0(_dof_node);
    std::vector<real> internals_L_new(_internals_L->dim());
    std::vector<real> ul(_element_set->nodes_element() * _dof_node);
    LeftConstitutiveUpdate(elem_t(0), quad_t(0), ul, ul, concentration_L0, concentration_L, 0.0, P_L_new, f_L_new, F_L_new, internals_L_new,
                           _C_L.data(), _dPdU_L.data(), _dF_L.data());
    for (int i=0; i < _CField_L->dim(); ++i) {
        //setup the reference conductivities
        _kappa_ref_L[i] = _C_L[i];
        // std::cout << "_kappa_ref_L[" << i << "]: " << _kappa_ref_L[i] <<std::endl;
    }
    for (elem_t e(0); e < _element_set->elements(); ++e) {
        for (quad_t q(0); q < _element_set->nquad(); ++q) {
            for (size_t i = 0; i < _CField_L->dim(); ++i) {
                _CField_L->local(e, q)[i] = _C_L[i];
            }
            for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
                _dPdUField_L->local(e, q)[i] = _dPdU_L[i] + _dF_L[i];
            }
            for (size_t i = 0; i < _dFField_L->dim(); ++i) {
                _dFField_L->local(e, q)[i] = _dF_L[i];
            }
        }
    }

    if (_usingFullStabilizationTerm)
        _computeAveragedFULLStabilizationCoefficientLeft();
    else
        _computeAveragedStabilizationCoefficientLeft();

    // Choose the sign of the some contributions to the residual or the stiffness according
    // to the side of the element set.
    // If the methods of this class are used to compute the contributions, it means that the
    // element set if a half butterfly (processor boundary), so the side is either LEFT or RIGHT
    // and cannot be INTERFACE. However, since the InterfaceMechanicsRegion inherits from
    // ReactionDiffusionInterfaceRegion, there might be the case that _element_set->side() = INTERFACE
    // In this case, to be consistent with the return value of ElementSetInterfaceOneSided::side(),
    // which returns LEFT if the side is INTERFACE, the convention is made that the contributions
    // are implemented as if I was LEFT, namely _amIRight = -1.0.
    if (_element_set->side() == summit::RIGHT)
        _amIRight = 1.0;
    else
        _amIRight = -1.0;

    // end of method
    return;
}

void summit::ReactionDiffusionInterfaceRegion::_computeAveragedFULLStabilizationCoefficientLeft()
{
    // compute the stability parameter
    real beta_DG = _material.StabilityParameter(InterfaceDG::DEFAULT);

    // spatial dimension
    size_t spatial_dim = _element_set->dim();

    for (elem_t e(0); e < _element_set->elements(); ++e) {
        for (quad_t q(0); q < _element_set->nquad(); ++q) {
            for (size_t i(0); i < _dof_node; ++i) {
                for (size_t j(0); j < _dof_node; ++j) {
                    for (dim_t J(0); J < spatial_dim; ++J) {
                        size_t iJ = spatial_dim * i + J;
                        for (dim_t L(0); L < spatial_dim; ++L) {
                            size_t jL = spatial_dim * j + L;
                            size_t iJjL = spatial_dim * spatial_dim * iJ + jL;
                            (_avg_stab_coef_L->local(e, q))[_dof_node * i + j] +=
                              0.5 * (_bulkC_L.second)->local(e, q)[iJjL] * beta_DG *
                              _element_set->normalL(e, q, J) * _element_set->normalL(e, q, L);
                        }
                    }
                }
            }
        }
    }

    return;
}


void summit::ReactionDiffusionInterfaceRegion::_computeAveragedStabilizationCoefficientLeft()
{
    // compute the stability parameter
    std::vector<real> my_betas(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getBetasForDGStability(my_betas.data());
    for (size_t i(0); i < _dof_node; ++i) {
        for (elem_t e(0); e < _element_set->elements(); ++e) {
            for (quad_t q(0); q < _element_set->nquad(); ++q) {
                const real* qL = _internals_L->local(e, q);
                _avg_stab_coef_L->local(e, q)[i] = 0.5 * _kappa_ref_L[i *_element_set->dim() + i * _dof_node * _element_set->dim() * _element_set->dim()] * my_betas[i];
            }
        }
    }

    return;
}

summit::ReactionDiffusionInterfaceRegion::ReactionDiffusionInterfaceRegion(Checkpoint* checkpoint, const char* name)
  : commonInterfaceMechanicsRegion(checkpoint, name),
    _material(*GET_CONST_REFERENCE(summit::InterfaceDG,
                                   checkpoint->location()->tag(std::string("material")))),
    _material_L(*GET_CONST_REFERENCE(summit::ReactionDiffusionMaterial,
                                     checkpoint->location()->tag(std::string("material_L")))),
    _TJ(new ElementQuadratureField<real>(checkpoint, "TJ")),
    _stresses_L(new ElementQuadratureField<real>(checkpoint, "stresses_L")),
    _F_L(new ElementQuadratureField<real>(checkpoint, "f_L")),
    _strains_L(new ElementQuadratureField<real>(checkpoint, "strains_L")),
    _internals_L(new ElementQuadratureField<real>(checkpoint, "internals_L")),
    _internals_avg(new ElementQuadratureField<real>(checkpoint, "internals_avg")),
    _avg_stab_coef_L(new ElementQuadratureField<real>(checkpoint, "avg_stab_coef_L")),
    _CField_L(new ElementQuadratureField<real>(checkpoint, "CField_L")),
    _dPdUField_L(new ElementQuadratureField<real>(checkpoint, "dPdUField_L")),
    _C_L(_strains_L->dim() * _strains_L->dim()),
    _bulkC_L(_element_set->side(), _CField_L),
    _bulkdPdU_L(_element_set->side(), _dPdUField_L),
    _bulkdF_L(_element_set->side(), _dFField_L),
    _dPdU_L(_dPdUField_L->dim()),
    _dF_L(_dFField_L->dim())
{
    _internals = new ElementQuadratureField<real>(checkpoint, "internals");

    DataSet* ds = checkpoint->OpenDataSet("C_L");
    assert(ds->dims()[0] == (int)_C_L.size());
    ds->read(_C_L.data());

    // Read _bufferSizeForComm
    ds = checkpoint->OpenDataSet("bufferSizeForComm");
    int bufferSizeForCommInt;
    ds->read(&bufferSizeForCommInt);
    _bufferSizeForComm = bufferSizeForCommInt;

    // Read _dimForTJElementaryResidual
    ds = checkpoint->OpenDataSet("dimForTJElementaryResidual");
    int dimForTJElementaryResidualInt;
    ds->read(&dimForTJElementaryResidualInt);
    _dimForTJElementaryResidual = dimForTJElementaryResidualInt;

    ds = checkpoint->OpenDataSet("amIRight");
    ds->read(&_amIRight);

    // register the fields
    RegisterField("tractions and jumps", _TJ);
    RegisterField("internals", _internals);
    RegisterField("stresses_L", _stresses_L);
    RegisterField("f_L", _F_L);
    RegisterField("strains_L", _strains_L);
    RegisterField("internals_L", _internals_L);
    RegisterField("internals_avg", _internals_avg);
    RegisterField("CField_L", _CField_L);
    RegisterField("dPdUField_L", _dPdUField_L);
    return;
}

summit::ReactionDiffusionInterfaceRegion::~ReactionDiffusionInterfaceRegion()
{
    // delete the pointer to the element quadrature field for left stresses
    delete _stresses_L;
    delete _F_L;
    // delete the pointer to the element quadrature field for left strains
    delete _strains_L;
    // delete the pointer to the element quadrature field for left internal variables
    delete _internals_L;
    // delete the pointer to the element quadrature field for interface internal variables
    delete _internals;
    // delete TJ
    delete _TJ;
    // delete the pointer to the element quadrature field for avg internal variables
    delete _internals_avg;
    // delete the pointer to the element quadrature field for the left Elastic tangent modulus
    delete _CField_L;
    delete _dPdUField_L;
    delete _dFField_L;
    delete _avg_stab_coef_L;

    // end of method
    return;
}

void summit::ReactionDiffusionInterfaceRegion::AssembleJacobiPreconditioner(NodalField<real> const& u,
                                                                    NodalField<real>& jacobi,
                                                                    const real dt) const
{
    // nothing for now. Just checking that we actually need something here...
    return;
}

summit::ElementQuadratureField<summit::real>*
summit::ReactionDiffusionInterfaceRegion::CloneVariablesForResidual() const
{
    return _TJ->clone_storage();
}

summit::ElementQuadratureField<summit::real>*
summit::ReactionDiffusionInterfaceRegion::VariablesForResidual()
{
    return _TJ;
}

size_t summit::ReactionDiffusionInterfaceRegion::bufferSizeForComm() const { return _bufferSizeForComm; }

size_t summit::ReactionDiffusionInterfaceRegion::ComputeDimensionForTJElementaryResidual() const
{
    return _dimForTJElementaryResidual;
}

void summit::ReactionDiffusionInterfaceRegion::ElementaryConstitutive(elem_t e,
                                                              real dt,
                                                              bool update,
                                                              std::vector<real> const& ul,
                                                              std::vector<real> const& u0l,
                                                              real* TJ)
{
    //std::cout << "I am in ReactionDiffusionInterfaceRegion::ElementaryConstitutive. Update is: " << update <<std::endl;
    // strain dimension
    int const strain_dim = _strains_L->dim();
    // dimension of left and right internal variables
    size_t const internal_L_dim = _material_L.nInt();

    // spatial dimension
    size_t const spatial_dim = _element_set->dim();

    // memory allocation ...
    // ... for the left part
   std::vector<real> P_L_new;
    P_L_new.resize(strain_dim);

   std::vector<real> f_L_new;
    f_L_new.resize(strain_dim);
    // memory allocation
   std::vector<real> F_L_new;
    F_L_new.resize(spatial_dim * _dof_node);
   std::vector<real> internal_L_new;
    internal_L_new.resize(internal_L_dim);


    std::vector<real> concentration_L(_dof_node);
    std::vector<real> concentration_L0(_dof_node);

    // ... for the jump in displacement
   std::vector<real> jumpU;
    jumpU.resize(_dof_node);
    // ... for the left traction
   std::vector<real> left_traction;
    left_traction.resize(_dof_node);
    // ... for the interface material
   std::vector<real> interfaceMaterial_bufferForComm;
    interfaceMaterial_bufferForComm.resize(_material.materialBufferSizeForComm(spatial_dim));

    // calculation of the characteristic length (inradius) of the left and right elements
    // resize vector to store the nodal coordinates (left and right sides) of the interface
    // element e
   std::vector<real> xl;
    xl.resize(_element_set->nodes_element() * _element_set->dim());
    //    localize the coordintes
    _element_set->Localize(_coordinates, e, xl);
    //    characteristic lengths of left and right elements
    real measInterface = _element_set->InRadiusElement(&xl[0]);
    real inRadius_left = 0.0;
    if (_element_set->side() == summit::LEFT) inRadius_left = measInterface;
    real inRadius_right = 0.0;
    if (_element_set->side() == summit::RIGHT) inRadius_right = measInterface;

    // integrate over quadrature points
    for (quad_t q(0); q < _element_set->nquad(); ++q) {
        // constitutive update of the left element
	    for (size_t i = 0; i < _CField_L->dim(); ++i) {
            _CField_L->local(e, q)[i] = 0.0;
            _C_L[i] = 0.0;
        }
        for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
            _dPdUField_L->local(e, q)[i] = 0.0;
            _dPdU_L[i] = 0.0;
        }
	    for (size_t i = 0; i < _dFField_L->dim(); ++i) {
            _dFField_L->local(e, q)[i] = 0.0;
	        _dF_L[i] = 0.0;
        }
        LeftConstitutiveUpdate(e, q, ul, u0l, concentration_L0, concentration_L, dt, P_L_new, f_L_new, F_L_new, internal_L_new, _C_L.data(), _dPdU_L.data(), _dF_L.data());
        for (size_t i = 0; i < _CField_L->dim(); ++i) {
            _CField_L->local(e, q)[i] = _C_L[i];
        }
        for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
            _dPdUField_L->local(e, q)[i] = _dPdU_L[i] + _dF_L[i];
        }
        for (size_t i = 0; i < _dFField_L->dim(); ++i) {
            _dFField_L->local(e, q)[i] = _dF_L[i];
        }

        // jacobians (with quadrature weights)
        real jac = _element_set->jac(e, q);

        // reference to the left normal
        const real* normalL = _element_set->normalL(e, q);

        // compute the displacement jump at the current quad point
        DisplacementJump(e, q, jac, ul, jumpU);

        // AverageTraction(P_L_new,P_R_new,jac,normalL,left_traction);
        std::fill(left_traction.begin(), left_traction.end(), 0.);

        // project the average PK-I onto the normal to get the left traction
        for (size_t i = 0; i < _dof_node; ++i) {
            for (dim_t j(0); j < spatial_dim; ++j) {
                size_t ij = spatial_dim * i + j;
                left_traction[i] += (0.5 * jac * (P_L_new[ij] + f_L_new[ij] )) * normalL[j];
            }
        }

        // evaluate the failure criterion at the interface for left or right element
        //   vector interfaceMaterial_bufferForComm stores the information to decide if the cohesive
        //   law (traction separation law) has to be used or not.
        InterfaceMaterialFailureCriterionEvaluationOneSide(
          e, q, P_L_new, F_L_new, normalL, _element_set->side(), interfaceMaterial_bufferForComm);

        // copy to global array
        std::copy(left_traction.begin(), left_traction.end(), TJ);  // left traction
        std::copy(jumpU.begin(), jumpU.end(), TJ + _dof_node);      // displacement jump
        TJ[2 * _dof_node] = inRadius_left;                          // h_left
        TJ[2 * _dof_node + 1] = inRadius_right;                     // h_right
        std::copy(interfaceMaterial_bufferForComm.begin(), interfaceMaterial_bufferForComm.end(),
                  TJ + 2 * _dof_node + 2);  // buffer from interface material

        for (size_t i = 0; i < _avg_stab_coef_L->dim(); ++i) {
            TJ[2 * _dof_node + 2 + interfaceMaterial_bufferForComm.size() + i] =
              _avg_stab_coef_L->local(e, q)[i] / measInterface;
        }

        TJ += _bufferSizeForComm;

        // update if necessary
        if (update) {
            std::copy(P_L_new.begin(), P_L_new.end(), _stresses_L->local(e, q));
            std::copy(F_L_new.begin(), F_L_new.end(), _strains_L->local(e, q));
            std::copy(internal_L_new.begin(), internal_L_new.end(), _internals_L->local(e, q));
        }
    }

    // end of method
    return;
}


void summit::ReactionDiffusionInterfaceRegion::ElementaryResidual(elem_t e,
                                                          const std::vector<real>& ul,
                                                          const real* TJ_ElementaryResidual,
                                                          std::vector<real>& rl,
                                                          const bool update)
{
    // DG flux (size of container is _dof_node)
    const real* dg_traction;
    // displacement jump (size of container is _dof_node)
    const real* jumpU;
    // traction compute with cohesive law (size of container is _dof_node)
    const real* cohesive_traction;
    // coefficients to activate the traction-separation-law (TSL) (size of container is 2)
    const real* coefficients;
    // vector containing the normal (size of container is _dof_node)
    const real* normal;
    // vector containing the contracted stabilization coefficient
    // (size of container is _dof_node * _dof_node)
    const real* averg_hs_C_q;
    // stride between information on consecutive quadrature points in
    // TJ_ElementaryResidual
    const size_t stride = ComputeDimensionForTJElementaryResidual();

    // integrate over quadrature points
    for (quad_t q(0); q < _element_set->nquad(); ++q) {
        // extract P_AVG_new (DG flux)
        // from TJ_ElementaryResidual
        // to   TJ_ElementaryResidual + _dof_node
        dg_traction = TJ_ElementaryResidual;

        // extract jumpU (displacement jump)
        // from TJ_ElementaryResidual + _dof_node
        // to   TJ_ElementaryResidual + 2 * _dof_node
        jumpU = TJ_ElementaryResidual + _dof_node;

        // extract cohesive traction
        // from TJ_ElementaryResidual+2*_dof_node+2
        // to   TJ_ElementaryResidual+3*_dof_node+2
        cohesive_traction = TJ_ElementaryResidual + 2 * _dof_node + 2;

        // extract coefficients to activate the TSL and the contact under compression
        // from TJ_ElementaryResidual+3*_dof_node+2
        // to   TJ_ElementaryResidual+3*_dof_node+2+2
        coefficients = TJ_ElementaryResidual + 3 * _dof_node + 2;

        // extract the normal in the deformed configuration
        // from TJ_ElementaryResidual+3*_dof_node+2+2
        // to   TJ_ElementaryResidual+4*_dof_node+2+2
        normal = TJ_ElementaryResidual + 3 * _dof_node + 2 + 2;

        // extract the contracted stabilization coefficient
        // from TJ_ElementaryResidual+4*_dof_node+2+2
        // to   TJ_ElementaryResidual+4*_dof_node+2+2+_dof_node * _dof_node
        averg_hs_C_q = TJ_ElementaryResidual + 3 * _dof_node + 4 + _element_set->dim();

        // compute the part of the residual at the current quadrature point
        _residualIntegrand(e, q, ul, averg_hs_C_q, jumpU, dg_traction, cohesive_traction, coefficients,
                           normal, rl, const_cast<real*>(TJ_ElementaryResidual));

        // increment to the next quadrature point
        TJ_ElementaryResidual += stride;
    }

    // all done
    return;
}


void summit::ReactionDiffusionInterfaceRegion::_residualIntegrand(elem_t e,
                                                          quad_t q,
                                                          const std::vector<real>& ul,
                                                          const real* averg_hs_C,
                                                          const real* dispJump,
                                                          const real* dgTraction,
                                                          const real* cohesive_traction,
                                                          const real* coefficients,
                                                          const real* normal,
                                                          std::vector<real>& rl,
                                                          real* TJ_ElementaryResidual)
{
    std::vector<real> my_damage(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q), my_damage.data());
    std::vector<int> my_types(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceTypes(_internals->local(e, q), my_types.data());
    std::vector<real> my_values(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceValues(_internals->local(e, q), my_values.data());

    real coeff_consistencyTerm = 1.0;// - (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q));
    // activate the compatibility term
    real coeff_compatibilityTerm = 1.0;// - (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q));
    // activate the stabilization term
    real coeff_stabilizationTerm = 1.0;// - (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q));
    // de-activate the cohesive term
    real coeff_cohesiveLawTerm = 0.0;

    bool deleteArrays = false;

    std::vector<real> leftConcentration(_dof_node);
    std::fill(leftConcentration.begin(), leftConcentration.end(), 0.);
    for (lnode_t b = _element_set->leftNodeStart(); b != _element_set->leftNodeDone(); ++b) {
        for (int c = 0; c < _dof_node; c++) {
            leftConcentration[c] += _element_set->shape(e, q, b) * ul[c + b * _dof_node];
        }
    }
    // this code is a hack to force the upwind stabilization to work in parallel
    std::vector<real> otherConcentration(_dof_node);// this is the right state initially    }
   std::vector<real> dC;
    dC.resize(_dof_node*_dof_node*2);// derivative of stab term with respect to left
    // and right state

   std::vector<real> C;
    C.resize(_dof_node);
    for (int c = 0; c < _dof_node; c++) {
        C[c] = 0.0;
    }

    // the following blocks of code do the same thing, just are split up because this is the most complicated thing ever
    if(_amIRight<0){// we are the left state
        for (int c = 0; c < _dof_node; c++) {
            otherConcentration[c] = leftConcentration[c] + dispJump[c] /  _element_set->jac(e, q);
        }
        const real* normalL = _element_set->normalL(e, q);// one cannot use the normal coming across the parallel communications because this normal doesnt work, and never did. it will be filled with zeros until someone fixes this

        _material_L.upwindStabilization(&leftConcentration[0], &leftConcentration[0], &otherConcentration[0], &otherConcentration[0], normalL, &C[0], &dC[0]);
        for (int c = 0; c < _dof_node; c++) {
            C[c] = C[c] * _element_set->jac(e, q);
        }
        real myJ = _element_set->jac(e, q);

    }else{// we are actually the right state and leftState is a misnomer
        for (int c = 0; c < _dof_node; c++) {
            otherConcentration[c] = leftConcentration[c] - dispJump[c] /  _element_set->jac(e, q);
        }
        const real* normalL = _element_set->normalL(e, q);// one cannot use the normal coming across the parallel communications because this normal doesnt work, and never did. it will be filled with zeros until someone fixes this

        _material_L.upwindStabilization(&otherConcentration[0], &otherConcentration[0], &leftConcentration[0], &leftConcentration[0], normalL, &C[0], &dC[0]);
        for (int c = 0; c < _dof_node; c++) {
            C[c] = C[c] * _element_set->jac(e, q);
        }
        real myJ = _element_set->jac(e, q);
    }

    // loop over the nodes of the left element
    for (lnode_t c = _element_set->leftNodeStart(); c != _element_set->leftNodeDone(); ++c) {
        const real test_u = _element_set->shape(e, q, c);
        const real* test_du = _element_set->dShape(e, q, c);

        // loop over its degrees of freedom
        for (size_t i = 0; i < _dof_node; ++i) {
            // compute the index of the current dof in the local displacement vector
            size_t ci = _dof_node * c + i;
            // Here, we compute MINUS the internal forces because the assemble function only adds
            // to the residual: residual = f^ext - f^int
            // update the consistency term
            rl[ci] -= _amIRight * coeff_consistencyTerm * (1.0 - my_damage[i]) *
                      TractionResidualTerm<summit::RIGHT>(e, q, test_u, i, dgTraction);

            rl[ci] -= _amIRight * coeff_consistencyTerm * (1.0 - my_damage[i]) *
                      TractionResidualTerm<summit::RIGHT>(e, q, test_u, i, &C[0]);// this applies the upwind stabilization term in parallel
            // update the compatibility term
#if IMR_COMPATIBILITY
            rl[ci] -=
              coeff_compatibilityTerm * (1.0 - my_damage[i]) *
              CompatibilityResidualTerm(e, q, test_du, test_u, i, dispJump, (_bulkC_L.second)->local(e, q), _kappa_ref_L.data());
#endif  // IMR_COMPATIBILITY
        // update the penalty term
            if (_usingFullStabilizationTerm) {
                rl[ci] -= _amIRight * coeff_stabilizationTerm * (1.0 - my_damage[i]) *
                          StabilizationResidualTermFULL<summit::RIGHT>(e, q, test_u, i, dispJump,
                                                                       averg_hs_C);
            }
            else {
                rl[ci] -= _amIRight * coeff_stabilizationTerm * (1.0 - my_damage[i]) *
                          StabilizationResidualTerm<summit::RIGHT>(e, q, test_u, i, dispJump,
                                                                   averg_hs_C[i]);
            }
            // update the cohesive law
            if(my_types[i]==0){
                rl[ci] -= _amIRight * my_damage[i] *
                          StabilizationResidualTerm<summit::RIGHT>(e, q, test_u, i, dispJump, my_values[i]);
            }
            else{
                real myJump = _amIRight * (leftConcentration[i] - my_values[i]);
                myJump *= _element_set->jac(e, q);
                // std::cout << "myJump: " << myJump <<std::endl;
                rl[ci] -= _amIRight * my_damage[i] *
                          StabilizationResidualTerm<summit::RIGHT>(e, q, test_u, 0, &myJump, averg_hs_C[i]);
            }
        }
    }

    if (deleteArrays) {
        delete[] dispJump;
        delete[] dgTraction;
        delete[] cohesive_traction;
    }

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegion::QuadratureResidual(
  elem_t e,
  quad_t q,
  const std::vector<real>& ul,
  real alpha,
  std::vector<real> const& jumpU,
  std::vector<real> const& left_traction,
  std::vector<real> const& cohesive_traction,
  std::vector<real> const& coefficients,
  std::vector<real>& rl)
{
    // say something useful
    std::cout << "To avoid calling a virtual function at each quadrature point, "
              << "ReactionDiffusionInterfaceRegion::QuadratureResidual has been replaced by "
              << "ReactionDiffusionInterfaceRegion::_residualIntegrand" << std::endl;

    // complain
    throw std::logic_error("Not implemented");
}

void summit::ReactionDiffusionInterfaceRegion::ElementaryStiffness(
  elem_t e,
  real dt,
  NodalField<real> const& u,
  summit::real* TJ,
  summit::real const* missingShapeAndDerivative,
  summit::real const* missingCijkl,
  summit::real const* missingdPdU,
  std::vector<real>& rl,
  std::vector<real>& Kl)
{
    // number of quadrature points
    size_t const nquads = _element_set->nquad();

    const int dim_TJ = this->bufferSizeForComm();

    // array for the interface material tangent computed in case of fracture
   std::vector<real> interface_tangent;
    interface_tangent.resize(_element_set->nquad() * _dof_node * _dof_node);
    std::fill(interface_tangent.begin(), interface_tangent.end(), 0.0);

    // Interface computation
    // summation of dimensions of (i), (ii), (iii), (iv), and (v)
    size_t dim_TJ_ElementaryResidual = ComputeDimensionForTJElementaryResidual();
   ElementQuadratureField<real> TJ_ElementaryResidual(1, nquads, dim_TJ_ElementaryResidual);
    TJ_ElementaryResidual.resize(1, nquads, dim_TJ_ElementaryResidual);
    real* tjer = TJ_ElementaryResidual.local(elem_t(0), quad_t(0));
    this->ElementaryInterfaceConstitutive(e, dt, true /*update*/, false /*timeUpdate*/, TJ, tjer,
                                          interface_tangent.data());

    // DG flux (size of container is _dof_node)
    real* dg_traction;
    // displacement jump (size of container is _dof_node)
    real* jumpU;
    // traction compute with cohesive law (size of container is _dof_node)
    const real* cohesive_traction;
    // coefficients to activate the traction-separation-law (TSL) (size of container is 2)
    const real* coefficients;
    // vector containing the normal (size of container is _dof_node)
    const real* normal;
    // vector containing the contracted stabilization coefficient
    // (size of container is _dof_node * _dof_node)
    const real* averg_hs_C_q;

    // integrate over quadrature points
    for (quad_t q(0); q < _element_set->nquad(); ++q) {
        tjer = TJ_ElementaryResidual.local(elem_t(0), q);

        // extract P_AVG_new (DG flux)
        // from tjer
        // to   tjer + _dof_node
        dg_traction = tjer;

        // extract jumpU (displacement jump)
        // from tjer + _dof_node
        // to   tjer + 2 * _dof_node
        jumpU = tjer + _dof_node;

        // extract cohesive traction
        // from tjer+2*_dof_node+2
        // to   tjer+3*_dof_node+2
        cohesive_traction = tjer + 2 * _dof_node + 2;

        // extract coefficients to activate the TSL and the contact under compression
        // from tjer+3*_dof_node+2
        // to   tjer+3*_dof_node+2+2
        coefficients = tjer + 3 * _dof_node + 2;

        // extract the normal in the deformed configuration
        // from tjer+3*_dof_node+2+2 to tjer+4*_dof_node+2+2
        normal = tjer + 3 * _dof_node + 2 + 2;

        // extract the contracted stabilization coefficient
        // from tj+2*_dof_node+2+materialBuffer
        // to   tj+2*_dof_node+2+materialBuffer+_dof_node*_dof_node
        averg_hs_C_q = TJ + q * dim_TJ + 2 * _dof_node + 2 +
                       _material.materialBufferSizeForComm(_element_set->dim());

        if (coefficients[0] < 0.5) {
            _uncrackedIntegrand(e, q, u, dg_traction, jumpU, interface_tangent.data(), averg_hs_C_q,
                                rl, Kl, missingCijkl, missingdPdU, missingShapeAndDerivative);
        }
        else {
            // regular case, no recontact
            if (coefficients[1] < 0.5) {
                _crackedIntegrand(e, q, cohesive_traction, interface_tangent.data(), rl, Kl,
                                  missingShapeAndDerivative);
            }
            // recontact
            else {
                // compute the projection of the displacement jump and the dg traction onto the
                // normal to the interface.
                real u_n = 0.0;
                real t_dg_n = 0.0;
                for (size_t i = 0; i < _dof_node; ++i) {
                    u_n += jumpU[i] * normal[i];
                    t_dg_n += dg_traction[i] * normal[i];
                }
                for (size_t i = 0; i < _dof_node; ++i) {
                    jumpU[i] = u_n * normal[i];
                    dg_traction[i] = t_dg_n * normal[i];
                }

                _uncrackedIntegrand(e, q, u, dg_traction, jumpU, interface_tangent.data(),
                                    averg_hs_C_q, rl, Kl, missingCijkl, missingdPdU, missingShapeAndDerivative);
            }
        }
    }

    // all done
    return;
}


void summit::ReactionDiffusionInterfaceRegion::PrepareStiffness(
  NodalField<real> const& u,
  NodalField<real> const& u0,
  real dt,
  ElementQuadratureField<real>* TJ_elementsize_nonlinearLawVariables,
  ElementQuadratureField<real>* shapeFunctionAndDeriv,
  ElementQuadratureField<real>* Cijkl,
  ElementQuadratureField<real>* dPdU)
{
    // number of nodes per element
    size_t const nen = _element_set->connectivities_element();
    // size_t const nen = _element_set->nodes_element();
    size_t const dim = _element_set->dim();
    size_t const nelem = _element_set->elements();
    size_t const nquads = _element_set->nquad();

    // Prepare the traction average, displacement jump and nonlinear law variable for interface
    // elements
    //----------------------------------------------------------------------------------------

    // get the size of the local residual
    int const residual_dim = nen * _dof_node;

    // allocate memory for the local displacement (previous and current)
   std::vector<real> ul;
    ul.resize(residual_dim);
   std::vector<real> u0l;
    u0l.resize(residual_dim);

    // loop over the elements of the element set
    for (elem_t e(0); e != _element_set->elements(); ++e) {
        // extract local part of unknown
        _element_set->Localize(u, e, ul);
        _element_set->Localize(u0, e, u0l);

        // compute elementary constitutive
        ElementaryConstitutive(e, dt, false, ul, u0l,
                               TJ_elementsize_nonlinearLawVariables->local(e, quad_t(0)));
    }

    // copy the shape function and it derivative to the output variable
    //-----------------------------------------------------------------------

    for (elem_t e(0); e < nelem; ++e) {
        for (quad_t q(0); q < nquads; ++q) {
            // get the pointer to the date field for this quadrature
            summit::real* shapeDevData = shapeFunctionAndDeriv->local(e, q);
            for (lnode_t n(0); n < nen; ++n) {
                // store the shape function
                shapeDevData[n] = _element_set->shape(e, q, n);
                // store the derivative of the shape function
                for (dim_t i(0); i < dim; ++i) {
                    shapeDevData[nen + n * dim + i] = _element_set->dShape(e, q, n, i);
                }
            }
        }
    }

    // Prepare the stiffness tensor Cijkl
    //-------------------------------------------------------------------------
    for (elem_t e(0); e < nelem; ++e) {
        for (quad_t q(0); q < nquads; ++q) {
            for (size_t i = 0; i < Cijkl->dim(); ++i) {
                Cijkl->local(e, q)[i] = ((_bulkC_L.second)->local(e, q))[i];
            }
            for (size_t i = 0; i < dPdU->dim(); ++i) {
                dPdU->local(e, q)[i] = ((_bulkdPdU_L.second)->local(e, q))[i];
            }
        }
    }

    // end of method
    return;
}


void summit::ReactionDiffusionInterfaceRegion::ComputeStiffnessInterface(
  NodalField<real> const& u,
  NodalField<real> const& u0,
  real dt,
  summit::ElementQuadratureField<real>& TJ_elementsize_nonlinearLawVariables,
  summit::ElementQuadratureField<real>& Hb_shapeFunctionAndDeriv,
  summit::ElementQuadratureField<real>& Hb_Cijkl,
  summit::ElementQuadratureField<real>& Hb_dPdU,
  summit::ElementQuadratureField<int>& halfButterflyMissingWingEQN,
  summit::ElementQuadratureField<real>& halfButterflyMissingWingForces,
  Stiffness& stiffness,
  NodalField<real>& residual,
  summit::SIDE side)
{
    // number of nodes per element
    // this method is called on the halfbutterfly element on the lower processor ID and
    // the element set will have all the information of the "missing wing" to construct
    // a "full ghost" butterfly element, thus the nen is multiplied by factor 2
    size_t const nen = 2 * _element_set->connectivities_element();
    // size of the local residual
    int const residual_dim = nen * _dof_node;
    // allocate memory for local residual and stiffness matrix
   std::vector<real> rl;
    rl.resize(residual_dim);
   std::vector<real> Kl;
    Kl.resize(residual_dim * residual_dim);

    // allocate memory for element equation map and forces
    summit::NodalField<int> elemEqnMap(_dof_node, nen);
    summit::NodalField<summit::real> elemForces(_dof_node, nen);
    // accessor to halfButterflyMissingWingEQN and halfButterflyMissingWingForces data
    int* halfButterflyMissingWingEQN_data = halfButterflyMissingWingEQN.data();
    real* halfButterflyMissingWingForces_data = halfButterflyMissingWingForces.data();

    // loop over the elements of the element set
    for (elem_t e(0); e != _element_set->elements(); ++e) {
        // reset residual and stiffness
        std::fill(rl.begin(), rl.end(), 0.0);
        std::fill(Kl.begin(), Kl.end(), 0.0);

        // compute elementary stiffness matrix and residual
        ElementaryStiffness(e, dt, u, /* ul, u0l,*/
                            TJ_elementsize_nonlinearLawVariables.local(e, quad_t(0)),
                            Hb_shapeFunctionAndDeriv.local(e, quad_t(0)),
                            Hb_Cijkl.local(e, quad_t(0)), Hb_dPdU.local(e, quad_t(0)), rl, Kl);

        // only take the residual of the halfbutterfly element
        rl.resize(residual_dim / 2);
        // assemble local residual into global array
        if (stiffness.getAssembleResidualFlag() != 0) {
            _element_set->Assemble(rl, e, residual);
        }

        // NOTE: ONLY THE ELEMENT ON THE LOWER PROCESSOR ID IS RESPONSIBLE FOR ASSEMBLING THE
        // STIFFNESS
        // if this halfbutterfly element is on lower id processor,
        // it will assemble the stiffness matrix to the global
        if (side == summit::LEFT) {
            // construct the equation map and forces array for this element
            //--fill the left wing
            for (size_t nod = 0; nod < nen / 2; ++nod) {
                for (size_t i = 0; i < _dof_node; ++i) {
                    int nodeID = _element_set->dof_map()->Connectivity(e)[nod];
                    elemEqnMap(nod, i) = stiffness.mapNodeToEqnNumber(nodeID, i);
                    elemForces(nod, i) = stiffness.getForces(nodeID, i);
                }
            }
            //--fill the right wing
            for (size_t nod = 0; nod < nen / 2; ++nod) {
                for (size_t i = 0; i < _dof_node; ++i) {
                    elemEqnMap(nod + nen / 2, i) =
                      halfButterflyMissingWingEQN_data[e * nen / 2 * _dof_node + nod * _dof_node +
                                                       i];
                    elemForces(nod + nen / 2, i) =
                      halfButterflyMissingWingForces_data[e * nen / 2 * _dof_node +
                                                          nod * _dof_node + i];
                }
            }

            // assemble local stiffness matrix into global matrix
            stiffness.AddElement(elemEqnMap, elemForces, &Kl[0], nen, nen, _dof_node);
        }
    }

    // end of method
    return;
}

void summit::ReactionDiffusionInterfaceRegion::ElementaryPrepareUpdate(elem_t e,
                                                               real dt,
                                                               std::vector<real> const& ul,
                                                               std::vector<real> const& u0l,
                                                               ElementQuadratureField<real>* TJf)
{
    // strain dimension
    int const strain_dim = _strains_L->dim();
    // dimension of left and right internal variables
    size_t const internal_L_dim = _material_L.nInt();
    // spatial dimension
    size_t spatial_dim = _element_set->dim();

    // memory allocation
   std::vector<real> P_L_new;
    P_L_new.resize(strain_dim);
   std::vector<real> f_L_new;
    f_L_new.resize(strain_dim);
    // memory allocation
   std::vector<real> F_L_new;


    std::vector<real> concentration_L(_dof_node);
    std::vector<real> concentration_L0(_dof_node);

    // GB: before nen in place of _dof_node
    F_L_new.resize(spatial_dim * _dof_node);
   std::vector<real> internal_L_new;
    internal_L_new.resize(internal_L_dim);

   std::vector<real> interfaceMaterial_bufferForComm;
    interfaceMaterial_bufferForComm.resize(_material.materialBufferSizeForComm(spatial_dim));

   std::vector<real> xl;
    xl.resize(_element_set->nodes_element() * _element_set->dim());
    //    localize the coordintes
    _element_set->Localize(_coordinates, e, xl);
    //    characteristic lengths of left and right elements
    real inRadius_left = 0.0;
    if (_element_set->side() == summit::LEFT) inRadius_left = _element_set->InRadiusElement(&xl[0]);
    real inRadius_right = 0.0;
    if (_element_set->side() == summit::RIGHT)
        inRadius_right = _element_set->InRadiusElement(&xl[0]);

    real* TJ = TJf->local(elem_t(e), quad_t(0));

    // ... for the jump in displacement
   std::vector<real> jumpU;
    jumpU.resize(_dof_node);
    // ... for the left traction
   std::vector<real> left_traction;
    left_traction.resize(_dof_node);

    // integrate over quadrature points
    for (quad_t q(0); q < _element_set->nquad(); ++q) {
        // constitutive update of the left element
	for (size_t i = 0; i < _CField_L->dim(); ++i) {
            _CField_L->local(e, q)[i] = 0.0;
            _C_L[i] = 0.0;
        }
        for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
            _dPdUField_L->local(e, q)[i] = 0.0;
            _dPdU_L[i] = 0.0;
        }
	    for (size_t i = 0; i < _dFField_L->dim(); ++i) {
            _dFField_L->local(e, q)[i] = 0.0;
	        _dF_L[i] = 0.0;
        }
        LeftConstitutiveUpdate(e, q, ul, u0l, concentration_L0, concentration_L, dt, P_L_new, f_L_new, F_L_new, internal_L_new, _C_L.data(), _dPdU_L.data(), _dF_L.data());
        for (size_t i = 0; i < _CField_L->dim(); ++i) {
            _CField_L->local(e, q)[i] = _C_L[i];
        }
        for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
            _dPdUField_L->local(e, q)[i] = _dPdU_L[i] + _dF_L[i];
        }
        for (size_t i = 0; i < _dFField_L->dim(); ++i) {
            _dFField_L->local(e, q)[i] = _dF_L[i];
        }
        // update the stresses and the internal variables
        std::copy(P_L_new.begin(), P_L_new.end(), _stresses_L->local(e, q));
        std::copy(F_L_new.begin(), F_L_new.end(), _strains_L->local(e, q));
        std::copy(internal_L_new.begin(), internal_L_new.end(), _internals_L->local(e, q));

        // reference to the left normal
        const real* normalL = _element_set->normalL(e, q);

        InterfaceMaterialFailureCriterionEvaluationOneSide(
          e, q, P_L_new, F_L_new, normalL, _element_set->side(), interfaceMaterial_bufferForComm);

        // jacobians (with quadrature weights)
        real jac = _element_set->jac(e, q);

        // compute the displacement jump at the current quad point
        DisplacementJump(e, q, jac, ul, jumpU);

        // AverageTraction(P_L_new,P_R_new,jac,normalL,left_traction);
        std::fill(left_traction.begin(), left_traction.end(), 0.);

        // project the average PK-I onto the normal to get the left traction
        for (size_t i = 0; i < _dof_node; ++i) {
            for (dim_t j(0); j < spatial_dim; ++j) {
                size_t ij = spatial_dim * i + j;
                left_traction[i] += (0.5 * jac * (P_L_new[ij] + f_L_new[ij])) * normalL[j];
            }
        }

        // copy to global array
        // left traction
        std::copy(left_traction.begin(), left_traction.end(), TJ);
        // displacement jump
        std::copy(jumpU.begin(), jumpU.end(), TJ + _dof_node);
        // h_left
        TJ[2 * _dof_node] = inRadius_left;
        // h_right
        TJ[2 * _dof_node + 1] = inRadius_right;
        // buffer from interface material
        std::copy(interfaceMaterial_bufferForComm.begin(), interfaceMaterial_bufferForComm.end(),
                  TJ + 2 * _dof_node + 2);
        TJ += _bufferSizeForComm;
    }

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegion::ElementaryComputeUpdate(elem_t e,
                                                               ElementQuadratureField<real>* TJf,
                                                               real dt)
{
    int dim_TJ_ElementaryResidual = ComputeDimensionForTJElementaryResidual();
   ElementQuadratureField<real> TJ_ElementaryResidualField;
    TJ_ElementaryResidualField.resize(1, _element_set->nquad(), dim_TJ_ElementaryResidual);
    real* TJ_ElementaryResidual = TJ_ElementaryResidualField.local(elem_t(0), quad_t(0));

    // Step to compute the DG fluxes or TSL for the interface element
    ElementaryInterfaceConstitutive(e, dt, true /*update*/, true /*timeUpdate*/,
                                    TJf->local(e, quad_t(0)), TJ_ElementaryResidual);

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegion::ElementaryComputeUpdateSubIteration(
  elem_t e, ElementQuadratureField<real>* TJf, real dt)
{
    int dim_TJ_ElementaryResidual = ComputeDimensionForTJElementaryResidual();
   ElementQuadratureField<real> TJ_ElementaryResidualField;
    TJ_ElementaryResidualField.resize(1, _element_set->nquad(), dim_TJ_ElementaryResidual);
    real* TJ_ElementaryResidual = TJ_ElementaryResidualField.local(elem_t(0), quad_t(0));

    // Step to compute the DG fluxes or TSL for the interface element
    ElementaryInterfaceConstitutive(e, dt, true /*update*/, false /*timeUpdate*/,
                                    TJf->local(e, quad_t(0)), TJ_ElementaryResidual);

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegion::ElementaryComputeUpdate(elem_t e,
                                                               ElementQuadratureField<real>* TJ,
                                                               real dt,
                                                               std::string const& name)
{
    int dim_TJ_ElementaryResidual = ComputeDimensionForTJElementaryResidual();
   ElementQuadratureField<real> TJ_ElementaryResidualField;
    TJ_ElementaryResidualField.resize(1, _element_set->nquad(), dim_TJ_ElementaryResidual);
    real* TJ_ElementaryResidual = TJ_ElementaryResidualField.local(elem_t(0), quad_t(0));

    // Step to compute the DG fluxes or TSL for the interface element
    ElementaryInterfaceConstitutive(e, dt, true, true /*timeUpdate*/, name, TJ->local(e, quad_t(0)),
                                    TJ_ElementaryResidual);

    // all done
    return;
}

#if 0
summit::real
summit::ReactionDiffusionInterfaceRegion::
CohesiveLawResidualTerm(elem_t e, quad_t q, lnode_t c, size_t i, const real* jumpU,
                        const real* cohesive_traction, bulkTangent_t const & bulkC) const
{
    // term of node c in i-th direction of the local residual
    real rl_ci = 0.0;

    // update the cohesive law term
    if (bulkC.first == summit::LEFT)
    {
        // add on the left
        rl_ci -= _element_set->shape(e,q,c) * cohesive_traction[i];
    }
    else if (bulkC.first == summit::RIGHT)
    {
        // substract on the right
        rl_ci += _element_set->shape(e,q,c) * cohesive_traction[i];
    }
    else
    {
        // say something useful...
        std::cout << "in InterfaceWeakForm::CohesiveLawResidualTerm, side: "
                  << bulkC.first << " unknown!!"
                  << std::endl;
        // ... and die
        exit(1);
    }

    // all done
    return rl_ci;
}
#endif

void summit::ReactionDiffusionInterfaceRegion::LeftConstitutiveUpdate(elem_t e,
                                                              quad_t q,
                                                              std::vector<real> const& ul,
                                                              std::vector<real> const& u0l,
                                                              std::vector<real>& concentration0,
                                                              std::vector<real>& concentration,
                                                              real dt,
                                                              std::vector<real>& P_L_new,
                                                              std::vector<real>& f_L_new,
                                                              std::vector<real>& F_L_new,
                                                              std::vector<real>& internal_L_new,
                                                              real* C_L,
                                                              real* dPdU_L,
                                                              real* df_L)
{
    // spatial dimension
    size_t const spatial_dim = _element_set->dim();
    // strain dimension
    int const strain_dim = _strains_L->dim();
    // dimension of left internal variables
    size_t const internal_L_dim = _material_L.nInt();
    // To know if the tangent has to be computed
    bool const compute_tangent = (C_L ? true : false);

    size_t const nen = _element_set->connectivities_element();
    // Compute the left gradient
    _element_set->Gradient_L(e, q, ul.data(), _dof_node, F_L_new.data());
    // std::vector<real> concentration(_dof_node);
    // std::vector<real> concentration0(_dof_node);
    std::fill(concentration.begin(), concentration.end(), 0.);
    std::fill(concentration0.begin(), concentration0.end(), 0.);


    for (lnode_t b = _element_set->leftNodeStart(); b != _element_set->leftNodeDone(); ++b) {
        for (int c = 0; c < _dof_node; c++) {
            concentration[c] += _element_set->shape(e, q, b) * ul[c + b * _dof_node];
            concentration0[c] += _element_set->shape(e, q, b) * u0l[c + b * _dof_node];
        }
    }

    // pointer to local value of stress at previous time step
    real* P_L = _stresses_L->local(e, q);
    // copy to local Left stresses
    std::copy(P_L, P_L + strain_dim, P_L_new.begin());

    // local value of internal variables at previous time step
    real* internal_L = _internals_L->local(e, q);
    // copy to local Left internal variables
    std::copy(internal_L, internal_L + internal_L_dim, internal_L_new.begin());

    // local value of old deformation gradient at previous time step
    real* F0_L = _strains_L->local(e, q);

    // Constitutive update
    _material_L.Constitutive(&concentration0[0], &concentration[0], F0_L, F_L_new.data(), P_L_new.data(),
                             internal_L_new.data(), C_L, dPdU_L, dt, _dof_node, spatial_dim,
                             compute_tangent, false);
    //this line updates internal variable processes needed to compute source terms
    //source terms themselves are not relevant for the trace quadrature points
    //line needed for consistency with bulk models
    std::vector<real> sources(_dof_node);
    std::vector<real> dsources(_dof_node * _dof_node);
    std::vector<real> dsourcesdgrad(_dof_node * _dof_node * spatial_dim);
    _material_L.Source(&concentration0[0], &concentration[0], F0_L, F_L_new.data(), internal_L_new.data(), &dt, &sources[0], &dsources[0], &dsourcesdgrad[0], spatial_dim, _dof_node);

    _material_L.ConvectiveFlux(&concentration0[0], &concentration[0], &internal_L_new[0],
                            &dt, f_L_new.data(), &df_L[0], _dof_node, spatial_dim);
    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegion::DisplacementJump(
  elem_t e, quad_t q, real jac, std::vector<real> const& ul, std::vector<real>& jumpU)
{
    // If we pre-multiply the whole displacement by the jacobian then the displacement jump is
    // computed in the same order in the sequential and the parallel cases. Of course, the way
    // it's done here is not really efficient... Also, that's why the post-multiplication is
    // commented out below.
    std::vector<real> ul_copy(ul.size());
    std::copy(ul.begin(), ul.end(), ul_copy.begin());
    // multiply the displacement by the jac
    for (size_t i = 0; i < ul.size(); ++i) {
        ul_copy[i] *= jac;
    }
    const size_t c_dof_node = _dof_node;
    // let the element set compute the jump
    _element_set->Jump(e, q, ul_copy, jumpU,  &c_dof_node);

#if 0
    // then, multiply by the jac
    for (size_t i = 0; i < spatial_dim; ++i) {
        jumpU[i] *= jac;
    }
#endif

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegion::InterfaceMaterialFailureCriterionEvaluationOneSide(
  elem_t e,
  quad_t q,
  std::vector<real>& P_L_new,
  std::vector<real>& F_L_new,
  const real* normalL,
  summit::SIDE side,
  std::vector<real>& interfaceMaterial_bufferForComm)
{
    // spatial dimension
    size_t const spatial_dim = _element_set->dim();

    // local value (at the quadrature point) of internal variables at previous time step
    const real* internal = _internals->local(e, q);

    // Evaluate the failure criterion for the specific Gauss point
    // Notice that:
    //   - Internal variables are not modified
    //   - interfaceMaterial_bufferForComm stores the information to decide if the failure
    //     criterion is satisfied at the quadrature point. This information will be used by
    //     summit::commonInterfaceMechanicsRegion::ComputeResidual(...)
    //   - The failure criterion is evaluated only for left or right element
    if (side == summit::LEFT) {
        _material.EvaluateFailureCriterion(P_L_new.data(), F_L_new.data(), NULL, NULL, internal,
                                           normalL, side, spatial_dim,
                                           interfaceMaterial_bufferForComm.data());
    }
    else if (side == summit::RIGHT) {
        _material.EvaluateFailureCriterion(NULL, NULL, P_L_new.data(), F_L_new.data(), internal,
                                           normalL, side, spatial_dim,
                                           interfaceMaterial_bufferForComm.data());
    }
    else {
        // complain
        throw std::runtime_error("The side here has to be either LEFT or RIGHT");
    }

    return;
}

void summit::ReactionDiffusionInterfaceRegion::ElementaryInterfaceConstitutive(elem_t e,
                                                                       real dt,
                                                                       bool update,
                                                                       bool timeUpdate,
                                                                       real* TJ,
                                                                       real* TJ_ElementaryResidual,
                                                                       real* interface_tangent)
{
    // ************* Data structure for TJ and TJ_ElementaryResidual
    //
    // TJ = [ T_DG_gp1, uJump_gp1, inRadiusLeft_gp1, inRadiusRight_gp1, interfaceVariables_gp1,
    //        T_DG_gp2, uJump_gp2, inRadiusLeft_gp2, inRadiusRight_gp2, interfaceVariables_gp2,
    //        ...
    //        T_DG_gpN, uJump_gpN, inRadiusLeft_gpN, inRadiusRight_gpN, interfaceVariables_gpN]
    //
    // TJ_ElementaryResidual = [
    //    T_DG_gp1, uJump_gp1, inRadiusLeft_gp1, inRadiusRight_gp1, T_TSL_gp1,
    //         coeff_TSL_gp1, coeff_contact_gp1, normalDeformedLeft_gp1,
    //    T_DG_gp2, uJump_gp2, inRadiusLeft_gp2, inRadiusRight_gp2, T_TSL_gp2,
    //         coeff_TSL_gp2, coeff_contact_gp2, normalDeformedLeft_gp2,
    //    ...
    //    T_DG_gpN, uJump_gpN, inRadiusLeft_gpN, inRadiusRight_gpN, T_TSL_gpN,
    //         coeff_TSL_gpN, coeff_contact_gpN, normalDeformedLeft_gpN]
    //
    // dimension TJ --> [ _dof_node, _dof_node, 1, 1, sizeInterfaceVariables,
    //                    _dof_node, _dof_node, 1, 1, sizeInterfaceVariables,
    //                    ...
    //                    _dof_node, _dof_node, 1, 1, sizeInterfaceVariables]
    //
    // dimension TJ_ElementaryResidual --> [ _dof_node, _dof_node, 1, 1, _dof_node, 1, 1,_dof_node,
    //                                       _dof_node, _dof_node, 1, 1, _dof_node, 1, 1,_dof_node,
    //                                       ...
    //                                       _dof_node, _dof_node, 1, 1, _dof_node, 1, 1,_dof_node]
    //
    // N is the number of quadrature points

    // ************* Dimensions of the vectors used in calculations
    // number of Gauss points
    size_t const nquad = _element_set->nquad();
    // spatial dimension
    size_t const ndm = _element_set->dim();
    // number of degree of freedom per node
    size_t const ndf = _dof_node;
    // dimension of the traction vector
    size_t const sizeTraction = _dof_node;
    // dimension of the displacement jump vector
    size_t const sizeJump = _dof_node;
    // dimension of TJ_ElementaryResidual
    size_t const sizeTJElementaryResidual = ComputeDimensionForTJElementaryResidual();
    // buffSize = size of First Piola-Kirchhoff stress tensor + size of the displacement jump
    //          + 2 + interface variables
    size_t const buffSize = this->bufferSizeForComm();
    size_t const materialBuffSize = _material.materialBufferSizeForComm(_element_set->dim());
    // counters
    size_t counterTJ, counterTJ_ElementaryResidual;

    // ************* PART I: Copy data from TJ to TJ_ElementaryResidual
    //               (for all the quadrature points)
    // Only DG flux (left_traction), displacement jump (uJump),
    // and left and right inRadius are copied

    // initialize the TJ_ElementaryResidual array
    for (quad_t q(0); q < nquad; ++q) {
        for (size_t a = 0; a < sizeTJElementaryResidual; a++) {
            TJ_ElementaryResidual[q * sizeTJElementaryResidual + a] = 0.0;
        }
    }

    // loop over quadrature points
    counterTJ = 0;
    counterTJ_ElementaryResidual = 0;
    for (quad_t q(0); q < nquad; ++q) {
        // Copy traction (DG flux), displacement jump, and left and right inRadius
        for (size_t a = 0; a < (sizeTraction + sizeJump + 2); a++) {
            TJ_ElementaryResidual[counterTJ_ElementaryResidual + a] = TJ[counterTJ + a];
        }

        for (size_t i = 0; i < _avg_stab_coef_L->dim(); ++i) {
            TJ_ElementaryResidual[counterTJ_ElementaryResidual + 3 * _dof_node + _element_set->dim() + 4 + i] =
              TJ[counterTJ + 2 * _dof_node + 2 + materialBuffSize + i];
        }

        counterTJ += buffSize;
        counterTJ_ElementaryResidual += sizeTJElementaryResidual;
    }

    // number of interface variables is zero
    // It is a purely DG fully tied interface law
    if (materialBuffSize == 0) {
        // all done
        return;
    }

    // ************* PART II: Unpack the information stored in interface variables
    // The data unpacked is:
    //  - Traction to the left element: used in the calculation of interface constitutive law
    //  - normal to left element in deformed configuration: used in the calculation of interface
    //    constitutive law
    //  - number of times that failure criterion is satisfied at the interface:
    //    The possible values are: 2 (both left and right elements), 1 (only left or right element),
    //    0 (none element)

    // buffers to store the data packed in the interfaceVariables (TJ) for all the quadrature points
    //    left traction (computed with the First Piola-Kirchhoff stress tensor of the left element)
   ElementQuadratureField<real> tractionLeft;
    tractionLeft.resize(1, nquad, sizeTraction);
    //    number of times that failure criterion is satisfied
   std::vector<int> numberTimesFailureCriterionIsSatisfied;
    numberTimesFailureCriterionIsSatisfied.resize(nquad);

    // initialize counters
    counterTJ = 0;
    counterTJ_ElementaryResidual = 0;
    // loop over quadrature points
    for (quad_t q(0); q < nquad; ++q) {
        // the data is stored in the corresponding arrays
        _material.UnpackBufferForCommunication(
          ndm, &numberTimesFailureCriterionIsSatisfied[q], tractionLeft.local(elem_t(0), q),
          &TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + sizeJump + 2 +
                                 sizeTraction + 2],
          &TJ[counterTJ + sizeTraction + sizeJump + 2]);
        // update counters
        counterTJ_ElementaryResidual += sizeTJElementaryResidual;
        counterTJ += buffSize;
    }

    // ************* PART III: Decide if fracture is initiated or not
    // array to store the flag that indicates if fracture has to be initiated
    bool* initiateFracture;
    initiateFracture = new bool[nquad];
    // avoid valgrind to complain about
    for (quad_t q(0); q < nquad; ++q) {
        initiateFracture[q] = false;
    }
    // decide fracture initiation
    // the method DecideFractureInitiation(..) takes the decision based on the material input
    // parameters
    // the value of each component of initiateFracture can be true or false, and this value is used
    // to set the parameter coeff_TSL which activates the traction-separation-law
    _material.DecideFractureInitiation(nquad, &numberTimesFailureCriterionIsSatisfied[0],
                                       initiateFracture);

    // ************* PART IV: Compute the traction for interface material
    // Traction calculated with Constitutive (i.e., traction from traction-separation-law) is
    // directly stored in TJ_ElementaryResidual
    // Coefficients for TSL and contact are also directly stored in TJ_ElementaryResidual

    // array to store internal variables
    size_t const internal_dim = _material.nInt();
   std::vector<real> internal_new;
    internal_new.resize(internal_dim);
    // array to store the jump divided by the jacobian
   std::vector<real> jumpDividedJacobian;
    jumpDividedJacobian.resize(sizeJump);
    // array to store the DG_flux divided by the jacobian
   std::vector<real> dgFluxDividedJacobian;
    dgFluxDividedJacobian.resize(sizeJump);

    // to store the jacobian and its inverse for a specific quadrature point
   real jac;
   real inv_jac;

    // pointer to local value of internal variables at previous time step
    real* internal;

#if SAVE_DATAFRACTURE_MODEI
    // create an array to store the damage of all the quadrature points
   std::vector<real> data_damage;
    data_damage.resize(nquad);
#endif

    // status of the contact activation in each quadrature point
   std::vector<int> contactActivationStatusPerQuadraturePoint;
    contactActivationStatusPerQuadraturePoint.resize(nquad);

    // characteristic length for contact
    // it is the minimum inter-element penetration length required to apply contact
   real contactCharacteristicLength;
    contactCharacteristicLength = MIN(MAX(1.0e-10, fabs(TJ_ElementaryResidual[2 * ndf])),
                                      MAX(1.0e-10, fabs(TJ_ElementaryResidual[2 * ndf + 1])));
    contactCharacteristicLength = 0.1 * contactCharacteristicLength;

    // loop over quadrature points
    counterTJ_ElementaryResidual = 0;
    counterTJ = 0;
    for (quad_t q(0); q < nquad; ++q) {
        // local value of internal variables at previous time step
        internal = _internals->local(e, q);
        // copy to local Left internal variables
        std::copy(internal, internal + internal_dim, internal_new.begin());

        // inverse of jacobian (with Gauss quadrature weight)
        jac = _element_set->jac(e, q);
        inv_jac = 1.0 / jac;

        // the jump is multiplied by the inverse of the jacobian: this step is required because the
        // current jump is multiplied by the jacobian
        for (size_t i = 0; i < sizeJump; ++i) {
            jumpDividedJacobian[i] =
              TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + i] * inv_jac;
        }

        // the DG_flux is multiplied by the inverse of the jacobian: this step is required because
        // the
        // current DG_flux is multiplied by the jacobian
        for (size_t i = 0; i < sizeJump; ++i) {
            dgFluxDividedJacobian[i] =
              TJ_ElementaryResidual[counterTJ_ElementaryResidual + i] * inv_jac;
        }

        // compute the constitutive law for the interface material
        // store traction from TSL
        // store coefficients for TSL activation and contact
        // send normal to left element in deformed configuration (it is not modified)
        _material.Constitutive(  // displacement jump
          &jumpDividedJacobian[0],
          // normal to left element (deformed configuration)
          &TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + sizeJump + 2 +
                                 sizeTraction + 2],
          // traction to the left element
          tractionLeft.local(elem_t(0), q),
          // DG_flux
          &dgFluxDividedJacobian[0],
          // coeffients for TSL activation and contact
          &TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + sizeJump + 2 +
                                 sizeTraction],
          // internal variables (can be modified)
          &internal_new[0],
          // traction computed with traction-separation-law
          &TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + sizeJump + 2],
          // interface tangents
          interface_tangent,
          // characteristic length for contact
          contactCharacteristicLength,
          // number of degree of freedom per node
          ndf,
          // spatial dimension
          ndm,
          // bool whether to compute tangents
          (interface_tangent ? true : false),
          // to indicate fracture initiation
          initiateFracture[q]);

        // store the contact status for the quad point q
        contactActivationStatusPerQuadraturePoint[q] =
          (int)TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + sizeJump + 2 +
                                     sizeTraction + 1];

        // increase the pointer interface_tangent until next quadrature point
        if (interface_tangent) {
            interface_tangent += ndm * ndm;
        }
        // the TSL is multiplied by the jacobian: this step is required because the traction needed
        // in ElementaryResidual has to be multiplied by the jacobian

        for (size_t i = 0; i < sizeTraction; ++i) {
            TJ[counterTJ + 2 * _dof_node + 3 + i] =
              TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + sizeJump + 2 + i];
            TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + sizeJump + 2 + i] *=
              jac;
        }

#if SAVE_DATAFRACTURE_MODEI
        // save the damage corresponding to the quadrature point q
        data_damage[q] = internal_new[7];
#endif

        // update history variable in internal variables
        if (timeUpdate) {
            _material.Update(&internal_new[0]);
        }

        // update the internal variables computed for the interface material in the global array
        if (update) {
            std::copy(internal_new.begin(), internal_new.end(), _internals->local(e, q));
        }
        // update counter
        counterTJ_ElementaryResidual += sizeTJElementaryResidual;
        counterTJ += buffSize;
    }

#if 0
    //
    // This part is for elementwise recontacting decision. If it is commented out it means that the
    // decision for recontacting is taken at each quadrature points.
    //

    // decide if contact is applied to the element or not
    bool contactActivationStatus =
            _material.DecideContactActivation(nquad, &contactActivationStatusPerQuadraturePoint[0]);
    if (contactActivationStatus == true)
    {
      printf("\nActivated\n");
       // initialize counter
       counterTJ_ElementaryResidual = 0;
       for (quad_t q(0); q < nquad; ++q)
       {
           // activate contact for quad point q
           TJ_ElementaryResidual[counterTJ_ElementaryResidual
                                + sizeTraction
                                + sizeJump
                                + 2
                                + sizeTraction
                                + 1] = 1.0;
           // update counter
           counterTJ_ElementaryResidual += sizeTJElementaryResidual;
       }
    }
    else
    {
       // initialize counter
       counterTJ_ElementaryResidual = 0;
       for (quad_t q(0); q < nquad; ++q)
       {
           // DO NOT activate contact for quad point q
           TJ_ElementaryResidual[counterTJ_ElementaryResidual
                                + sizeTraction
                                + sizeJump
                                + 2
                                + sizeTraction
                                + 1] = 0.0;
           // update counter
           counterTJ_ElementaryResidual += sizeTJElementaryResidual;
       }
    }
#endif

    // delete array
    delete[] initiateFracture;


#if SAVE_DATAFRACTURE_MODEI

    // number of nodes in the element (two interface elements)
    int nnodes = _element_set->nodes_element();

    // vector to store the nodal coordinates of the interface element e
   std::vector<real> xl;
    xl.resize(nnodes * ndm);
    // localize the coordinates
    _element_set->Localize(_coordinates, e, xl);

    // vector to store coordinates x and y of the quadrature points
   std::vector<real> x_gp;
   std::vector<real> y_gp;
    x_gp.resize(nquad);
    y_gp.resize(nquad);
    for (quad_t q(0); q < nquad; ++q) {
        x_gp[q] = 0.0;
        y_gp[q] = 0.0;
    }

    // interpolation of the coordinates x and y of the quadrature points
    double shp_gp;
    for (quad_t q(0); q < nquad; ++q) {
        for (lnode_t c = _element_set->leftNodeStart(); c != _element_set->leftNodeDone(); ++c) {
            shp_gp = _element_set->shape(e, q, c);
            x_gp[q] += shp_gp * xl[c * ndm + 0];
            y_gp[q] += shp_gp * xl[c * ndm + 1];
        }
    }

    // determine if the element is in the crack path
    // counting the number of quadrature points whose y coordinate is equal to 0
    size_t count = 0;
    for (quad_t q(0); q < nquad; ++q) {
        if (fabs(y_gp[q]) < 1.0e-6) count++;
    }

    // only save the information for the elements that are in the crack path

    if (count == nquad) {
        // Get the material label and element index as strings
        std::string buffer_m = std::to_string(_element_set->materialLabel());
        std::string buffer_e = std::to_string(e);
        std::string file_damageM = "_mL_" + buffer_m;

        // Loop over quadrature points
        for (quad_t q(0); q < nquad; ++q) {
            std::string buffer_q = std::to_string(q);

            // Construct the damage file name
            std::string filename = "damage_e_" + buffer_e + "_q_" + buffer_q + file_damageM + ".txt";

            // Write damage data
            std::ofstream myfileDamage(filename, std::ios::out | std::ios::app);
            myfileDamage << x_gp[q] << " " << dt << " " << data_damage[q] << "\n";
        }

        // Optional: Write dtime (uncomment if needed)
        /*
        std::ofstream myfileDeltaTime("dtime_e_q_mL.txt", std::ios::out | std::ios::app);
        myfileDeltaTime << dt << "\n";
        */

        // Optional: Write X coordinates (uncomment if needed)
        /*
        std::string coordX_filename = "coordX_e_" + buffer_e + file_damageM + ".txt";
        std::ofstream myfileXcoord(coordX_filename, std::ios::out | std::ios::trunc);
        for (quad_t q(0); q < nquad; ++q) {
            myfileXcoord << x_gp[q] << "\n";
        }
        */
    }

    #endif

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegion::ElementaryInterfaceConstitutive(elem_t e,
                                                                       real dt,
                                                                       bool update,
                                                                       bool timeUpdate,
                                                                       std::string const& name,
                                                                       real* TJ,
                                                                       real* TJ_ElementaryResidual,
                                                                       real* interface_tangent)
{
    // position of the property in the internal variable container
    int position;
    // size of material property: scalar, vector, tensor
    size_t size;
    // ask the material if he can process the current material property from the internal
    // variable
    bool gotIt = _material.GetLocationInInternalTable(name, position, size);
    // if it does
    if (gotIt) {
        // ************* Data structure for TJ and TJ_ElementaryResidual
        //
        // TJ = [ T_DG_gp1, uJump_gp1, inRadiusLeft_gp1, inRadiusRight_gp1, interfaceVariables_gp1,
        //        T_DG_gp2, uJump_gp2, inRadiusLeft_gp2, inRadiusRight_gp2, interfaceVariables_gp2,
        //        ...
        //        T_DG_gpN, uJump_gpN, inRadiusLeft_gpN, inRadiusRight_gpN, interfaceVariables_gpN]
        //
        // TJ_ElementaryResidual = [
        //    T_DG_gp1, uJump_gp1, inRadiusLeft_gp1, inRadiusRight_gp1, T_TSL_gp1,
        //         coeff_TSL_gp1, coeff_contact_gp1, normalDeformedLeft_gp1,
        //    T_DG_gp2, uJump_gp2, inRadiusLeft_gp2, inRadiusRight_gp2, T_TSL_gp2,
        //         coeff_TSL_gp2, coeff_contact_gp2, normalDeformedLeft_gp2,
        //    ...
        //    T_DG_gpN, uJump_gpN, inRadiusLeft_gpN, inRadiusRight_gpN, T_TSL_gpN,
        //         coeff_TSL_gpN, coeff_contact_gpN, normalDeformedLeft_gpN]
        //
        // dimension TJ --> [ _dof_node, _dof_node, 1, 1, sizeInterfaceVariables,
        //                    _dof_node, _dof_node, 1, 1, sizeInterfaceVariables,
        //                    ...
        //                    _dof_node, _dof_node, 1, 1, sizeInterfaceVariables]
        //
        // dimension TJ_ElementaryResidual --> [ _dof_node, _dof_node, 1, 1,
        //                                       _dof_node, 1, 1,_dof_node,
        //                                       _dof_node, _dof_node, 1, 1,
        //                                       _dof_node, 1, 1,_dof_node,
        //                                       ...
        //                                       _dof_node, _dof_node, 1, 1,
        //                                       _dof_node, 1, 1,_dof_node]
        //
        // N is the number of quadrature points

        // ************* Dimensions of the vectors used in calculations
        // number of Gauss points
        size_t const nquad = _element_set->nquad();
        // spatial dimension
        size_t const ndm = _element_set->dim();
        // number of degree of freedom per node
        size_t const ndf = _dof_node;
        // dimension of the traction vector
        size_t const sizeTraction = _dof_node;
        // dimension of the displacement jump vector
        size_t const sizeJump = _dof_node;
        // dimension of TJ_ElementaryResidual
        size_t const sizeTJElementaryResidual = ComputeDimensionForTJElementaryResidual();
        // buffSize = size of First Piola-Kirchhoff stress tensor + size of the displacement jump
        //          + 2 + interface variables
        size_t const buffSize = this->bufferSizeForComm();
        size_t const materialBuffSize = _material.materialBufferSizeForComm(_element_set->dim());
        // counters
        size_t counterTJ, counterTJ_ElementaryResidual;

        // ************* PART I: Copy data from TJ to TJ_ElementaryResidual
        //               (for all the quadrature points)
        // Only DG flux (left_traction), displacement jump (uJump),
        // and left and right inRadius are copied

        // initialize the TJ_ElementaryResidual array
        for (quad_t q(0); q < nquad; ++q) {
            for (size_t a = 0; a < sizeTJElementaryResidual; a++) {
                TJ_ElementaryResidual[q * sizeTJElementaryResidual + a] = 0.0;
            }
        }

        // loop over quadrature points
        counterTJ = 0;
        counterTJ_ElementaryResidual = 0;
        for (quad_t q(0); q < nquad; ++q) {
            // Copy traction (DG flux), displacement jump, and left and right inRadius
            for (size_t a = 0; a < (sizeTraction + sizeJump + 2); a++) {
                TJ_ElementaryResidual[counterTJ_ElementaryResidual + a] = TJ[counterTJ + a];
            }
            counterTJ += buffSize;
            counterTJ_ElementaryResidual += sizeTJElementaryResidual;
        }

        // number of interface variables is zero
        // It is a purelly DG fully tied interface law
        if (materialBuffSize == 0) {
            // all done
            return;
        }

        // ************* PART II: Unpack the information stored in interface variables
        // The data unpacked is:
        //  - Traction to the left element: used in the calculation of interface constitutive law
        //  - normal to left element in deformed configuration: used in the calculation of interface
        //    constitutive law
        //  - number of times that failure criterion is satisfied at the interface:
        //    The possible values are: 2 (both left and right elements), 1 (only left or right
        //    element), 0 (none element)
        // buffers to store the data packed in the interfaceVariables (TJ) for all the quadrature
        // points left traction (computed with the First Piola-Kirchhoff stress tensor of the left
        // element)
       ElementQuadratureField<real> tractionLeft;
        tractionLeft.resize(1, nquad, sizeTraction);
        //    number of times that failure criterion is satisfied
       std::vector<int> numberTimesFailureCriterionIsSatisfied;
        numberTimesFailureCriterionIsSatisfied.resize(nquad);

        // initialize counters
        counterTJ = 0;
        counterTJ_ElementaryResidual = 0;
        // loop over quadrature points
        for (quad_t q(0); q < nquad; ++q) {
            // the data is stored in the corresponding arrays
            _material.UnpackBufferForCommunication(
              ndm, &numberTimesFailureCriterionIsSatisfied[q], tractionLeft.local(elem_t(0), q),
              &TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + sizeJump + 2 +
                                     sizeTraction + 2],
              &TJ[counterTJ + sizeTraction + sizeJump + 2]);
            // update counters
            counterTJ_ElementaryResidual += sizeTJElementaryResidual;
            counterTJ += buffSize;
        }

        // ************* PART III: Decide if fracture is initiated or not
        // array to store the flag that indicates if fracture has to be initiated
        bool* initiateFracture;
        initiateFracture = new bool[nquad];
        // avoid valgrind to complain about
        for (quad_t q(0); q < nquad; ++q) {
            initiateFracture[q] = false;
        }
        // MAYBE WE DON'T NEED THAT....
        // decide fracture initiation
        // the method DecideFractureInitiation(..) takes the decision based on the material input
        // parameters
        // the value of each component of initiateFracture can be true or false, and this value is
        // used to set the parameter coeff_TSL which activates the traction-separation-law
        // only decide crack in a converged configuration
        if (update) {
            _material.DecideFractureInitiation(nquad, &numberTimesFailureCriterionIsSatisfied[0],
                                               initiateFracture);
        }

        // ************* PART IV: Compute the traction for interface material
        // Traction calculated with Constitutive (i.e., traction from traction-separation-law) is
        // directly stored in TJ_ElementaryResidual
        // Coefficients for TSL and contact are also directly stored in TJ_ElementaryResidual

        // array to store internal variables
        size_t const internal_dim = _material.nInt();
       std::vector<real> internal_new;
        internal_new.resize(internal_dim);
        // array to store the jump divided by the jacobian
       std::vector<real> jumpDividedJacobian;
        jumpDividedJacobian.resize(sizeJump);
        // array to store the DG_flux divided by the jacobian
       std::vector<real> dgFluxDividedJacobian;
        dgFluxDividedJacobian.resize(sizeJump);

        // to store the jacobian and its inverse for a specific quadrature point
       real jac;
       real inv_jac;

        // pointer to local value of internal variables at previous time step
        real* internal;

        // loop over quadrature points
        counterTJ_ElementaryResidual = 0;
        for (quad_t q(0); q < nquad; ++q) {
            // local value of internal variables at previous time step
            internal = _internals->local(e, q);
            // copy to local Left internal variables
            std::copy(internal, internal + internal_dim, internal_new.begin());

            // inverse of jacobian (with Gauss quadrature weight)
            jac = _element_set->jac(e, q);
            inv_jac = 1.0 / jac;

            // the jump is multiplied by the inverse of the jacobian: this step is required because
            // the current jump is multiplied by the jacobian
            for (size_t i = 0; i < sizeJump; ++i) {
                jumpDividedJacobian[i] =
                  TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + i] * inv_jac;
            }

            // the DG_flux is multiplied by the inverse of the jacobian: this step is required
            // because the current DG_flux is multiplied by the jacobian
            for (size_t i = 0; i < sizeJump; ++i) {
                dgFluxDividedJacobian[i] =
                  TJ_ElementaryResidual[counterTJ_ElementaryResidual + i] * inv_jac;
            }

            // compute the constitutive law for the interface material
            // store traction from TSL
            // store coefficients for TSL activation and contact
            // send normal to left element in deformed configuration (it is not modified)
            _material.Constitutive(  // displacement jump
              &jumpDividedJacobian[0],
              // normal to left element (deformed configuration)
              &TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + sizeJump + 2 +
                                     sizeTraction + 2],
              // traction to the left element
              tractionLeft.local(elem_t(0), q),
              // DG_flux
              &dgFluxDividedJacobian[0],
              // coeffients for TSL activation and contact
              &TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + sizeJump + 2 +
                                     sizeTraction],
              // internal variables (can be modified)
              &internal_new[0],
              // traction computed with traction-separation-law
              &TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + sizeJump + 2],
              interface_tangent,
              // jacobian
              // (this variable is not used for anything => should be removed)
              0.0,
              // number of degree of freedom per node
              ndf,
              // spatial dimension
              ndm,
              // bool whether to compute tangents
              (interface_tangent ? true : false),
              // to indicate fracture initiation
              initiateFracture[q]);

            // Activate contact only for a converged state
            // (otherwise trouble for NCG in quasi-static problems)
            if (!update) {
                TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + sizeJump + 2 +
                                      sizeTraction + 1] = 0.;
            }

            // Compute the interface tangent if needed before increasing the pointer on TJ
            if (interface_tangent) {
                interface_tangent += ndm * ndm;
            }
            // the TSL is multiplied by the jacobian: this step is required because the traction
            // needed in ElementaryResidual has to be multiplied by the jacobian
            for (size_t i = 0; i < sizeTraction; ++i) {
                TJ_ElementaryResidual[counterTJ_ElementaryResidual + sizeTraction + sizeJump + 2 +
                                      i] *= jac;
            }

            // update history variable in internal variables
            if (timeUpdate) {
                _material.Update(&internal_new[0]);
            }

            // update if necessary
            if (update) {
                // local value of internal variables at previous time step
                real* internal_storage = _internals->local(e, q);

                // copy internal_new to internal
                for (size_t i = 0; i < size; ++i) {
                    // transfer component by component
                    internal_storage[position + i] = internal_new[position + i];
                }
            }

            // update counter
            counterTJ_ElementaryResidual += sizeTJElementaryResidual;
        }

        // delete array
        delete[] initiateFracture;
    }

    // all done
    return;
}


void summit::ReactionDiffusionInterfaceRegion::InterfaceEnergy(real& winter) const
{
    if (_element_set->side() == RIGHT or
        !_material.IsFracture())  // no account for the RIGHT to avoid double contribution in // ??
    {
        return;
    }

    int energyIndex;
    size_t ncomp;  // Should return 1... maybe other comp for plastic energy
    if (!_material.GetLocationInInternalTable("cohesive energy", energyIndex, ncomp)) {
        std::cout
          << "Cohesive energy is not registered for your material and thus it cannot be computed"
          << std::endl;
        exit(1);
    }

    const real* internals = _internals->local(elem_t(0), quad_t(0)) + energyIndex;
    size_t ninternal = _material.nInt();
    const real* jac = _element_set->jac()->local(elem_t(0), quad_t(0));

    for (elem_t e(0); e < _element_set->elements(); ++e) {
        for (quad_t q(0); q < _element_set->nquad(); ++q, ++jac, internals += ninternal) {
            winter += (*jac) * (*internals);
        }
    }

    return;
}

void summit::ReactionDiffusionInterfaceRegion::InterpolateAndSetPrimalVariableAsInternalVariable(
  NodalField<real> const& u)
{
    std::cout << "ERROR in "
                 "summit::ReactionDiffusionInterfaceRegion::"
                 "InterpolateAndSetPrimalVariableAsInternalVariable()"
              << "   --> this method has not been implemented yet" << std::endl;
    exit(1);

    // end of method
    return;
}

void summit::ReactionDiffusionInterfaceRegion::Interpolate(NodalField<real> const& nodal_field,
                                                   ElementQuadratureField<real>& quad_field) const
{
    // dimension of the nodal field
    const size_t ndim = nodal_field.dim();
    // check if the dimension of the nodal field is equivalent to the one of the quadrature field
    assert(ndim == quad_field.dim());
    // number of quadrature points per element
    size_t nquad = _element_set->nquad();
    // instantiate a local container
    size_t nen = _element_set->nodes_element();
    std::vector<real> local_nodal_field(ndim * nen);

    // loop over the elements constituting the region
    size_t nElements = _element_set->elements();
    for (elem_t e(0); e < nElements; ++e) {
        // extract local part of unknown
        _element_set->Localize(nodal_field, e, local_nodal_field);

        // loop over the quadrature points
        for (quad_t q(0); q < nquad; ++q) {
            real* local_quad_field = quad_field.local(e, q);
            // loop over the values of local quad field
            for (size_t i = 0; i < ndim; ++i) {
                local_quad_field[i] = 0.0;
                for (lnode_t a(0); a < nen; ++a) {
                    // Here, the out-of-interface shape function always contributes with 0
                    local_quad_field[i] +=
                      local_nodal_field[ndim * a + i] * _element_set->shape(e, q, a);
                }
            }
        }  // end of loop over the quadrature points
    }      // end of loop over the elements

    // end of method
    return;
}

bool summit::ReactionDiffusionInterfaceRegion::setBoundaryInternalVariableValue(
  InternalBoundary const& dB_I, PropertyValue_t const& propValues)
{
    // bool to say it worked or not
    bool didItWork = true;
    // get the number of integration points
    size_t nquad = _element_set->nquad();
    // position of the property in the internal variable container
    int position;
    // size of material property: scalar, vector, tensor
    size_t size;
    // loop over the entry in the map of property values
    for (PropertyValue_t::const_iterator it = propValues.begin(); it != propValues.end(); ++it) {
        // ask the material if he can process the current material property from the internal
        // variable and set the size which is passed by reference.
        bool gotIt = _material.GetLocationInInternalTable(it->first, position, size);
        // check if it worked
        didItWork &= gotIt;
        // if it does
        if (gotIt) {
            // loop over the mesh entities in the internal boundary
            for (Boundary::MeshEntityConstIterator_t meIt = dB_I.begin(); meIt != dB_I.end();
                 ++meIt) {
                // try to find the current global element id in the map
                ElementSet::mapGlobalToLocalConstIt_t mIt =
                  _element_set->globalToLocalElementMap().find((*meIt)->index());
                // if the element of current global index is found in the element set
                if (mIt != _element_set->globalToLocalElementMap().end()) {
                    // loop over quadrature points
                    for (quad_t q(0); q < nquad; ++q) {
                        // grab the local value of internal variables at the current quadratue point
                        real* internal = _internals->local(elem_t(mIt->second), q);
                        // transfer the material property to the current internal variable
                        for (size_t i = 0; i < size; ++i) {
                            // set the value component by component
                            internal[position + i] = it->second;
                        }
                    }
                }
            }
        }
    }

    // all done
    return didItWork;
}

void summit::ReactionDiffusionInterfaceRegion::getMapFromMeshToRegionIndexing(
  InternalBoundary const& dB_I, std::map<int, int>& meshToRegionIndexing) const
{
    // loop over the mesh entities in the internal boundary
    for (Boundary::MeshEntityConstIterator_t meIt = dB_I.begin(); meIt != dB_I.end(); ++meIt) {
        // try to find the current global element id in the map
        ElementSet::mapGlobalToLocalConstIt_t mIt =
          _element_set->globalToLocalElementMap().find((*meIt)->index());
        // if the element of current global index is found in the element set
        if (mIt != _element_set->globalToLocalElementMap().end()) {
            // fill the map
            meshToRegionIndexing[(*meIt)->index()] = mIt->second;
        }
    }

    // all done
    return;
}

const summit::Material* summit::ReactionDiffusionInterfaceRegion::material() const { return &_material; }

summit::Region::IVsheet_t summit::ReactionDiffusionInterfaceRegion::getInternalVariableFields(
  std::string const& name) const
{
    // position of the property in the internal variable container
    int position;
    // size of material property: scalar, vector, tensor
    size_t size;
    // ask the material if he can process the current material property from the internal
    // variable and set the size which is passed by reference.
    bool gotIt = _material.GetLocationInInternalTable(name, position, size);

    // all done
    return std::make_tuple(gotIt, position, size, _internals);
}

summit::Region::IVsheet_t summit::ReactionDiffusionInterfaceRegion::getSideInternalVariableFields(
  std::string const& name, SIDE side) const
{
    // position of the property in the internal variable container
    int position;
    // size of material property: scalar, vector, tensor
    size_t size;
    // ask the material if he can process the current material property from the internal
    // variable and set the size which is passed by reference.
    bool gotIt = false;
    switch (side) {
        case LEFT:
            gotIt = _material_L.GetLocationInInternalTable(name, position, size);
            return std::make_tuple(gotIt, position, size, _internals_L);
            break;
        case RIGHT:
            gotIt = _material_L.GetLocationInInternalTable(name, position, size);
            return std::make_tuple(gotIt, position, size, _internals_L);
            break;
        default:
            std::cout << "Error in ReactionDiffusionInterfaceRegion : cannot create a side of type: " << side
                    << std::endl;
            exit(1);
            return std::make_tuple(false, position, size, _internals_L);
            break;
    }
    // all done
}


void summit::ReactionDiffusionInterfaceRegion::setSideInternalVariableFields(
  Region::IVsheet_t const& ivField,
  std::string const& name, SIDE side)
{
    // position of the property in the solid internal variable container
    int position;
    // size of material property: scalar, vector, tensor; for the solid
    size_t size;
    switch (side) {
        case LEFT:{
            bool solidGotIt = _material_L.GetLocationInInternalTable(name, position, size);
            // I dont understand why this is a compilation error?
            bool GotIt = std::get<0>(ivField);
            if (!GotIt) {
                // say something useful
                std::cout
                << "ReactionDiffusionInterfaceRegion::setInternalVariableFields: ivField does not have the information you wanted"
                << std::endl;
                // complain
                throw std::logic_error("Bad Transfer");
            }
            // local position in the container of internal variable
            int Position = std::get<1>(ivField);
            // local size of internal variable
            size_t Size = (size_t)(std::get<2>(ivField));
            if (Size != size) {
                // say something useful
                std::cout << "ReactionDiffusionInterfaceRegion::setSideInternalVariableFields: trying to pass\n"
                        << "data of different size" << std::endl;
                // complain
                throw std::logic_error("Inconsistent Sizes");
            }
            // loop over the elements of the element set
            for (elem_t e(0); e < this->element_set()->elements(); ++e) {
                for (quad_t q(0); q < this->element_set()->nquad(); ++q) {
                    // local value of internal variables
                    real* internal = _internals_L->local(e, q);
                    // local value of internal variables
                    real const* Internal = std::get<3>(ivField)->local(e, q);
                    // loop over the size
                    for (size_t j = 0; j < size; ++j) {
                        internal[position + j] = Internal[Position + j];
                    }
                }
            }
            return;
        }
        case RIGHT:{
            bool solidGotIt = _material_L.GetLocationInInternalTable(name, position, size);
            // I dont understand why this is a compilation error?
            bool GotIt = std::get<0>(ivField);
            if (!GotIt) {
                // say something useful
                std::cout
                << "ReactionDiffusionInterfaceRegion::setInternalVariableFields: ivField does not have the information you wanted"
                << std::endl;
                // complain
                throw std::logic_error("Bad Transfer");
            }
            // local position in the container of internal variable
            int Position = std::get<1>(ivField);
            // local size of internal variable
            size_t Size = (size_t)(std::get<2>(ivField));
            if (Size != size) {
                // say something useful
                std::cout << "ReactionDiffusionInterfaceRegion::setSideInternalVariableFields: trying to pass\n"
                        << "data of different size" << std::endl;
                // complain
                throw std::logic_error("Inconsistent Sizes");
            }
            // loop over the elements of the element set
            for (elem_t e(0); e < this->element_set()->elements(); ++e) {
                for (quad_t q(0); q < this->element_set()->nquad(); ++q) {
                    // local value of internal variables
                    real* internal = _internals_L->local(e, q);
                    // local value of internal variables
                    real const* Internal = std::get<3>(ivField)->local(e, q);
                    // loop over the size
                    for (size_t j = 0; j < size; ++j) {
                        internal[position + j] = Internal[Position + j];
                    }
                }
            }
            return;
        }
        default:
            std::cout << "Error in ReactionDiffusionInterfaceRegion : cannot create a side of type: " << side
                    << std::endl;
            exit(1);
            return;
    }
}

void summit::ReactionDiffusionInterfaceRegion::setInternalVariableFields(
  std::vector<Region::IVsheet_t> const& ivField,
  std::string const& name,
  transferMap_t const& transMap)
{
    // position of the property in the solid internal variable container
    int solidPosition;
    // size of material property: scalar, vector, tensor; for the solid
    size_t solidSize;
    // ask the material if he can process the current material property from the internal
    // variable and set the size which is passed by reference.
    bool solidGotIt = _material.GetLocationInInternalTable(name, solidPosition, solidSize);

    if (solidGotIt) {
        if (transMap.size() != ivField.size()) {
            // say something useful
            std::cout << "ReactionDiffusionInterfaceRegion::setInternalVariableFields: transMap and\n"
                      << "ivField have different sizes! transMap.size() = " << transMap.size()
                      << " and\n"
                      << "ivField.size() = " << ivField.size() << std::endl;
            // complain
            throw std::logic_error("Inconsistent Sizes");
        }
        // loop over the ivField and the transMap
        for (size_t i = 0; i < transMap.size(); ++i) {
            // was the information correctly collected
            bool fluidGotIt = std::get<0>(ivField[i]);
            // if not
            if (!fluidGotIt) {
                // say something useful
                std::cout << "ReactionDiffusionInterfaceRegion::setInternalVariableFields: the solid\n"
                          << "side does not have the information you wanted" << std::endl;
                // complain
                throw std::logic_error("Bad Transfer");
            }
            // local position in the container of solid internal variable
            int fluidPosition = std::get<1>(ivField[i]);
            // local size of solid internal variable
            size_t fluidSize = (size_t)(std::get<2>(ivField[i]));
            if (fluidSize != solidSize) {
                // say something useful
                std::cout << "ReactionDiffusionInterfaceRegion::setInternalVariableFields: trying to pass\n"
                          << "data of different size from fluid (size = " << fluidSize << ") to\n"
                          << "to solid (size = " << solidSize << ")" << std::endl;
                // complain
                throw std::logic_error("Inconsistent Sizes");
            }
            // loop over the current map in transmap
            for (std::map<int, int>::const_iterator it = transMap[i].begin();
                 it != transMap[i].end(); ++it) {
                // solid element index
                elem_t solidElmId(it->first);
                // fluid element index
                elem_t fluidElmId(it->second);
                for (quad_t q(0); q < _element_set->nquad(); ++q) {
                    // local value of solid internal variables
                    real* solidInternal = _internals->local(solidElmId, q);
                    // local value of fluid internal variables
                    real const* fluidInternal = std::get<3>(ivField[i])->local(fluidElmId, q);
                    // loop over the size
                    for (size_t j = 0; j < solidSize; ++j) {
                        solidInternal[solidPosition + j] = fluidInternal[fluidPosition + j];
                    }
                }
            }
        }
    }

    // all done
    return;
}

const summit::real summit::ReactionDiffusionInterfaceRegion::computeAreaForInternalVariable(
  std::string const& ivName, const real ivRefValue)
{
    // region area
    real area = 0.0;

    // retrieve the position of the internal variables containing the sought data.
    int ivPosition;
    size_t ivSize;  // known to be one here but still needed in the arguments
    bool ivExists = _material.GetLocationInInternalTable(ivName, ivPosition, ivSize);

    if (!ivExists) {
        std::string msg;
        msg = "material model does not contain \'" + ivName + "\' as internal variable";
        throw std::runtime_error(msg);
    }

    // loop over the elements of the element set
    for (elem_t e(0); e < _element_set->elements(); ++e) {
        // integrate over quadrature points
        for (quad_t q(0); q < _element_set->nquad(); ++q) {
            real ivValue = _internals->local(e, q)[ivPosition];

            if (fabs(ivValue - ivRefValue) <= 1.0e-13) {
                // integrate over quadrature points
                for (quad_t q(0); q < _element_set->nquad(); ++q) {
                    // get jacobian (already contains quadrature point weight)
                    real jac = _element_set->jac(e, q);

                    // accumulate quadrature point contribution
                    area += jac;
                }

                break;
            }
        }
    }

    // end of method
    return area;
}

/* Backup until it is verified that the computeAreaForInternalVariable way of computing this works
ok
const summit::real summit::ReactionDiffusionInterfaceRegion::computeCrackedArea()
{
    // region area
    real area = 0.0;

    // retrieve the position of the internal variables containing the sought data: interface damage
    int damagePosition;
    size_t damageSize;  // known to be one here but still needed in the arguments
    bool damageExists =
      _material.GetLocationInInternalTable("interface damage", damagePosition, damageSize);

    if (!damageExists) {
        throw std::runtime_error(
          "material model does not contain 'interface damage' as internal variable");
    }

    // loop over the elements of the element set
    for (elem_t e(0); e < _element_set->elements(); ++e) {
        // integrate over quadrature points
        for (quad_t q(0); q < _element_set->nquad(); ++q) {
            real damage = _internals->local(e, q)[damagePosition];

            if (fabs(damage - 1.0) <= 1.0e-13) {
                // integrate over quadrature points
                for (quad_t q(0); q < _element_set->nquad(); ++q) {
                    // get jacobian (already contains quadrature point weight)
                    real jac = _element_set->jac(e, q);

                    // accumulate quadrature point contribution
                    area += jac;
                }

                break;
            }
        }
    }

    // end of method
    return area;
}
*/

const summit::real summit::ReactionDiffusionInterfaceRegion::computeCrackedVolume()
{
    // region volume
    real vol = 0.0;

    // retrieve the position of the internal variables containing the sought data: interface damage
    int damagePosition;
    size_t damageSize;  // known to be one here but still needed in the arguments
    bool damageExists =
      _material.GetLocationInInternalTable("interface damage", damagePosition, damageSize);

    if (!damageExists) {
        throw std::runtime_error(
          "material model does not contain 'interface damage' as internal variable");
    }

    // retrieve the position of the internal variables containing the sought data: normal fracture
    // opening
    int openingPosition;
    size_t openingSize;  // known to be one here but still needed in the arguments
    bool openingExists =
      _material.GetLocationInInternalTable("normal fracture opening", openingPosition, openingSize);

    if (!openingExists) {
        throw std::runtime_error(
          "material model does not contain 'normal fracture opening' as internal variable");
    }

    // loop over the elements of the element set
    for (elem_t e(0); e < _element_set->elements(); ++e) {
        // integrate over quadrature points
        for (quad_t q(0); q < _element_set->nquad(); ++q) {
            real damage = _internals->local(e, q)[damagePosition];
            real opening = _internals->local(e, q)[openingPosition];

            if (fabs(damage - 1.0) <= 1.0e-13) {
                // integrate over quadrature points
                for (quad_t q(0); q < _element_set->nquad(); ++q) {
                    // get jacobian (already contains quadrature point weight)
                    real jac = _element_set->jac(e, q);

                    // accumulate quadrature point contribution
                    vol += opening * jac;
                }

                break;
            }
        }
    }

    // end of method
    return vol;
}

void summit::ReactionDiffusionInterfaceRegion::getGlobalIdOfCrackedElement(
  real normalFractureOpeningThreshold, std::vector<int>& crackedElements) const
{
    // get the number of integration points
    size_t nquad = _element_set->nquad();
    // position of the internal variable for the interface damage
    int posDamage;
    // size of the internal variable for the interface damage
    size_t sizDamage;
    // ask the material if he knows interface damage and set the size which is passed by reference.
    bool gotDamage = _material.GetLocationInInternalTable("interface damage", posDamage, sizDamage);
    // position of the internal variable for the normal fracture opening
    int posOpening;
    // size of the internal variable for the normal fracture opening
    size_t sizOpening;
    // ask the material if he knows normal fracture opening and set the size which is passed by
    // reference.
    bool gotOpening =
      _material.GetLocationInInternalTable("normal fracture opening", posOpening, sizOpening);

    // if the internal variables are not scalar
    if (sizDamage != 1 || sizOpening != 1) {
        // say something useful
        std::cout << "ReactionDiffusionInterfaceRegion::getGlobalIdOfCrackedElement, don't know how to\n"
                  << "process non-scalar internal variables: 'interface damage' and\n"
                  << "'normal fracture opening'" << std::endl;
        // complain
        throw std::logic_error("Not Implemented");
    }
    // if the material does not know about this
    if (!gotDamage || !gotOpening) {
        // send a warning
        std::cout << "ReactionDiffusionInterfaceRegion::getGlobalIdOfCrackedElement try to get crack\n"
                  << "element global indices from a region with a material that knows neither\n"
                  << "the key word 'interface damage' nor the key word 'normal fracture opening'"
                  << std::endl;
    }
    else {
        // number of quadrature points testing positive
        size_t nPosTestQuads = 0;
        // loop over the elements
        for (ElementSet::mapGlobalToLocalConstIt_t mIt =
               _element_set->globalToLocalElementMap().begin();
             mIt != _element_set->globalToLocalElementMap().end(); ++mIt) {
            // set the number of quad point testing positive to zero for this new element
            nPosTestQuads = 0;
            // loop over quadrature points
            for (quad_t q(0); q < nquad; ++q) {
                // grab the local value of internal variables at the current quadratue
                // point
                real* internal = _internals->local(elem_t(mIt->second), q);
// test
#if 0
                            if ( (fabs(internal[posDamage] - 1.0) < 1.0e-5) &&
                                 (internal[posOpening] >= normalFractureOpeningThreshold) )
#endif
                if (fabs(internal[posDamage] - 1.0) < 1.0e-5) {
                    ++nPosTestQuads;
                }
            }
            // if all the quad points test positive
            if (nPosTestQuads == nquad) {
                // then, the element is cracked and should be added to the list
                crackedElements.push_back(mIt->first);
            }
        }
    }

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegion::getGlobalFaceFractureStatus(
  std::vector<bool>& globalFaceIsFractured, real normalFractureOpeningThreshold) const
{
    // get the number of integration points
    size_t nquad = _element_set->nquad();
    // position of the internal variable for the interface damage
    int posDamage;
    // size of the internal variable for the interface damage
    size_t sizDamage;
    // ask the material if he knows interface damage and set the size which is passed by reference.
    bool gotDamage = _material.GetLocationInInternalTable("interface damage", posDamage, sizDamage);
    // position of the internal variable for the normal fracture opening
    int posOpening;
    // size of the internal variable for the normal fracture opening
    size_t sizOpening;
    // ask the material if he knows normal fracture opening and set the size which is passed by
    // reference.
    bool gotOpening =
      _material.GetLocationInInternalTable("normal fracture opening", posOpening, sizOpening);

    // if the internal variables are not scalar
    if (sizDamage != 1 || sizOpening != 1) {
        // say something useful
        std::cout << "ReactionDiffusionInterfaceRegion::getGlobalIdOfCrackedElement, don't know how to\n"
                  << "process non-scalar internal variables: 'interface damage' and\n"
                  << "'normal fracture opening'" << std::endl;
        // complain
        throw std::logic_error("Not Implemented");
    }
    // if the material does not know about this
    if (!gotDamage || !gotOpening) {
        // send a warning
        std::cout << "ReactionDiffusionInterfaceRegion::getGlobalIdOfCrackedElement try to get crack\n"
                  << "element global indices from a region with a material that knows neither\n"
                  << "the key word 'interface damage' nor the key word 'normal fracture opening'"
                  << std::endl;
    }
    else {
        // number of quadrature points testing positive
        size_t nPosTestQuads = 0;

        const std::vector<int>& globalFaceId = _element_set->globalElementId();
        // loop over the elements
        for (elem_t e(0); e < _element_set->elements(); ++e) {
            // set the number of quad point testing positive to zero for this new element
            nPosTestQuads = 0;
            // loop over quadrature points
            for (quad_t q(0); q < nquad; ++q) {
                // grab the local value of internal variables at the current quadratue
                // point
                real* internal = _internals->local(e, q);
                // test
#if 0
		if ( (fabs(internal[posDamage] - 1.0) < 1.0e-5) &&
		     (internal[posOpening] >= normalFractureOpeningThreshold) )
#endif
                if (fabs(internal[posDamage] - 1.0) < 1.0e-5) {
                    ++nPosTestQuads;
                }
            }
            // if all the quad points test positive
            if (nPosTestQuads == nquad) {
                // then, the element is cracked and should be added to the list
                int gFaceId = globalFaceId[e];
                globalFaceIsFractured[gFaceId] = true;
            }
        }
    }

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegion::SetMaterialParameters(
  PropertySheet_t const& sheet, CommunicationManager const& commManager, Mesh const& mesh)
{
    // get the number of integration points
    size_t nquad = _element_set->nquad();
    // loop over the PropertySheet_t
    for (PropertySheet_t::const_iterator pIt = sheet.begin(); pIt != sheet.end(); ++pIt) {
        // position of the property in the internal variable container for the left material
        int leftPosition;
        // size of material property: scalar, vector, tensor; for the left material
        size_t leftSize;
        // ask the left material if he can process the current material property from the internal
        // variable
        bool leftGotIt = _material_L.GetLocationInInternalTable(pIt->first, leftPosition, leftSize);

        // position of the property in the internal variable container for the left material
        int position;
        // size of material property: scalar, vector, tensor; for the left material
        size_t size;
        // ask the interface material if it can process the current material property from
        // the internal variable
        bool gotIt = _material.GetLocationInInternalTable(pIt->first, position, size);

        if (leftGotIt) {
            // Here, without knowledge of all the element across all the partitions,
            // it is impossible to do size checking
            // then, build the vector of global element index
            std::vector<int> globId;
            _element_set->globalId(globId);
            // container for the left properties of the current element
            std::vector<real> leftElmProp(leftSize);
            // loop over the elements in the element set
            for (elem_t f(0); f < _element_set->elements(); ++f) {
                // create a mesh entity (most likely a face)
                MeshEntity me(mesh, mesh.dim() - 1, globId[f]);
                // grab its coboundary
                std::vector<int> const& cob = me.OrientedCoBoundary();
                // index of the left bulk element
                int leftBulkId = abs(cob[0]) - 1;
                // fill the left containers with the values in the property sheet
                for (size_t i = 0; i < leftSize; ++i) {
                    // fill each component of the current property with the value corresponding to
                    // the global element id in the property sheet
                    leftElmProp[i] = pIt->second[leftBulkId * leftSize + i];
                }
                // loop over quadrature points
                for (quad_t q(0); q < nquad; ++q) {
                    // grab the local value of the left internal variables at the current
                    // quadrature point
                    real* leftInternal = _internals_L->local(f, q);
                    real* field_local = _internals_avg->local(f, q);
                    // transfer the material property to the current internal variable
                    for (size_t i = 0; i < leftSize; ++i) {
                        // transfer component by component left ...
                        leftInternal[leftPosition + i] = leftElmProp[i];
                        field_local[leftPosition + i] = 0.5 * leftElmProp[i];
                    }
                }
            }
        }
        else {
            if (gotIt) {
                // say something usefull
                std::cout
                  << "WARNING: In ReactionDiffusionInterfaceRegion::SetMaterialParameters with a sheet\n"
                  << "as argument. The interface material model is parametric\n"
                  << "and the bulk material model is not.\n"
                  << "(" << pIt->first << ").\n"
                  << std::endl;
            }
        }
        // compute the elastic left tangent modulus
        std::vector<real> P_L_new(_strains_L->dim());
        std::vector<real> f_L_new(_F_L->dim());
        std::vector<real> F_L_new(_strains_L->dim());
        std::vector<real> concentration_L(_dof_node);
        std::vector<real> concentration_L0(_dof_node);
        std::vector<real> internals_L_new(_internals_L->dim());
        std::vector<real> ul(_element_set->nodes_element() * _dof_node);

        for (elem_t e(0); e < _element_set->elements(); ++e) {
            for (quad_t q(0); q < _element_set->nquad(); ++q) {
                LeftConstitutiveUpdate(e, q, ul, ul, concentration_L, concentration_L0, 0.0, P_L_new, f_L_new, F_L_new, internals_L_new,
                                       _C_L.data(), _dPdU_L.data(), _dF_L.data());

                for (size_t i = 0; i < _CField_L->dim(); ++i) {
                    _CField_L->local(e, q)[i] = _C_L[i];
                }
                for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
                    _dPdUField_L->local(e, q)[i] = _dPdU_L[i] + _dF_L[i];
                }
                for (size_t i = 0; i < _dFField_L->dim(); ++i) {
                    _dFField_L->local(e, q)[i] = _dF_L[i];
                }
            }
        }
    }

    if (_usingFullStabilizationTerm)
        _computeAveragedFULLStabilizationCoefficientLeft();
    else
        _computeAveragedStabilizationCoefficientLeft();

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegion::SetMaterialParameters(
  std::string const& name,
  Functor<real> const& functor,
  CommunicationManager const& commManager,
  Mesh const& mesh)
{
    // get the number of integration points
    size_t nquad = _element_set->nquad();
    // position of the property in the internal variable container for the left material
    int leftPosition;
    // size of material property: scalar, vector, tensor; for the left material
    size_t leftSize;
    // ask the left material if he can process the current material property from the internal
    // variable
    bool leftGotIt = _material_L.GetLocationInInternalTable(name, leftPosition, leftSize);

    // position of the property in the internal variable container for the left material
    int position;
    // size of material property: scalar, vector, tensor; for the left material
    size_t size;
    // ask the interface material if it can process the current material property from
    // the internal variable
    bool gotIt = _material.GetLocationInInternalTable(name, position, size);

    if (leftGotIt) {
        // Here, without knowledge of all the element across all the partitions,
        // it is impossible to do size checking
        // then, build the vector of global element index
        std::vector<int> globId;
        _element_set->globalId(globId);
        // container for the left properties of the current element
        std::vector<real> leftElmProp(leftSize);
        // loop over the elements in the element set
        for (elem_t f(0); f < _element_set->elements(); ++f) {
            // create a mesh entity (most likely a face)
            MeshEntity me(mesh, mesh.dim() - 1, globId[f]);
            // grab its coboundary
            std::vector<int> const& cob = me.OrientedCoBoundary();
            // index of the left bulk element
            int leftBulkId = abs(cob[0]) - 1;
            // create a mesh entity
            MeshEntity leftBulk(mesh, mesh.dim(), leftBulkId);
            // ~> allocate memory for centroid cartesian coordinates array
            std::vector<real> leftBulkcentroid(mesh.dim(), 0.0);
            for (int i = 0; i < mesh.dim(); i++) {
                leftBulkcentroid[i] = leftBulk.centroid()[i];
            }
            // fill the left containers with the values in the property sheet
            for (size_t i = 0; i < leftSize; ++i) {
                // fill each component of the current property with the value corresponding to
                // the global element id in the property sheet
                leftElmProp[i] = functor.get(leftBulkcentroid)[i];
            }
            // loop over quadrature points
            for (quad_t q(0); q < nquad; ++q) {
                // grab the local value of the left internal variables at the current
                // quadrature point
                real* leftInternal = _internals_L->local(f, q);
                real* field_local = _internals_avg->local(f, q);
                // transfer the material property to the current internal variable
                for (size_t i = 0; i < leftSize; ++i) {
                    // transfer component by component left ...
                    leftInternal[leftPosition + i] = leftElmProp[i];
                    field_local[leftPosition + i] = 0.5 * leftElmProp[i];
                }
            }
        }
    }
    else {
        if (gotIt) {
            // say something usefull
            std::cout
              << "WARNING: In ReactionDiffusionInterfaceRegion::SetMaterialParameters with a functor\n"
              << "as argument. The interface material model is parametric\n"
              << "and the bulk material model is not.\n"
              << "(" << name << ").\n"
              << std::endl;
        }
    }

    // compute the elastic left tangent modulus
    std::vector<real> P_L_new(_strains_L->dim());
    std::vector<real> F_L_new(_strains_L->dim());
    std::vector<real> f_L_new(_F_L->dim());
    std::vector<real> concentration_L(_dof_node);
    std::vector<real> concentration_L0(_dof_node);
    std::vector<real> internals_L_new(_internals_L->dim());
    std::vector<real> ul(_element_set->nodes_element() * _dof_node);

    for (elem_t e(0); e < _element_set->elements(); ++e) {
        for (quad_t q(0); q < _element_set->nquad(); ++q) {
            LeftConstitutiveUpdate(e, q, ul, ul, concentration_L0, concentration_L, 0.0, P_L_new, f_L_new, F_L_new, internals_L_new, _C_L.data(), _dPdU_L.data(), _dF_L.data());

            // stiffness scaling of the stabilization parameter
            for (size_t i = 0; i < _CField_L->dim(); ++i) {
                _CField_L->local(e, q)[i] = _C_L[i];
            }
            for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
                _dPdUField_L->local(e, q)[i] = _dPdU_L[i] + _dF_L[i];
            }
            for (size_t i = 0; i < _dFField_L->dim(); ++i) {
                _dFField_L->local(e, q)[i] = _dF_L[i];
            }
        }
    }

    if (_usingFullStabilizationTerm)
        _computeAveragedFULLStabilizationCoefficientLeft();
    else
        _computeAveragedStabilizationCoefficientLeft();

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegion::SetMaterialParameters(std::string const& name)
{
    // get the number of integration points
    size_t nquad = _element_set->nquad();
    // position of the property in the internal variable container for the left material
    int leftPosition;
    // size of material property: scalar, vector, tensor; for the left material
    size_t leftSize;
    // ask the left material if he can process the current material property from the internal
    // variable
    bool leftGotIt = _material_L.GetLocationInInternalTable(name, leftPosition, leftSize);

    // position of the property in the internal variable container for the left material
    int position;
    // size of material property: scalar, vector, tensor; for the left material
    size_t size;
    // ask the interface material if it can process the current material property from
    // the internal variable
    bool gotIt = _material.GetLocationInInternalTable(name, position, size);

    if (leftGotIt) {
        // found it
        if (gotIt) {
            // if the leftSize and the size are different
            if (leftSize != size) {
                // say something usefull
                std::cout << "In ReactionDiffusionInterfaceRegion::SetMaterialParameters\n"
                          << "The buffer sizes of the material parameter for the\n"
                          << "left side and the interface do not match.\n"
                          << "leftSize = " << leftSize << " and size = " << size << std::endl;
                // complain
                throw std::logic_error("Inconsistent Sizes");
            }
            // loop over the elements in the element set
            for (elem_t f(0); f < _element_set->elements(); ++f) {
                // loop over quadrature points
                for (quad_t q(0); q < nquad; ++q) {
                    // grab the local value of the interface internal variables at the current
                    // quadratue point
                    real const* field_local = _internals_avg->local(f, q);
                    real* internal = _internals->local(f, q);
                    for (size_t i = 0; i < leftSize; ++i) {
                        // set the mean value where it should go
                        internal[position + i] = field_local[leftPosition + i];
                    }
                }
            }
        }
    }
    else {
        if (gotIt) {
            // say something usefull
            std::cout << "WARNING: In ReactionDiffusionInterfaceRegion::SetMaterialParameters\n"
                      << "The interface material model is parametric\n"
                      << "and the bulk material model is not.\n"
                      << "(" << name << ").\n"
                      << std::endl;
        }
    }

    // all done
    return;
}

summit::ElementQuadratureField<real>* summit::ReactionDiffusionInterfaceRegion::internals_avg()
{
    return _internals_avg;
}

summit::ElementQuadratureField<real>* summit::ReactionDiffusionInterfaceRegion::internalVariables()
{
    return _internals;
}

void summit::ReactionDiffusionInterfaceRegion::getCField_L(ElementQuadratureField<real>* Cijkl)
{
    size_t const nelem = _element_set->elements();
    size_t const nquads = _element_set->nquad();
    for (elem_t e(0); e < nelem; ++e) {
        for (quad_t q(0); q < nquads; ++q) {
            for (size_t i = 0; i < Cijkl->dim(); ++i) {
                Cijkl->local(e, q)[i] = ((_bulkC_L.second)->local(e, q))[i];
            }
        }
    }
    return;
}

void summit::ReactionDiffusionInterfaceRegion::_uncrackedIntegrand(
  elem_t e,
  quad_t q,
  NodalField<real> const& u,
  const real* left_traction,
  const real* jumpU,
  real* interface_tangent,
  const real* averg_hs_C_q,
  std::vector<real>& rl,
  std::vector<real>& Kl,
  summit::real const* missingCijkl,
  summit::real const* missingdPdU,
  summit::real const* missingShapeAndDerivative)
{
    std::vector<real> my_damage(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q), my_damage.data());
    std::vector<int> my_types(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceTypes(_internals->local(e, q), my_types.data());
    std::vector<real> my_values(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceValues(_internals->local(e, q), my_values.data());

    real StabFactor = 1.0;//  - (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q));
    real ComFactor = 1.0;//  - (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q));
    real ConFactor = 1.0;//  - (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q));

   std::vector<real> ul;
    ul.resize(_element_set->nodes_element() * _dof_node);
    _element_set->Localize(u, e, ul);
    std::vector<real> leftConcentration(_dof_node);
    std::fill(leftConcentration.begin(), leftConcentration.end(), 0.);
    for (lnode_t b = _element_set->leftNodeStart(); b != _element_set->leftNodeDone(); ++b) {
        for (int c = 0; c < _dof_node; c++) {
            leftConcentration[c] += _element_set->shape(e, q, b) * ul[c + b * _dof_node];
        }
    }
    // Here, in assemblying the stiffness, we assume that the side of the half butterfly is
    // summit::LEFT.
    // Indeed, only the left part of the half butterfly (lower processor id) gets to assemble
    // its contribution to the global stiffness.
    // The 'missing pieces' to compute the elementary contributions are provided by
    //  missingShapeAndDerivative -> shape functions and derivatives of the other half
    //  missingCijkl -> tangent modulus of the other half
    // Finally, the iteration on the right-hand nodes is done by using the left-hand counter
    // loop and translating the loop indices of nen (which is the number of elements of the
    // half butterfly)
    // However, notice that the contributions to the elementary residual are assembled to the global
    // residual by both half butterflies and the rule for assemblying these contributions follows
    // that of _residualIntegrand (namely, assemble right hand piece and multiply by _amIRight to
    // get the correct sign).

    // number of element nodes in one element (half butterfly)
    size_t const nen = _element_set->nodes_element();

    // spatial dimension
    size_t const spatial_dim = _element_set->dim();

    // size of the local residual: this is double since this method is used for GHOST full butterfly
    int const residual_dim = 2 * _element_set->nodes_element() * _dof_node;

    // Elastic tangent modulus
    const real* _bulkC_R = &missingCijkl[q * spatial_dim * _dof_node * spatial_dim * _dof_node];
    const real* _bulkdPdU_R = &missingdPdU[q * spatial_dim * _dof_node * _dof_node];

    // loop over the nodes of the left element -> test function
    for (lnode_t c = _element_set->leftNodeStart(); c != _element_set->leftNodeDone(); ++c) {
        const real test_u = _element_set->shape(e, q, c);
        const real* test_du = _element_set->dShape(e, q, c);
        // loop over its degrees of freedom
        for (size_t i = 0; i < _dof_node; ++i) {
            // compute the index of the current dof in the local displacement vector
            size_t ci = _dof_node * c + i;

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling r^L
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            //
            // Compute the "left" interface residual
            //
            // Here, we compute MINUS the internal forces because the assemble function only
            // adds to the residual: residual = f^ext - f^int
            // update the consistency term
            rl[ci] -=
              _amIRight * ConFactor * (1.0 - my_damage[i]) *
              TractionResidualTerm<summit::RIGHT>(e, q, test_u, i, left_traction);
            // update the compatibility term
#if IMR_COMPATIBILITY
            rl[ci] -= ComFactor * (1.0 - my_damage[i]) *
              CompatibilityResidualTerm(e, q, test_du, test_u, i, jumpU, (_bulkC_L.second)->local(e, q), _kappa_ref_L.data());
#endif  // IMR_COMPATIBILITY
        // update the penalty term
            if (_usingFullStabilizationTerm) {
                rl[ci] -= StabFactor * (1.0 - my_damage[i]) * _amIRight * StabilizationResidualTermFULL<summit::RIGHT>(
                                        e, q, test_u, i, jumpU, averg_hs_C_q);
            }
            else {
                rl[ci] -= StabFactor * (1.0 - my_damage[i]) * _amIRight * StabilizationResidualTerm<summit::RIGHT>(
                                        e, q, test_u, i, jumpU, averg_hs_C_q[i]);
            }
            // update the cohesive law
            if(my_types[i]==0){
                rl[ci] -= _amIRight * my_damage[i] *
                          StabilizationResidualTerm<summit::RIGHT>(e, q, test_u, i, jumpU, my_values[i]);
            }
            else{
                real myJump = _amIRight * (leftConcentration[i] - my_values[i]);
                myJump *= _element_set->jac(e, q);
                // std::cout << "myJump: " << myJump <<std::endl;
                rl[ci] -= _amIRight * my_damage[i] *
                          StabilizationResidualTerm<summit::RIGHT>(e, q, test_u, 0, &myJump, averg_hs_C_q[i]);
            }


            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^LL
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // loop over the nodes of the left element -> displacement
            for (lnode_t a = _element_set->leftNodeStart(); a != _element_set->leftNodeDone();
                 ++a) {
                const real disp_u = _element_set->shape(e, q, a);
                const real* disp_du = _element_set->dShape(e, q, a);
                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t ak = _dof_node * a + k;
                    // compute the index of the current entry in the local stiffness array
                    size_t ciak = ci * residual_dim + ak;
                    // update the consistency term
                    Kl[ciak] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentMatrixTerm<summit::LEFT>(
                      e, q, test_u, i, disp_du, k, (_bulkC_L.second)->local(e, q));

                    Kl[ciak] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentSecondMatrixTerm<summit::LEFT>(
                      e, q, test_u, i, disp_u, k, (_bulkdPdU_L.second)->local(e, q));
                    // update the compatibility term
#if IMR_COMPATIBILITY
                    Kl[ciak] += ComFactor * (1.0 - my_damage[i]) * CompatibilityTangentMatrixTerm<summit::LEFT>(
                      e, q, test_du, i, disp_u, k, (_bulkC_L.second)->local(e, q), _kappa_ref_L.data());
#endif  // IMR_COMPATIBILITY

                    // update the penalty term
                    if (_usingFullStabilizationTerm) {
                        Kl[ciak] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTermFULL<summit::LEFT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C_q);
                    }
                    else {
                        Kl[ciak] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTerm<summit::LEFT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C_q[i]);
                    }
                    if(my_types[i]==0){
                        Kl[ciak] += my_damage[i] * StabilizationTangentMatrixTerm<summit::LEFT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, my_values[i]);
                    }
                    else{
                        Kl[ciak] += my_damage[i] *
                        StabilizationTangentMatrixTerm<summit::LEFT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C_q[i]);
                    }
                }
            }

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^LR
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // loop over the nodes of the right element -> displacement
            for (lnode_t b = _element_set->leftNodeStart(); b != _element_set->leftNodeDone();
                 ++b) {
                // _element_set->shape(e, q, b);
                const real disp_u = missingShapeAndDerivative[(nen + nen * spatial_dim) * q + b];
                // _element_set->dShape(e, q, b);
                const real* disp_du =
                  &missingShapeAndDerivative[(nen + nen * spatial_dim) * q + nen + b * spatial_dim];
                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t bk = _dof_node * (b + nen) + k;
                    // compute the index of the current entry in the local stiffness array
                    size_t cibk = ci * residual_dim + bk;
                    // update the consistency term
                    Kl[cibk] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentMatrixTerm<summit::LEFT>(e, q, test_u, i, disp_du,
                                                                           k, _bulkC_R);
                    Kl[cibk] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentSecondMatrixTerm<summit::LEFT>(
                      e, q, test_u, i, disp_u, k, _bulkdPdU_R);
                    // update the compatibility term
#if IMR_COMPATIBILITY

                    Kl[cibk] += ComFactor * (1.0 - my_damage[i]) * CompatibilityTangentMatrixTerm<summit::RIGHT>(
                      e, q, test_du, i, disp_u, k, (_bulkC_L.second)->local(e, q), _kappa_ref_L.data());
#endif  // IMR_COMPATIBILITY
        // update the penalty term
                    if (_usingFullStabilizationTerm) {
                        Kl[cibk] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTermFULL<summit::LEFT, summit::RIGHT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C_q);
                    }
                    else {
                        Kl[cibk] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTerm<summit::LEFT, summit::RIGHT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C_q[i]);
                    }
                    if(my_types[i]==0){
                        Kl[cibk] += my_damage[i] * StabilizationTangentMatrixTerm<summit::LEFT, summit::RIGHT>(
                          e, q, test_u, i, disp_u, k, my_values[i]);
                    }
                    //weak dirichlet does not depend on the other side of the element!
                }
            }
        }
    }

    // loop over the nodes of the right element -> test function
    for (lnode_t d = _element_set->leftNodeStart(); d != _element_set->leftNodeDone(); ++d) {
        //_element_set->shape(e, q, d);
        const real test_u = missingShapeAndDerivative[(nen + nen * spatial_dim) * q + d];
        // _element_set->dShape(e, q, d);
        const real* test_du =
          &missingShapeAndDerivative[(nen + nen * spatial_dim) * q + nen + d * spatial_dim];

        // loop over its degrees of freedom
        for (size_t i = 0; i < _dof_node; ++i) {
            // compute the index of the current dof in the local displacement vector
            size_t di = _dof_node * (d + nen) + i;

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^RL
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // loop over the nodes of the left element -> displacement
            for (lnode_t a = _element_set->leftNodeStart(); a != _element_set->leftNodeDone();
                 ++a) {
                const real disp_u = _element_set->shape(e, q, a);
                const real* disp_du = _element_set->dShape(e, q, a);
                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t ak = _dof_node * a + k;
                    // compute the index of the current entry in the local stiffness
                    // array
                    size_t diak = di * residual_dim + ak;

                    // update the consistency term
                    Kl[diak] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentMatrixTerm<summit::RIGHT>(
                      e, q, test_u, i, disp_du, k, (_bulkC_L.second)->local(e, q));
                    Kl[diak] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentSecondMatrixTerm<summit::RIGHT>(
                      e, q, test_u, i, disp_u, k, (_bulkdPdU_L.second)->local(e, q));

                    // update the compatibility term
#if IMR_COMPATIBILITY
                    Kl[diak] += ComFactor * (1.0 - my_damage[i]) * CompatibilityTangentMatrixTerm<summit::LEFT>(e, q, test_du, i,
                                                                             disp_u, k, _bulkC_R, _kappa_ref_L.data());
#endif  // IMR_COMPATIBILITY

                    // update the penalty term
                    if (_usingFullStabilizationTerm) {
                        Kl[diak] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTermFULL<summit::RIGHT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C_q);
                    }
                    else {
                        Kl[diak] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTerm<summit::RIGHT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C_q[i]);
                    }
                    if(my_types[i]==0){
                        Kl[diak] += my_damage[i] * StabilizationTangentMatrixTerm<summit::RIGHT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, my_values[i]);
                    }
                    //weak dirichlet does not depend on the other side of the element!
                }
            }

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^RR
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // loop over the nodes of the right element -> displacement
            for (lnode_t b = _element_set->leftNodeStart(); b != _element_set->leftNodeDone();
                 ++b) {
                //_element_set->shape(e, q, b);
                const real disp_u = missingShapeAndDerivative[(nen + nen * spatial_dim) * q + b];
                //_element_set->dShape(e, q, b);
                const real* disp_du =
                  &missingShapeAndDerivative[(nen + nen * spatial_dim) * q + nen + b * spatial_dim];
                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t bk = _dof_node * (b + nen) + k;
                    // compute the index of the current entry in the local stiffness
                    // array
                    size_t dibk = di * residual_dim + bk;
                    // update the consistency term

                    Kl[dibk] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentMatrixTerm<summit::RIGHT>(e, q, test_u, i,
                                                                            disp_du, k, _bulkC_R);

                    Kl[dibk] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentSecondMatrixTerm<summit::RIGHT>(
                      e, q, test_u, i, disp_u, k, _bulkdPdU_R);
                    // update the compatibility term
#if IMR_COMPATIBILITY
                    Kl[dibk] += ComFactor * (1.0 - my_damage[i]) * CompatibilityTangentMatrixTerm<summit::RIGHT>(e, q, test_du, i,
                                                                              disp_u, k, _bulkC_R, _kappa_ref_L.data());
#endif  // IMR_COMPATIBILITY
        // update the penalty term
                    if (_usingFullStabilizationTerm) {
                        Kl[dibk] += StabFactor * (1.0 - my_damage[i]) *
                          StabilizationTangentMatrixTermFULL<summit::RIGHT, summit::RIGHT>(
                            e, q, test_u, i, disp_u, k, averg_hs_C_q);
                    }
                    else {
                        Kl[dibk] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTerm<summit::RIGHT, summit::RIGHT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C_q[i]);
                    }
                    if(my_types[i]==0){
                        Kl[dibk] += my_damage[i] * StabilizationTangentMatrixTerm<summit::RIGHT, summit::RIGHT>(
                          e, q, test_u, i, disp_u, k, my_values[i]);
                    }
                    else{
                        Kl[dibk] += my_damage[i] *
                        StabilizationTangentMatrixTerm<summit::RIGHT, summit::RIGHT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C_q[i]);
                    }
                }
            }
        }
    }

    return;
}


void summit::ReactionDiffusionInterfaceRegion::_crackedIntegrand(
  elem_t e,
  quad_t q,
  const real* cohesive_traction,
  real* interface_tangent,
  std::vector<real>& rl,
  std::vector<real>& Kl,
  summit::real const* missingShapeAndDerivative)
{
    // Here, in assemblying the stiffness, we assume that the side of the half butterfly is
    // summit::LEFT.
    // Indeed, only the left part of the half butterfly (lower processor id) gets to assemble
    // its contribution to the global stiffness
    // The 'missing pieces' to compute the elementary contributions are provided by
    //  missingShapeAndDerivative -> shape functions and derivatives of the other half
    //  missingCijkl -> tangent modulus of the other half
    // Finally, the iteration on the right-hand nodes is done by using the left-hand counter
    // loop and translating the loop indices of nen (which is the number of elements of the
    // half butterfly)
    // However, notice that the contributions to the elementary residual are assembled to the global
    // residual by both half butterflies and the rule for assemblying these contributions follows
    // that of _residualIntegrand (namely, assemble right hand piece and multiply by _amIRight to
    // get the correct sign).

    // number of element nodes in one element (half butterfly)
    size_t const nen = _element_set->nodes_element();

    // spatial dimension
    size_t const spatial_dim = _element_set->dim();

    // size of the local residual
    int const residual_dim = _element_set->nodes_element() * _dof_node;

    real* dFdU = interface_tangent + _dof_node * _dof_node * q;

    // loop over the nodes of the left element -> test function
    for (lnode_t c = _element_set->leftNodeStart(); c != _element_set->leftNodeDone(); ++c) {
        const real test_u = _element_set->shape(e, q, c);

        // loop over its degrees of freedom
        for (size_t i = 0; i < _dof_node; ++i) {
            // compute the index of the current dof in the local displacement vector
            size_t ci = _dof_node * c + i;

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling r^L
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            //
            // Compute the "left" interface residual
            //
            // Here, we compute MINUS the internal forces because the assemble function only
            // adds to the residual: residual = f^ext - f^int
            // update the consistency term
            rl[ci] -=
              _amIRight * TractionResidualTerm<summit::RIGHT>(e, q, test_u, i, cohesive_traction);

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^LL
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // tangent matrix for fracture
            // loop over the nodes of the left element -> displacement
            for (lnode_t a = _element_set->leftNodeStart(); a != _element_set->leftNodeDone();
                 ++a) {
                const real disp_u = _element_set->shape(e, q, a);

                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t ak = _dof_node * a + k;
                    // compute the index of the current entry in the local stiffness array
                    size_t ciak = ci * residual_dim + ak;

                    Kl[ciak] += CohesiveTangentMatrixTerm<summit::LEFT, summit::LEFT>(
                      e, q, test_u, i, disp_u, k, dFdU);
                }
            }

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^LR
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // loop over the nodes of the right element -> displacement
            for (lnode_t a = _element_set->leftNodeStart(); a != _element_set->leftNodeDone();
                 ++a) {
                //_element_set->shape(e, q, a);
                const real disp_u = missingShapeAndDerivative[(nen + nen * spatial_dim) * q + a];

                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t ak = _dof_node * (a + nen) + k;
                    // compute the index of the current entry in the local stiffness array
                    size_t ciak = ci * residual_dim + ak;

                    Kl[ciak] += CohesiveTangentMatrixTerm<summit::LEFT, summit::RIGHT>(
                      e, q, test_u, i, disp_u, k, dFdU);
                }
            }
        }
    }

    // loop over the nodes of the right element -> test function
    for (lnode_t d = _element_set->leftNodeStart(); d != _element_set->leftNodeDone(); ++d) {
        //_element_set->shape(e, q, d);
        const real test_u = missingShapeAndDerivative[(nen + nen * spatial_dim) * q + d];

        // loop over its degrees of freedom
        for (size_t i = 0; i < _dof_node; ++i) {
            // compute the index of the current dof in the local displacement vector
            size_t di = _dof_node * (d + nen) + i;

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^RL
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // loop over the nodes of the left element -> displacement
            // tangent matrix for fracture
            for (lnode_t a = _element_set->leftNodeStart(); a != _element_set->leftNodeDone();
                 ++a) {
                const real disp_u = _element_set->shape(e, q, a);

                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t ak = _dof_node * a + k;
                    // compute the index of the current entry in the local stiffness
                    // array
                    size_t diak = di * residual_dim + ak;

                    Kl[diak] += CohesiveTangentMatrixTerm<summit::RIGHT, summit::LEFT>(
                      e, q, test_u, i, disp_u, k, dFdU);
                }
            }

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^RR
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // loop over the nodes of the right element -> displacement
            // tangent matrix for fracture
            for (lnode_t a = _element_set->leftNodeStart(); a != _element_set->leftNodeDone();
                 ++a) {
                //_element_set->shape(e, q, a);
                const real disp_u = missingShapeAndDerivative[(nen + nen * spatial_dim) * q + a];

                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t ak = _dof_node * (a + nen) + k;
                    // compute the index of the current entry in the local stiffness array
                    size_t diak = di * residual_dim + ak;

                    Kl[diak] += CohesiveTangentMatrixTerm<summit::RIGHT, summit::RIGHT>(
                      e, q, test_u, i, disp_u, k, dFdU);
                }
            }
        }
    }

    return;
}

void summit::ReactionDiffusionInterfaceRegion::WriteForRestart(Checkpoint* checkpoint,
                                                       const char* name,
                                                       const char* tag) const
{
    if (tag == nullptr)
        summit::commonInterfaceMechanicsRegion::WriteForRestart(checkpoint, name,
                                                                "ReactionDiffusionInterfaceRegion");
    else
        summit::commonInterfaceMechanicsRegion::WriteForRestart(checkpoint, name, tag);

    int matIndex_L = _material_L.GetMaterialIndex();
    checkpoint->write<int>("matIndex_L", matIndex_L);

    int mat_index = _material.GetMaterialIndex();
    checkpoint->write<int>("matIndex", mat_index);

    int bufferSizeForCommInt = _bufferSizeForComm;
    checkpoint->write<int>("bufferSizeForComm", bufferSizeForCommInt);

    int dimForTJElementaryResidualInt = _dimForTJElementaryResidual;
    checkpoint->write<int>("dimForTJElementaryResidual", dimForTJElementaryResidualInt);

    checkpoint->write<real>("amIRight", _amIRight);

    _TJ->WriteForRestart(checkpoint, "TJ");
    _internals->WriteForRestart(checkpoint, "internals");
    _stresses_L->WriteForRestart(checkpoint, "stresses_L");
    _strains_L->WriteForRestart(checkpoint, "strains_L");
    _internals_L->WriteForRestart(checkpoint, "internals_L");
    _internals_avg->WriteForRestart(checkpoint, "internals_avg");
    _CField_L->WriteForRestart(checkpoint, "CField_L");
    _dPdUField_L->WriteForRestart(checkpoint, "dPdUField_L");
    _dFField_L->WriteForRestart(checkpoint, "dfField_L");
    _avg_stab_coef_L->WriteForRestart(checkpoint, "avg_stab_coef_L");

    std::vector<int> dims(1, _C_L.size());
    if (dims[0] > 0) checkpoint->WriteDataSet("C_L", dims, SUMMIT_REAL, _C_L.data());

    // tag with material label
    std::string label = _material.GetLabel();
    checkpoint->location()->tag("material", label.data());
    label = _material_L.GetLabel();
    checkpoint->location()->tag("material_L", label.data());

    return;
}

REGISTER(Region, ReactionDiffusionInterfaceRegion);

// end of file
