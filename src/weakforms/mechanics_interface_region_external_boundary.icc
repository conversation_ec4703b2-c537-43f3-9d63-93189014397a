// -*- C++ -*-
//
// summit development team
// massachusetts institute of technology
// (c) 2012-2013 all rights reserved
//

// #if !defined(summit_mechanics_interface_region_external_boundary_icc)
// #error This header file contains implementation details of class MechanicsInterfaceRegionExternalBoundary
// #else

template <summit::SIDE side_c>
summit::real summit::MechanicsInterfaceRegionExternalBoundary::FluxTerm(
  elem_t e, quad_t q, real test_u, size_t i, const real* left_traction) const
{
    // term of node c in i-th direction of the local residual
    return summit::signSide<side_c>() * test_u * left_traction[i];
}

template <summit::SIDE side_c>
summit::real summit::MechanicsInterfaceRegionExternalBoundary::dFluxdGrad(
  elem_t e, quad_t q, real test_u, size_t i, const real* disp_du, size_t k, const real* bulkC_a)
  const
{
    // spatial dimension
    size_t spatial_dim = _element_set->dim();
    // term of nodes c in i-th direction and a in the k-th direction of the local tangent matrix
    real kl_ciak = 0.0;
    // contraction over l
    size_t ik = _dof_node * i + k;
    for (dim_t l(0); l < spatial_dim; ++l) {
      size_t ikl = spatial_dim * ik + l;
      kl_ciak += summit::signSide<side_c>() * test_u * bulkC_a[ikl] * disp_du[l];
    }
    // all done
    return  kl_ciak;
}

template <summit::SIDE side_c>
summit::real summit::MechanicsInterfaceRegionExternalBoundary::dFluxdU(
  elem_t e, quad_t q, real test_u, size_t i, const real disp_u, size_t k, const real* dPdU_a)
  const
{
    size_t ik = (i * _dof_node) + k;
    return summit::signSide<side_c>()* test_u * dPdU_a[ik] * disp_u ;
}

// #endif
// end of file
