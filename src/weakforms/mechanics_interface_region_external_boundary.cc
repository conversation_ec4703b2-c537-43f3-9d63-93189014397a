#include <iostream>
#include <iomanip>
#include <cstring>
#include <fstream>
#include <stdexcept>

#include "../fem/nodal_field.h"
#include "../elements/element_quadrature_field.h"
#include "../solvers/stiffness/stiffness.h"
#include "../io/summit_message.h"
#include "../elements/element_set_interface_one_sided.h"
#include "../materials/material.h"
#include "../materials/upwind_interface_dg.h"
#include "../mesh/internal_boundary.h"
#include "../mesh/mesh.h"
#include "../mesh/mesh_entity.h"

#include "mechanics_interface_region_external_boundary.h"

#include "../restart/checkpoint.h"
#include "../restart/Group.h"

namespace summit {

MechanicsInterfaceRegionExternalBoundary::MechanicsInterfaceRegionExternalBoundary(
    ElementSetInterfaceOneSided const* es,
    int dof_node,
    InterfaceDG const& material,
    MechanicalMaterial const& material_L,
    NodalField<real> const& coordinates,
    bool usingFullStabilizationTerm)
    : DirichletMechanicsRegion(es, dof_node, material, material_L, coordinates, usingFullStabilizationTerm)
{
    return;
}

MechanicsInterfaceRegionExternalBoundary::MechanicsInterfaceRegionExternalBoundary(
    Checkpoint* checkpoint, const char* name)
    : DirichletMechanicsRegion(checkpoint, name)
{
    
}

MechanicsInterfaceRegionExternalBoundary::~MechanicsInterfaceRegionExternalBoundary()
{
    // end of method
    return;
}

void MechanicsInterfaceRegionExternalBoundary::WriteForRestart(
    Checkpoint* checkpoint, const char* name, const char* tag) const
{
    // Call write of super class (this creates the group for this region)
    if (tag == nullptr) {
        DirichletMechanicsRegion::WriteForRestart(checkpoint, name, "MechanicsInterfaceRegionExternalBoundary");
    }
    else {
        DirichletMechanicsRegion::WriteForRestart(checkpoint, name, tag);
    }
    return;
}

void MechanicsInterfaceRegionExternalBoundary::Update(
    NodalField<real> const& u, NodalField<real> const& u0, real dt)
{
// number of nodes per element
    size_t const nen = _element_set->connectivities_element();
    size_t spatial_dim = _element_set->dim();
    // get the size of the local residual
    int const residual_dim = nen * _dof_node;
    // allocate memory for the local solution
    static std::vector<real> ul;
    ul.resize(residual_dim);
    static std::vector<real> u0l;
    u0l.resize(residual_dim);
    // allocate memory for local nodal coordinates
    static std::vector<real> xl;
    xl.resize(nen * spatial_dim);

    // in the Update, these are not used. We have them so we can reuse the code instead
    static std::vector<real> rl;
    rl.resize(residual_dim);
    static std::vector<real> Kl;
    Kl.resize(residual_dim * residual_dim);
    std::fill(rl.begin(), rl.end(), 0.0);
    std::fill(Kl.begin(), Kl.end(), 0.0);

    // loop over the elements of the element set
    for (elem_t e(0); e != _element_set->elements(); ++e) {
        // extract local part of unknown
        _element_set->Localize(u, e, ul);
        _element_set->Localize(u0, e, u0l);
        _element_set->Localize(_coordinates, e, xl);

        // assembly local residual
        ElementStiffness(e, dt, true, false, false, ul, u0l, xl, rl, Kl);
    }
    return;
}

void MechanicsInterfaceRegionExternalBoundary::Residual(
    NodalField<real> const& u,
    NodalField<real> const& u0,
    real dt,
    bool update,
    CommunicationManager const& commManager,
    NodalField<real>& residual)
{
    // number of nodes per element
    size_t const nen = _element_set->connectivities_element();
    size_t spatial_dim = _element_set->dim();
    // get the size of the local residual
    int const residual_dim = nen * _dof_node;
    static std::vector<real> rl;
    rl.resize(residual_dim);
    static std::vector<real> Kl;
    Kl.resize(residual_dim * residual_dim);

    // allocate memory for the local displacement (previous and current)
    static std::vector<real> ul;
    ul.resize(residual_dim);
    static std::vector<real> u0l;
    u0l.resize(residual_dim);
    // allocate memory for local nodal coordinates
    static std::vector<real> xl;
    xl.resize(nen * spatial_dim);

    // loop over the elements of the element set
    for (elem_t e(0); e != _element_set->elements(); ++e) {
        // extract local part of unknown
        _element_set->Localize(u, e, ul);
        _element_set->Localize(u0, e, u0l);

        // get the coordinates of the nodes of the current element
        _element_set->Localize(_coordinates, e, xl);

        // assembly local residual
        std::fill(rl.begin(), rl.end(), 0.0);
        std::fill(Kl.begin(), Kl.end(), 0.0);

        ElementStiffness(e, dt, update, true, false, ul, u0l, xl, rl, Kl);

        // assemble local residual into global array
        _element_set->Assemble(rl, e, residual);
    }
    return;
}

void MechanicsInterfaceRegionExternalBoundary::ComputeStiffness(
    NodalField<real> const& u,
    NodalField<real> const& u0,
    real dt,
    Stiffness& stiffness,
    NodalField<real>& residual)
{
    // number of nodes per element
    size_t const nen = _element_set->connectivities_element();

    // size of the local residual
    int const residual_dim = nen * _dof_node;
    size_t spatial_dim = _element_set->dim();
    // allocate memory for the local displacement (previous and current)
    // the element residual and stiffness
    static std::vector<real> ul;
    ul.resize(residual_dim);
    static std::vector<real> u0l;
    u0l.resize(residual_dim);
    static std::vector<real> xl;
    xl.resize(nen * spatial_dim);
    static std::vector<real> rl;
    rl.resize(residual_dim);
    static std::vector<real> Kl;
    Kl.resize(residual_dim * residual_dim);

    // loop over the elements of the element set
    for (elem_t e(0); e != _element_set->elements(); ++e) {
        // extract local part of unknown
        _element_set->Localize(u, e, ul);
        _element_set->Localize(u0, e, u0l);
        _element_set->Localize(_coordinates, e, xl);

        // reset residual and stiffness
        std::fill(rl.begin(), rl.end(), 0.0);
        std::fill(Kl.begin(), Kl.end(), 0.0);

        // compute elementary stiffness matrix and residual
        ElementStiffness(e, dt, false, true, true, ul, u0l, xl, rl, Kl);

        // assemble local residual into global array
        if (stiffness.getAssembleResidualFlag() != 0) {
            _element_set->Assemble(rl, e, residual);
        }

        // assemble local stiffness matrix into global matrix
        stiffness.AddElement(_element_set->dof_map()->Connectivity(e), &Kl[0], nen, _dof_node,
                             residual);
    }

    // end of method
    return;
}

// Template method implementations removed for simplicity
// These would be implemented based on specific boundary condition requirements

void MechanicsInterfaceRegionExternalBoundary::ElementStiffness(
    elem_t e,
    real dt,
    bool update,
    bool DoResidual,
    bool DoStiffness,
    std::vector<real> const& ul,
    std::vector<real> const& u0l,
    std::vector<real> const& xl,
    std::vector<real>& rl,
    std::vector<real>& Kl)
{
    // get dimensions
    int const strain_dim = _strains_L->dim();
    size_t const nen = _element_set->connectivities_element();
    size_t const internal_L_dim = _material_L.nInt();
    size_t const internal_dim = _material.nInt();

    // spatial dimension
    size_t const spatial_dim = _element_set->dim();
    int const residual_dim = nen * _dof_node;

    // memory allocation ...
    // ... for the left part
    std::vector<real> P_L_new;
    P_L_new.resize(strain_dim);

    // memory allocation
    std::vector<real> F_L_new;
    F_L_new.resize(spatial_dim * _dof_node);
    std::vector<real> internal_L_new;
    internal_L_new.resize(internal_L_dim);
    std::vector<real> internal_new;
    internal_new.resize(internal_dim);

    // ... for the left traction
    std::vector<real> traction;
    traction.resize(_dof_node);
    std::vector<real> dtractiondU;
    dtractiondU.resize(_dof_node*_dof_node);
    std::vector<real> dtractiondgradU;
    dtractiondgradU.resize(_dof_node*_dof_node*spatial_dim);

    // calculation of the characteristic length (inradius) of the elements
    real measInterface = _element_set->InRadiusElement(&xl[0]);
    std::vector<real> fDotN(_dof_node);
    std::vector<real> dfDotNdC(_dof_node*_dof_node);
    std::vector<real> dfDotNdgradC(_dof_node*_dof_node*spatial_dim);
    
    // integrate over quadrature points
    for (quad_t q(0); q < _element_set->nquad(); ++q) {
        // constitutive update of the left element
	    for (size_t i = 0; i < _CField_L->dim(); ++i) {
            _CField_L->local(e, q)[i] = 0.0;
            _C_L[i] = 0.0;
        }
        std::fill(fDotN.begin(), fDotN.end(), 0.);
        std::fill(dfDotNdC.begin(), dfDotNdC.end(), 0.);
        std::fill(dfDotNdgradC.begin(), dfDotNdgradC.end(), 0.);
        LeftConstitutiveUpdate(e, q, ul, u0l, xl, dt, measInterface,
                               P_L_new, F_L_new, internal_L_new, internal_new,
                               _C_L.data(), fDotN.data(),
                               dfDotNdC.data(), dfDotNdgradC.data());
	    for (size_t i = 0; i < _CField_L->dim(); ++i) {
            _CField_L->local(e, q)[i] = _C_L[i];
        }
        // // Compute boundary traction based on boundary condition type
        // std::vector<real> boundary_traction(_dof_node, 0.0);

        // Below is not needed in update function, but is required for residual
        // jacobians (with quadrature weights)
        real jac = _element_set->jac(e, q);
        // reference to the left normal
        const real* normalL = _element_set->normalL(e, q);

        std::fill(traction.begin(), traction.end(), 0.);
        std::fill(dtractiondU.begin(), dtractiondU.end(), 0.);
        std::fill(dtractiondgradU.begin(), dtractiondgradU.end(), 0.);
        // project the average PK-I onto the normal to get the left traction
        for (size_t i = 0; i < _dof_node; ++i) {
            traction[i] += jac * (fDotN[i]);
            for (size_t k = 0; k < _dof_node; ++k) {
                size_t ik = _dof_node * i + k;
                dtractiondU[ik] += jac * (dfDotNdC[ik]);
                for (dim_t j(0); j < spatial_dim; ++j) {
                    size_t ikj = spatial_dim * ik + j;
                    dtractiondgradU[ikj] = jac * dfDotNdgradC[ikj];// scale by the Jacobian
                }
            }
        }
        // loop over the nodes of the left element
        for (lnode_t c = _element_set->leftNodeStart(); c != _element_set->leftNodeDone(); ++c) {
            const real test_u = _element_set->shape(e, q, c);
            const real* test_du = _element_set->dShape(e, q, c);
            // loop over its degrees of freedom
            for (size_t i = 0; i < _dof_node; ++i) {
                // compute the index of the current dof in the local displacement vector
                size_t ci = _dof_node * c + i;
                // Here, we compute MINUS the internal forces because the assemble function only adds
                rl[ci] += FluxTerm<summit::LEFT>(e, q, test_u, i, traction.data());
                if(DoStiffness){
                //if(false){
                    for (lnode_t a = _element_set->leftNodeStart(); a != _element_set->leftNodeDone();++a) {
                        const real disp_u = _element_set->shape(e, q, a);
                        const real* disp_du = _element_set->dShape(e, q, a);
                        // loop over its degrees of freedom
                        for (size_t k = 0; k < _dof_node; ++k) {
                            // compute the index of the current dof in the local test function
                            size_t ak = _dof_node * a + k;
                            size_t ciak = ci * residual_dim + ak;
                            Kl[ciak] -= dFluxdGrad<summit::LEFT>(e, q, test_u, i, disp_du, k, dtractiondgradU.data());
                            Kl[ciak] -= dFluxdU<summit::LEFT>(e, q, test_u, i, disp_u, k, dtractiondU.data());
                        }
                    }
                }
            }
        }
        if(update){
            std::copy(P_L_new.begin(), P_L_new.end(), _stresses_L->local(e, q));
            std::copy(F_L_new.begin(), F_L_new.end(), _strains_L->local(e, q));
            std::copy(internal_L_new.begin(), internal_L_new.end(), _internals_L->local(e, q));
            std::copy(internal_new.begin(), internal_new.end(), _internals->local(e, q));
        }
    }
    return;
}

void MechanicsInterfaceRegionExternalBoundary::LeftConstitutiveUpdate(
    elem_t e,
    quad_t q,
    std::vector<real> const& ul,
    std::vector<real> const& u0l,
    std::vector<real> const& xl,
    real dt,
    real measInterface,
    std::vector<real>& P_L_new,
    std::vector<real>& F_L_new,
    std::vector<real>& internal_L_new,
    std::vector<real>& internal_new,
    real* C_L,
    real* fDotN,                                 
    real* dfDotNdC,
    real* dfDotNdgradC)
{
    // spatial dimension
    size_t const spatial_dim = _element_set->dim();
    // strain dimension
    int const strain_dim = _strains_L->dim();
    // dimension of internal variables
    size_t const internal_L_dim = _material_L.nInt();
    size_t const internal_dim = _material.nInt();
    // To know if the tangent has to be computed
    bool const compute_tangent = (C_L ? true : false);

    size_t const nen = _element_set->connectivities_element();
    size_t const ndm = _element_set->dim();
    size_t const ndf = _dof_node;
    // Compute the left gradient
    _element_set->Gradient_L(e, q, ul.data(), _dof_node, F_L_new.data());
    std::vector<real> u(_dof_node);
    std::vector<real> u0(_dof_node);
    std::fill(u.begin(), u.end(), 0.);
    std::fill(u0.begin(), u0.end(), 0.);
    std::vector<real> x(spatial_dim);
    std::fill(x.begin(), x.end(), 0.);
    
    for (lnode_t b = _element_set->leftNodeStart(); b != _element_set->leftNodeDone(); ++b) {
        for (int c = 0; c < _dof_node; c++) {
            u[c] += _element_set->shape(e, q, b) * ul[c + b * _dof_node];
            u0[c] += _element_set->shape(e, q, b) * u0l[c + b * _dof_node];
        }
        for (int c = 0; c < spatial_dim; c++) {
            x[c] += _element_set->shape(e, q, b) * xl[c + b * spatial_dim];
        }
    }

    // pointer to local value of stress at previous time step
    real* P_L = _stresses_L->local(e, q);
    // copy to local Left stresses
    std::copy(P_L, P_L + strain_dim, P_L_new.begin());

    // local value of internal variables at previous time step
    real* internal_L = _internals_L->local(e, q);
    std::copy(internal_L, internal_L + internal_L_dim, internal_L_new.begin());
    real* internal = _internals->local(e, q);
    std::copy(internal, internal + internal_dim, internal_new.begin());

    // local value of old deformation gradient at previous time step
    real* F0_L = _strains_L->local(e, q);

    const real* normalL = _element_set->normalL(e, q);

    const auto* myMat = dynamic_cast_or_continue<const UpwindInterfaceDG*>(&_material,__HERE__);
    if(myMat != nullptr){
        myMat->viscousBoundaryFlux(ndm, ndf, dt, measInterface, internal_new.data(), u.data(), u0.data(), F_L_new.data(), F0_L, normalL, x.data(), fDotN, dfDotNdC, dfDotNdgradC);
    }

    // The below stuff is just for parity with the rest of summit.
    // Any dependence of surface physics on the bulk response has to be encoded into
    // _material by the designer of material.
    _material_L.Constitutive(&u0[0], &u[0], F0_L, F_L_new.data(), P_L_new.data(),
                             internal_L_new.data(), C_L, dt, _dof_node, spatial_dim,
                             compute_tangent, false); // no artificial viscosity here
}

} // namespace summit
