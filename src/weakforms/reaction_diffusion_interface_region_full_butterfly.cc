#include <iostream>
#include <iomanip>
#include <cstring>
#include <fstream>
#include <stdexcept>

#include "../fem/nodal_field.h"
#include "../elements/element_quadrature_field.h"
#include "../solvers/stiffness/stiffness.h"
#include "../io/summit_message.h"
#include "../elements/element_set_interface_two_sided.h"
#include "../materials/material.h"
#include "../mesh/internal_boundary.h"
#include "../mesh/mesh.h"
#include "../mesh/mesh_entity.h"

#include "reaction_diffusion_interface_region_full_butterfly.h"

#include "../restart/checkpoint.h"
#include "../restart/Group.h"

#define IMR_COMPATIBILITY 1


summit::ReactionDiffusionInterfaceRegionFullButterfly::ReactionDiffusionInterfaceRegionFullButterfly(ElementSetInterfaceTwoSided const* es,
                                                           int dof_node,
                                                           InterfaceDG const& material,
                                                           ReactionDiffusionMaterial const& material_L,
                                                           ReactionDiffusionMaterial const& material_R,
                                                           NodalField<real> const& coordinates,
                                                           bool usingFullStabilizationTerm)
  :  // call the base class constructor
    ReactionDiffusionInterfaceRegion(
      es, dof_node, material, material_L, coordinates, usingFullStabilizationTerm),
    // right material
    _material_R(material_R),
    // right stress
    _stresses_R(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(), _element_set->dim() * _dof_node)),
    _F_R(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(),  _element_set->dim() * _dof_node)),   
    // right strain
    _strains_R(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(), _element_set->dim() * _dof_node)),
    // right internal variables
    _internals_R(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(), _material_R.nInt())),
    // Elastic tangent modulus
    _CField_R(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(), _strains_R->dim() * _strains_R->dim())),
    // right tangent modulus
    _C_R(_strains_R->dim() * _strains_R->dim()),
    _dPdUField_R(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(), _strains_R->dim() * dof_node)),
    _dFField_R(new ElementQuadratureField<real>(
      _element_set->elements(), _element_set->nquad(), dof_node * dof_node)),
    _dPdU_R(_strains_R->dim() * dof_node),
    _dF_R(_strains_L->dim() * dof_node),
    _bulkC_R(summit::RIGHT, _CField_R),
    _bulkdPdU_R(summit::RIGHT, _dPdUField_R),
    _bulkdF_R(summit::RIGHT, _dFField_R),
    _kappa_ref_R(_strains_R->dim() * _strains_R->dim()),
    // compute the right part by default
    _computeRightPart(true)
{
    if (_usingFullStabilizationTerm) {
        _avg_stab_coef_R = new ElementQuadratureField<real>(
          _element_set->elements(), _element_set->nquad(), _dof_node * _dof_node);
    }
    else {
        _avg_stab_coef_R =
          new ElementQuadratureField<real>(_element_set->elements(), _element_set->nquad(), _dof_node);
    }

    // register the right stresses as a field
    RegisterField("stresses_R", _stresses_R);
    RegisterField("f_R", _F_R);
    // register the right strains as a field
    RegisterField("strains_R", _strains_R);
    // Initialize strains to Identity (deformation gradient)
    // loop over the elements and the quad points
    //this is commented out since we initialize the class with no concentration/temperature gradients (DANIEL)
    // register the right internal variables as a field
    RegisterField("internals_R", _internals_R);

    // initialize the internal variables
    for (elem_t e(0); e < _element_set->elements(); ++e) {
        for (quad_t q(0); q < _element_set->nquad(); ++q) {
            _material_R.InitInternal(_internals_R->local(e, q));
        }
    }

    // register the Elastic tangent modulus as a field
    RegisterField("CField_R", _CField_R);
    RegisterField("dPdUField_R", _dPdUField_R);
    RegisterField("dfField_R", _dFField_R);
    // compute the elastic right tangent modulus
    std::vector<real> P_R_new(_strains_R->dim());
    std::vector<real> f_R_new(_F_R->dim());
    std::vector<real> F_R_new(_strains_R->dim());

    std::vector<real> concentration_R(_dof_node);
    std::vector<real> concentration_R0(_dof_node);
    std::vector<real> internals_R_new(_internals_R->dim());
    std::vector<real> ul(dof_node * _element_set->nodes_element(), 0.0);
    RightConstitutiveUpdate(elem_t(0), quad_t(0), ul, ul, concentration_R0, concentration_R, 0.0, P_R_new, f_R_new, F_R_new, internals_R_new, _C_R.data(), _dPdU_R.data(), _dF_R.data());
    for (int i=0; i < _CField_L->dim(); ++i) {
        //setup the reference conductivities
        _kappa_ref_R[i] = _C_R[i];
        // std::cout << "_kappa_ref_R[" << i << "]: " << _kappa_ref_R[i] <<std::endl;
    }
    for (elem_t e(0); e < _element_set->elements(); ++e) {
        for (quad_t q(0); q < _element_set->nquad(); ++q) {
            // for (size_t i = 0; i < _CField_L->dim(); ++i) {
            //     _CField_L->local(e, q)[i] = _C_L[i];
            // }
            for (size_t i = 0; i < _CField_R->dim(); ++i) {
                _CField_R->local(e, q)[i] = _C_R[i];
            }
            // for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
            //     _dPdUField_L->local(e, q)[i] = _dPdU_L[i];
            // }
            for (size_t i = 0; i < _dPdUField_R->dim(); ++i) {
                _dPdUField_R->local(e, q)[i] = _dPdU_R[i];
            }
            // for (size_t i = 0; i < _dFField_L->dim(); ++i) {
            //     _dFField_L->local(e, q)[i] = _dF_L[i];
            // }
            for (size_t i = 0; i < _dFField_R->dim(); ++i) {
                _dFField_R->local(e, q)[i] = _dF_R[i];
            }
        }
    }

    if (_usingFullStabilizationTerm)
        _computeAveragedFULLStabilizationCoefficientRight();
    else
        _computeAveragedStabilizationCoefficientRight();

    // end of method
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::_computeAveragedFULLStabilizationCoefficientRight()
{
    // compute the stability parameter
    real beta_DG = _material.StabilityParameter(InterfaceDG::DEFAULT);

    // spatial dimension
    size_t spatial_dim = _element_set->dim();

    for (elem_t e(0); e < _element_set->elements(); ++e) {
        for (quad_t q(0); q < _element_set->nquad(); ++q) {
            for (size_t i(0); i < _dof_node; ++i) {
                for (size_t j(0); j < _dof_node; ++j) {
                    for (dim_t J(0); J < spatial_dim; ++J) {
                        size_t iJ = spatial_dim * i + J;
                        for (dim_t L(0); L < spatial_dim; ++L) {
                            size_t jL = spatial_dim * j + L;
                            size_t iJjL = spatial_dim * spatial_dim * iJ + jL;
                            (_avg_stab_coef_R->local(e, q))[_dof_node * i + j] +=
                              0.5 * (_bulkC_R.second)->local(e, q)[iJjL] * beta_DG *
                              _element_set->normalL(e, q, J) * _element_set->normalL(e, q, L);
                        }
                    }
                }
            }
        }
    }

    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::_computeAveragedStabilizationCoefficientRight()
{
    // compute the stability parameter
    std::vector<real> my_betas(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getBetasForDGStability(my_betas.data());
    for (size_t i(0); i < _dof_node; ++i) {  
        for (elem_t e(0); e < _element_set->elements(); ++e) {
            for (quad_t q(0); q < _element_set->nquad(); ++q) {
                const real* qR = _internals_R->local(e, q);
                _avg_stab_coef_R->local(e, q)[i] = 0.5 * _kappa_ref_R[i *_element_set->dim() + i * _dof_node * _element_set->dim() * _element_set->dim()] * my_betas[i];
            }
        }
    }

    return;
}

summit::ReactionDiffusionInterfaceRegionFullButterfly::ReactionDiffusionInterfaceRegionFullButterfly(Checkpoint* checkpoint, const char* name)
  : ReactionDiffusionInterfaceRegion(checkpoint, name),
    _material_R(*GET_CONST_REFERENCE(summit::ReactionDiffusionMaterial,
                                     checkpoint->location()->tag(std::string("material_R"))))
{
    _stresses_R = new ElementQuadratureField<real>(checkpoint, "stresses_R");
    _F_R = new ElementQuadratureField<real>(checkpoint, "f_R");
    _strains_R = new ElementQuadratureField<real>(checkpoint, "strains_R");
    _internals_R = new ElementQuadratureField<real>(checkpoint, "internals_R");
    _avg_stab_coef_R = new ElementQuadratureField<real>(checkpoint, "avg_stab_coef_R");
    _CField_R = new ElementQuadratureField<real>(checkpoint, "CField_R");
    _dPdUField_R = new ElementQuadratureField<real>(checkpoint, "dPdUField_R");
    _dFField_R = new ElementQuadratureField<real>(checkpoint, "dfField_R");
    _bulkC_R.second = new ElementQuadratureField<real>(checkpoint, "bulkC_R_second");
    _bulkdPdU_R.second = new ElementQuadratureField<real>(checkpoint, "bulkdPdU_R_second");
    _bulkdF_R.second = new ElementQuadratureField<real>(checkpoint, "bulkdf_R_second");
    RegisterField("stresses_R", _stresses_R);
    RegisterField("f_R", _F_R);
    RegisterField("strains_R", _strains_R);
    RegisterField("internals_R", _internals_R);
    RegisterField("CField_R", _CField_R);
    RegisterField("dPdUField_R", _dPdUField_R);
    RegisterField("dfField_R", _dFField_R);
    std::vector<int> dims(1);

    // Read _C_R
    DataSet* ds = checkpoint->OpenDataSet("C_R");
    dims = ds->dims();
    _C_R.resize(dims[0]);
    ds->read(_C_R.data());

    // Read _bulkC_R_first
    int bulkC_R_first;
    ds = checkpoint->OpenDataSet("bulkC_R_first");
    ds->read(&bulkC_R_first);
    _bulkC_R.first = static_cast<summit::SIDE>(bulkC_R_first);

    // Read _bulkdPdU_R_first
    int bulkdPdU_R_first;
    ds = checkpoint->OpenDataSet("bulkdPdU_R_first");
    ds->read(&bulkdPdU_R_first);
    _bulkdPdU_R.first = static_cast<summit::SIDE>(bulkdPdU_R_first);

    // Read _computeRightPart
    ds = checkpoint->OpenDataSet("computeRightPart");
    int computeRightPart;
    ds->read(&computeRightPart);
    _computeRightPart = computeRightPart;
}

summit::ReactionDiffusionInterfaceRegionFullButterfly::~ReactionDiffusionInterfaceRegionFullButterfly()
{
    // delete the pointer to the element quadrature field for right stresses
    delete _stresses_R;
    delete _F_R;
    // delete the pointer to the element quadrature field for right strains
    delete _strains_R;
    // delete the pointer to the element quadrature field for right internal variables
    delete _internals_R;
    // delete the pointer to the element quadrature field for the right Elastic tangent modulus
    delete _CField_R;
    delete _dPdUField_R;
    delete _dFField_R;
    delete _avg_stab_coef_R;

    // end of method
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::AssembleJacobiPreconditioner(NodalField<real> const& u,
                                                                    NodalField<real>& jacobi,
                                                                    const real dt) const
{
    // number of nodes per element
    size_t const nen = _element_set->connectivities_element();

    // size of the local residual
    int const residual_dim = nen * _dof_node;

    // number of quadrature points
    size_t const nquads = _element_set->nquad();

    // allocate memory for the element Jacobi preconditioner
   std::vector<real> jl;
    jl.resize(residual_dim);

    // loop over the elements of the element set
    for (elem_t e(0); e != _element_set->elements(); ++e) {
        // reset jacobi
        std::fill(jl.begin(), jl.end(), 0.0);

        // calculation of the characteristic length (inradius) of the left and right elements
        // resize vector to store the nodal coordinates (left and right sides) of the interface
        // element e
       std::vector<real> xl;
        xl.resize(_element_set->nodes_element() * _element_set->dim());
        // localize the coordinates
        _element_set->Localize(_coordinates, e, xl);

        // Get the element size of the right element
        real inradius_right = static_cast<ElementSetInterfaceTwoSided const*>(_element_set)
                                ->InRadiusElementRight(&xl[0]);

        // Get the element size of the right element
        real inradius_left = _element_set->InRadiusElementLeft(&xl[0]);

        // integrate over quadrature points
        for (quad_t q(0); q < nquads; ++q) {
            real alpha = _avg_stab_coef_L->local(e, q)[0] / inradius_left +
                         _avg_stab_coef_R->local(e, q)[0] / inradius_right;

            // assembling diag(k^LL)
            // loop over the nodes of the left element
            for (lnode_t c = _element_set->leftNodeStart(); c != _element_set->leftNodeDone();
                 ++c) {
                const real disp_u = _element_set->shape(e, q, c);
                const real* disp_du = _element_set->dShape(e, q, c);

                // loop over its degrees of freedom
                for (size_t i = 0; i < _dof_node; ++i) {
                    // compute the index of the current dof in the local displacement vector
                    size_t ci = _dof_node * c + i;
                    // update the consistency term
                    jl[ci] += ConsistencyTangentMatrixTerm<summit::LEFT>(
                      e, q, disp_u, i, disp_du, i, (_bulkC_L.second)->local(e, q));
                    // update the compatibility term
                    jl[ci] += CompatibilityTangentMatrixTerm<summit::LEFT>(
                      e, q, disp_du, i, disp_u, i, (_bulkC_L.second)->local(e, q), _kappa_ref_L.data());
                    // update the penalty term
                    jl[ci] += StabilizationTangentMatrixTerm<summit::LEFT, summit::LEFT>(
                      e, q, disp_u, i, disp_u, i, alpha);
                }
            }
            // assembling diag(k^RR)
            // loop over the nodes of the left element
            for (lnode_t d =
                   static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeStart();
                 d !=
                 static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeDone();
                 ++d) {
                const real disp_u = _element_set->shape(e, q, d);
                const real* disp_du = _element_set->dShape(e, q, d);

                // loop over its degrees of freedom
                for (size_t i = 0; i < _dof_node; ++i) {
                    // compute the index of the current dof in the local displacement vector
                    size_t di = _dof_node * d + i;
                    // update the consistency term
                    jl[di] += ConsistencyTangentMatrixTerm<summit::RIGHT>(
                      e, q, disp_u, i, disp_du, i, (_bulkC_R.second)->local(e, q));
                    // update the compatibility term
                    jl[di] += CompatibilityTangentMatrixTerm<summit::RIGHT>(
                      e, q, disp_du, i, disp_u, i, (_bulkC_R.second)->local(e, q), _kappa_ref_R.data());
                    // update the penalty term
                    jl[di] += StabilizationTangentMatrixTerm<summit::RIGHT, summit::RIGHT>(
                      e, q, disp_u, i, disp_u, i, alpha);
                }
            }
        }

        // assemble local residual into global array
        _element_set->Assemble(jl, e, jacobi);
    }

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::SetMaterialParameters(
  PropertySheet_t const& sheet, CommunicationManager const& commManager, Mesh const& mesh)
{
    // get the number of integration points
    size_t nquad = _element_set->nquad();
    // loop over the PropertySheet_t
    for (PropertySheet_t::const_iterator pIt = sheet.begin(); pIt != sheet.end(); ++pIt) {
        // position of the property in the internal variable container for the left material
        int leftPosition;
        // size of material property: scalar, vector, tensor; for the left material
        size_t leftSize;
        // ask the left material if he can process the current material property from the internal
        // variable
        bool leftGotIt = _material_L.GetLocationInInternalTable(pIt->first, leftPosition, leftSize);
        // position of the property in the internal variable container for the right material
        int rightPosition;
        // size of material property: scalar, vector, tensor; for the right material
        size_t rightSize;
        // ask the right material if he can process the current material property from the internal
        // variable
        bool rightGotIt =
          _material_R.GetLocationInInternalTable(pIt->first, rightPosition, rightSize);
        // position of the property in the internal variable container for the left material
        int position;
        // size of material property: scalar, vector, tensor; for the left material
        size_t size;
        // ask the interface material if it can process the current material property from
        // the internal variable
        bool gotIt = _material.GetLocationInInternalTable(pIt->first, position, size);
        // if both the left and the right materials are able to process the material parameter
        if (leftGotIt && rightGotIt) {
            // Here, without knowledge of all the element across all the partitions,
            // it is impossible to do size checking
            // then, build the vector of global element index
            std::vector<int> globId;
            _element_set->globalId(globId);
            // container for the left properties of the current element
            std::vector<real> leftElmProp(leftSize);
            // container for the right properties of the current element
            std::vector<real> rightElmProp(rightSize);
            // loop over the elements in the element set
            for (elem_t f(0); f < _element_set->elements(); ++f) {
                // create a mesh entity (most likely a face)
                MeshEntity me(mesh, mesh.dim() - 1, globId[f]);
                // grab its coboundary
                std::vector<int> const& cob = me.OrientedCoBoundary();
                // index of the left bulk element
                int leftBulkId = abs(cob[0]) - 1;
                // index of the right bulk element
                int rightBulkId = abs(cob[1]) - 1;
                // if the leftSize and the rightSize are different
                if (leftSize != rightSize) {
                    // say something useful
                    std::cout << "In ReactionDiffusionInterfaceRegionFullButterfly::SetMaterialParameters with a sheet\n"
                              << "as argument. The buffer sizes of the material parameter for the\n"
                              << "left side and the right side do not match.\n"
                              << "leftSize = " << leftSize << " and rightSize = " << rightSize
                              << std::endl;
                    // complain
                    throw std::logic_error("Inconsistent Sizes");
                }
                // if the leftPosition and the rightPosition are different
                if (leftPosition != rightPosition) {
                    // say something useful
                    std::cout
                      << "In ReactionDiffusionInterfaceRegionFullButterfly::SetMaterialParameters with a functor\n"
                      << "as argument. The position of the material parameter for the\n"
                      << "left side and the right side do not match.\n"
                      << "leftPosition = " << leftPosition
                      << " and rightPosition = " << rightPosition << std::endl;
                    // complain
                    throw std::logic_error("Inconsistent position");
                }
                // fill the left and the right containers with the values in the property sheet
                for (size_t i = 0; i < leftSize; ++i) {
                    // fill each component of the current property with the value corresponding to
                    // the global element id in the property sheet
                    leftElmProp[i] = pIt->second[leftBulkId * leftSize + i];
                    // fill each component of the current property with the value corresponding to
                    // the global element id in the property sheet
                    rightElmProp[i] = pIt->second[rightBulkId * leftSize + i];
                }
                // loop over quadrature points
                for (quad_t q(0); q < nquad; ++q) {
                    // grab the local value of the left internal variables at the current
                    // quadratue point
                    real* leftInternal = _internals_L->local(f, q);
                    // grab the local value of the right internal variables at the current
                    // quadratue point
                    real* rightInternal = _internals_R->local(f, q);
                    real* field_local = _internals_avg->local(f, q);
                    // transfer the material property to the current internal variable
                    for (size_t i = 0; i < leftSize; ++i) {
                        // transfer component by component left ...
                        leftInternal[leftPosition + i] = leftElmProp[i];
                        // ... and right
                        rightInternal[rightPosition + i] = rightElmProp[i];
                        field_local[leftPosition + i] = 0.5 * (leftElmProp[i] + rightElmProp[i]);
                    }
                }
            }
        }
        else {
            if (gotIt) {
                // say something useful
                std::cout
                  << "WARNING: In ReactionDiffusionInterfaceRegionFullButterfly::SetMaterialParameters with a sheet\n"
                  << "as argument. The interface material model is parametric\n"
                  << "and the bulk material model is not.\n"
                  << "(" << pIt->first << ").\n"
                  << std::endl;
            }
        }
        // compute the elastic left tangent modulus
        std::vector<real> P_L_new(_strains_L->dim());
        std::vector<real> f_L_new(_F_L->dim());
        std::vector<real> F_L_new(_strains_L->dim());
        std::vector<real> internals_L_new(_internals_L->dim());
        std::vector<real> ul(_element_set->nodes_element() * _dof_node);


        std::vector<real> concentration_L(_dof_node);
        std::vector<real> concentration_L0(_dof_node);
        std::vector<real> concentration_R(_dof_node);
        std::vector<real> concentration_R0(_dof_node);
        // compute the elastic right tangent modulus
        std::vector<real> P_R_new(_strains_R->dim());
        std::vector<real> f_R_new(_F_R->dim());
        std::vector<real> F_R_new(_strains_R->dim());
        std::vector<real> internals_R_new(_internals_R->dim());

        for (elem_t e(0); e < _element_set->elements(); ++e) {
            for (quad_t q(0); q < _element_set->nquad(); ++q) {
                LeftConstitutiveUpdate(e, q, ul, ul, concentration_L0, concentration_L, 0.0, P_L_new, f_L_new, F_L_new, internals_L_new,
                                       _C_L.data(), _dPdU_L.data(), _dF_L.data());

                RightConstitutiveUpdate(e, q, ul, ul, concentration_R0, concentration_R, 0.0, P_R_new, f_R_new, F_R_new, internals_R_new,
                                        _C_R.data(), _dPdU_R.data(), _dF_R.data());

                for (size_t i = 0; i < _CField_L->dim(); ++i) {
                    _CField_L->local(e, q)[i] = _C_L[i];
                }
                for (size_t i = 0; i < _CField_R->dim(); ++i) {
                    _CField_R->local(e, q)[i] = _C_R[i];
                }
                for (size_t i = 0; i < _dPdUField_R->dim(); ++i) {
                    _dPdUField_R->local(e, q)[i] = _dPdU_R[i] + _dF_R[i];
                }
                for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
                    _dPdUField_L->local(e, q)[i] = _dPdU_L[i] + _dF_L[i];
                }
                for (size_t i = 0; i < _dFField_L->dim(); ++i) {
                    _dFField_L->local(e, q)[i] = _dF_L[i];
                }
                for (size_t i = 0; i < _dFField_R->dim(); ++i) {
                    _dFField_R->local(e, q)[i] = _dF_R[i];
                }
                
            }
        }
    }

    if (_usingFullStabilizationTerm) {
        _computeAveragedFULLStabilizationCoefficientLeft();
        _computeAveragedFULLStabilizationCoefficientRight();
    }
    else {
        _computeAveragedStabilizationCoefficientLeft();
        _computeAveragedStabilizationCoefficientRight();
    }

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::SetMaterialParameters(
  std::string const& name,
  Functor<real> const& functor,
  CommunicationManager const& commManager,
  Mesh const& mesh)
{
    // get the number of integration points
    size_t nquad = _element_set->nquad();
    // position of the property in the internal variable container for the left material
    int leftPosition;
    // size of material property: scalar, vector, tensor; for the left material
    size_t leftSize;
    // ask the left material if he can process the current material property from the internal
    // variable
    bool leftGotIt = _material_L.GetLocationInInternalTable(name, leftPosition, leftSize);
    // position of the property in the internal variable container for the right material
    int rightPosition;
    // size of material property: scalar, vector, tensor; for the right material
    size_t rightSize;
    // ask the right material if he can process the current material property from the internal
    // variable
    bool rightGotIt = _material_R.GetLocationInInternalTable(name, rightPosition, rightSize);
    // position of the property in the internal variable container for the left material
    int position;
    // size of material property: scalar, vector, tensor; for the left material
    size_t size;
    // ask the interface material if it can process the current material property from
    // the internal variable
    bool gotIt = _material.GetLocationInInternalTable(name, position, size);
    // if both the left and the right materials are able to process the material parameter
    if (leftGotIt && rightGotIt) {
        // Here, without knowledge of all the element across all the partitions,
        // it is impossible to do size checking
        // then, build the vector of global element index
        std::vector<int> globId;
        _element_set->globalId(globId);
        // container for the left properties of the current element
        std::vector<real> leftElmProp(leftSize);
        // container for the right properties of the current element
        std::vector<real> rightElmProp(rightSize);
        // loop over the elements in the element set
        for (elem_t f(0); f < _element_set->elements(); ++f) {
            // create a mesh entity (most likely a face)
            MeshEntity me(mesh, mesh.dim() - 1, globId[f]);
            // grab its coboundary
            std::vector<int> const& cob = me.OrientedCoBoundary();
            // index of the left bulk element
            int leftBulkId = abs(cob[0]) - 1;
            // index of the right bulk element
            int rightBulkId = abs(cob[1]) - 1;
            // create a mesh entity
            MeshEntity leftBulk(mesh, mesh.dim(), leftBulkId);
            // ~> allocate memory for centroid cartesian coordinates array
            std::vector<real> leftBulkcentroid(mesh.dim(), 0.0);
            for (int i = 0; i < mesh.dim(); i++) {
                double const* myLeftCentroid = leftBulk.centroid();
                leftBulkcentroid[i] = myLeftCentroid[i];
                delete[] myLeftCentroid;
            }
            // create a mesh entity
            MeshEntity rightBulk(mesh, mesh.dim(), rightBulkId);
            // ~> allocate memory for centroid cartesian coordinates array
            std::vector<real> rightBulkcentroid(mesh.dim(), 0.0);
            for (int i = 0; i < mesh.dim(); i++) {
                double const* myRightCentroid = rightBulk.centroid();
                rightBulkcentroid[i] = myRightCentroid[i];
                delete[] myRightCentroid;
            }
            // if the leftSize and the rightSize are different
            if (leftSize != rightSize) {
                // say something useful
                std::cout << "In ReactionDiffusionInterfaceRegionFullButterfly::SetMaterialParameters with a functor\n"
                          << "as argument. The buffer sizes of the material parameter for the\n"
                          << "left side and the right side do not match.\n"
                          << "leftSize = " << leftSize << " and rightSize = " << rightSize
                          << std::endl;
                // complain
                throw std::logic_error("Inconsistent Sizes");
            }
            // if the leftPosition and the rightPosition are different
            if (leftPosition != rightPosition) {
                // say something useful
                std::cout << "In ReactionDiffusionInterfaceRegionFullButterfly::SetMaterialParameters with a functor\n"
                          << "as argument. The position of the material parameter for the\n"
                          << "left side and the right side do not match.\n"
                          << "leftPosition = " << leftPosition
                          << " and rightPosition = " << rightPosition << std::endl;
                // complain
                throw std::logic_error("Inconsistent position");
            }
            // fill the left and the right containers with the values in the property sheet
            for (size_t i = 0; i < leftSize; ++i) {
                // fill each component of the current property with the value corresponding to
                // the global element id in the property sheet
                leftElmProp[i] = functor.get(leftBulkcentroid)[i];
                // fill each component of the current property with the value corresponding to
                // the global element id in the property sheet
                rightElmProp[i] = functor.get(rightBulkcentroid)[i];
            }
            // loop over quadrature points
            for (quad_t q(0); q < nquad; ++q) {
                // grab the local value of the left internal variables at the current
                // quadrature point
                real* leftInternal = _internals_L->local(f, q);
                // grab the local value of the right internal variables at the current
                // quadratue point
                real* rightInternal = _internals_R->local(f, q);
                real* field_local = _internals_avg->local(f, q);
                // transfer the material property to the current internal variable
                for (size_t i = 0; i < leftSize; ++i) {
                    // transfer component by component left ...
                    leftInternal[leftPosition + i] = leftElmProp[i];
                    // ... and right
                    rightInternal[rightPosition + i] = rightElmProp[i];
                    field_local[leftPosition + i] = 0.5 * (leftElmProp[i] + rightElmProp[i]);
                }
            }
        }
    }
    else {
        if (gotIt) {
            // say something useful
            std::cout
              << "WARNING: In ReactionDiffusionInterfaceRegionFullButterfly::SetMaterialParameters with a functor\n"
              << "as argument. The interface material model is parametric\n"
              << "and the bulk material model is not.\n"
              << "(" << name << ").\n"
              << std::endl;
        }
    }
    // compute the elastic left tangent modulus
    std::vector<real> P_L_new(_strains_L->dim());
    std::vector<real> f_L_new(_F_L->dim());
    std::vector<real> F_L_new(_strains_L->dim());
    std::vector<real> concentration_L(_dof_node);
    std::vector<real> concentration_L0(_dof_node);

    std::vector<real> concentration_R(_dof_node);
    std::vector<real> concentration_R0(_dof_node);
    std::vector<real> internals_L_new(_internals_L->dim());
    std::vector<real> ul(_element_set->nodes_element() * _dof_node);

    // compute the elastic right tangent modulus
    std::vector<real> P_R_new(_strains_R->dim());
    std::vector<real> f_R_new(_F_R->dim());
    std::vector<real> F_R_new(_strains_R->dim());
    std::vector<real> internals_R_new(_internals_R->dim());

    for (elem_t e(0); e < _element_set->elements(); ++e) {
        for (quad_t q(0); q < _element_set->nquad(); ++q) {
            LeftConstitutiveUpdate(e, q, ul, ul, concentration_L0, concentration_L, 0.0, P_L_new, f_L_new, F_L_new, internals_L_new, _C_L.data(), _dPdU_L.data(), _dF_L.data());

            RightConstitutiveUpdate(e, q, ul, ul, concentration_R0, concentration_R, 0.0, P_R_new, f_R_new, F_R_new, internals_R_new, _C_R.data(), _dPdU_R.data(), _dF_R.data());

            for (size_t i = 0; i < _CField_L->dim(); ++i) {
                _CField_L->local(e, q)[i] = _C_L[i];
            }
            for (size_t i = 0; i < _CField_R->dim(); ++i) {
                _CField_R->local(e, q)[i] = _C_R[i];
            }
            for (size_t i = 0; i < _dPdUField_R->dim(); ++i) {
                _dPdUField_R->local(e, q)[i] = _dPdU_R[i] + _dF_R[i];
            }
            for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
                _dPdUField_L->local(e, q)[i] = _dPdU_L[i] + _dF_L[i];
            }
            for (size_t i = 0; i < _dFField_L->dim(); ++i) {
                _dFField_L->local(e, q)[i] = _dF_L[i];
                }
            for (size_t i = 0; i < _dFField_R->dim(); ++i) {
                _dFField_R->local(e, q)[i] = _dF_R[i];
            }
            
        }
    }

    if (_usingFullStabilizationTerm) {
        _computeAveragedFULLStabilizationCoefficientLeft();
        _computeAveragedFULLStabilizationCoefficientRight();
    }
    else {
        _computeAveragedStabilizationCoefficientLeft();
        _computeAveragedStabilizationCoefficientRight();
    }

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::SetMaterialParameters(std::string const& name)
{
    // get the number of integration points
    size_t nquad = _element_set->nquad();
    // position of the property in the internal variable container for the left material
    int leftPosition;
    // size of material property: scalar, vector, tensor; for the left material
    size_t leftSize;
    // ask the left material if he can process the current material property from the internal
    // variable
    bool leftGotIt = _material_L.GetLocationInInternalTable(name, leftPosition, leftSize);
    // position of the property in the internal variable container for the right material
    int rightPosition;
    // size of material property: scalar, vector, tensor; for the right material
    size_t rightSize;
    // ask the right material if he can process the current material property from the internal
    // variable
    bool rightGotIt = _material_R.GetLocationInInternalTable(name, rightPosition, rightSize);
    // position of the property in the internal variable container for the left material
    int position;
    // size of material property: scalar, vector, tensor; for the left material
    size_t size;
    // ask the interface material if it can process the current material property from
    // the internal variable
    bool gotIt = _material.GetLocationInInternalTable(name, position, size);
    // if both the left and the right materials are able to process the material parameter
    if (leftGotIt && rightGotIt) {
        // if the leftSize and the rightSize are different
        if (leftSize != rightSize) {
            // say something useful
            std::cout << "In ReactionDiffusionInterfaceRegionFullButterfly::SetMaterialParameters\n"
                      << "The buffer sizes of the material parameter for the\n"
                      << "left side and the right side do not match.\n"
                      << "leftSize = " << leftSize << " and rightSize = " << rightSize << std::endl;
            // complain
            throw std::logic_error("Inconsistent Sizes");
        }
        // if the leftPosition and the rightPosition are different
        if (leftPosition != rightPosition) {
            // say something useful
            std::cout << "In ReactionDiffusionInterfaceRegionFullButterfly::SetMaterialParameters\n"
                      << "The position of the material parameter for the\n"
                      << "left side and the right side do not match.\n"
                      << "leftPosition = " << leftPosition
                      << " and rightPosition = " << rightPosition << std::endl;
            // complain
            throw std::logic_error("Inconsistent position");
        }
        // found it
        if (gotIt) {
            // if the leftSize and the size are different
            if (leftSize != size) {
                // say something useful
                std::cout << "In ReactionDiffusionInterfaceRegionFullButterfly::SetMaterialParameters\n"
                          << "The buffer sizes of the material parameter for the\n"
                          << "left side and the interface do not match.\n"
                          << "leftSize = " << leftSize << " and size = " << size << std::endl;
                // complain
                throw std::logic_error("Inconsistent Sizes");
            }
            // loop over the elements in the element set
            for (elem_t f(0); f < _element_set->elements(); ++f) {
                // loop over quadrature points
                for (quad_t q(0); q < nquad; ++q) {
                    // grab the local value of the interface internal variables at the current
                    // quadratue point
                    real const* field_local = _internals_avg->local(f, q);
                    real* internal = _internals->local(f, q);
                    for (size_t i = 0; i < leftSize; ++i) {
                        // set the mean value where it should go
                        internal[position + i] = field_local[leftPosition + i];
                    }
                }
            }
        }
    }
    else {
        if (gotIt) {
            // say something useful
            std::cout << "WARNING: In ReactionDiffusionInterfaceRegionFullButterfly::SetMaterialParameters\n"
                      << "The interface material model is parametric\n"
                      << "and the bulk material model is not.\n"
                      << "(" << name << ").\n"
                      << std::endl;
        }
    }

    // all done
    return;
}


void summit::ReactionDiffusionInterfaceRegionFullButterfly::getGlobalToLocalElementMap(
  InternalBoundary const& dB_I, std::map<int, int>& globalToLocal) const
{
    // loop over the mesh entities in the internal boundary
    for (Boundary::MeshEntityConstIterator_t meIt = dB_I.begin(); meIt != dB_I.end(); ++meIt) {
        // try to find the current global element id in the map
        ElementSet::mapGlobalToLocalConstIt_t mIt =
          _element_set->globalToLocalElementMap().find((*meIt)->index());
        // if the element of current global index is found in the element set
        if (mIt != _element_set->globalToLocalElementMap().end()) {
            // fill the map
            globalToLocal[(*meIt)->index()] = mIt->second;
        }
    }

    // all done
    return;
}


void summit::ReactionDiffusionInterfaceRegionFullButterfly::ElementaryConstitutive(elem_t e,
                                                              real dt,
                                                              bool update,
                                                              std::vector<real> const& ul,
                                                              std::vector<real> const& u0l,
                                                              real* TJ)
{
    //std::cout << "I am in ReactionDiffusionInterfaceRegionFullButterfly::ElementaryConstitutive. Update is: " << update <<std::endl;
    // strain dimension
    int const strain_dim = _strains_L->dim();
    // dimension of left and right internal variables
    size_t const internal_L_dim = _material_L.nInt();
    size_t const internal_R_dim = _material_R.nInt();
    // spatial dimension
    size_t const spatial_dim = _element_set->dim();

    // memory allocation ...
    // ... for the left part
   std::vector<real> P_L_new;
    P_L_new.resize(strain_dim);
   std::vector<real> f_L_new;
    f_L_new.resize(strain_dim);
    // memory allocation
   std::vector<real> F_L_new;
    F_L_new.resize(spatial_dim * _dof_node);

   std::vector<real> concentration_L;
    concentration_L.resize(_dof_node);
   std::vector<real> concentration_R;
    concentration_R.resize(_dof_node);
   std::vector<real> concentration_L0;
    concentration_L0.resize(_dof_node);
   std::vector<real> concentration_R0;
    concentration_R0.resize(_dof_node);


   std::vector<real> internal_L_new;
    internal_L_new.resize(internal_L_dim);
    // ... for the right part
   std::vector<real> P_R_new;
    P_R_new.resize(strain_dim);
   std::vector<real> f_R_new;
    f_R_new.resize(strain_dim);
    // memory allocation
   std::vector<real> F_R_new;
    F_R_new.resize(spatial_dim * _dof_node);
   std::vector<real> internal_R_new;
    internal_R_new.resize(internal_R_dim);
    // ... for the jump in displacement
   std::vector<real> jumpU;
    jumpU.resize(_dof_node);
    // ... for the left traction
   std::vector<real> left_traction;
    left_traction.resize(_dof_node);
    // ... for the interface material
   std::vector<real> interfaceMaterial_bufferForComm;
    interfaceMaterial_bufferForComm.resize(_material.materialBufferSizeForComm(spatial_dim));

    // calculation of the characteristic length (inradius) of the left and right elements
    // resize vector to store the nodal coordinates (left and right sides) of the interface
    // element e
   std::vector<real> xl;
    xl.resize(_element_set->nodes_element() * _element_set->dim());
    // localize the coordinates
    _element_set->Localize(_coordinates, e, xl);

    // Get the element size of the right element
    real inradius_right =
      static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->InRadiusElementRight(&xl[0]);

    // Get the element size of the right element
    real inradius_left = _element_set->InRadiusElementLeft(&xl[0]);

    // integrate over quadrature points
    for (quad_t q(0); q < _element_set->nquad(); ++q) {
        // constitutive update of the left element
	    for (size_t i = 0; i < _CField_L->dim(); ++i) {
            _CField_L->local(e, q)[i] = 0.0;
            _C_L[i] = 0.0;
        }
        for (size_t i = 0; i < _CField_R->dim(); ++i) {
            _CField_R->local(e, q)[i] = 0.0;
            _C_R[i] = 0.0;
        }
        for (size_t i = 0; i < _dPdUField_R->dim(); ++i) {
            _dPdUField_R->local(e, q)[i] = 0.0;
            _dPdU_R[i] = 0.0;
        }
        for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
            _dPdUField_L->local(e, q)[i] = 0.0;
            _dPdU_L[i] = 0.0;
        }
	    for (size_t i = 0; i < _dFField_L->dim(); ++i) {
            _dFField_L->local(e, q)[i] = 0.0;
	        _dF_L[i] =0.0;
        }
        for (size_t i = 0; i < _dFField_R->dim(); ++i) {
            _dFField_R->local(e, q)[i] = 0.0;
            _dF_R[i] = 0.0;
        }
        LeftConstitutiveUpdate(e, q, ul, u0l, concentration_L0, concentration_L, dt, P_L_new, f_L_new, F_L_new, internal_L_new, _C_L.data(), _dPdU_L.data(), _dF_L.data());
        // constitutive update of the right element
        RightConstitutiveUpdate(e, q, ul, u0l, concentration_R0, concentration_R, dt, P_R_new, f_R_new, F_R_new, internal_R_new, _C_R.data(), _dPdU_R.data(), _dF_R.data());
        for (size_t i = 0; i < _CField_L->dim(); ++i) {
            _CField_L->local(e, q)[i] = _C_L[i];
        }
        for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
            _dPdUField_L->local(e, q)[i] = _dPdU_L[i] + _dF_L[i];
        }
        for (size_t i = 0; i < _CField_R->dim(); ++i) {
            _CField_R->local(e, q)[i] = _C_R[i];
        }
        for (size_t i = 0; i < _dPdUField_R->dim(); ++i) {
            _dPdUField_R->local(e, q)[i] = _dPdU_R[i] + _dF_R[i];
        }
        for (size_t i = 0; i < _dFField_L->dim(); ++i) {
            _dFField_L->local(e, q)[i] = _dF_L[i];
        }
        for (size_t i = 0; i < _dFField_R->dim(); ++i) {
            _dFField_R->local(e, q)[i] = _dF_R[i];
        }
        // jacobians (with quadrature weights)
        real jac = _element_set->jac(e, q);

        // reference to the left normal
        const real* normalL = _element_set->normalL(e, q);

        // compute the displacement jump at the current quad point
        DisplacementJump(e, q, jac, ul, jumpU);

       std::vector<real> dC;
        dC.resize(_dof_node*_dof_node*2);// derivative of stab term with respect to left
        // and right state
        
       std::vector<real> C;
        C.resize(_dof_node);
        _material_R.upwindStabilization(&concentration_L[0], &concentration_L0[0], &concentration_R[0], &concentration_R0[0], normalL, &C[0], &dC[0]);
        for (size_t i = 0; i < _dof_node; ++i) {
            for (dim_t j(0); j < spatial_dim; ++j) {
                size_t ij = spatial_dim * i + j;
                P_L_new[ij] += f_L_new[ij];
                P_R_new[ij] += f_R_new[ij];
            }
        }
        AverageTraction(P_L_new, P_R_new, jac, normalL, left_traction);
        for (size_t i = 0; i < _dof_node; ++i) {
            left_traction[i] += C[i] * jac;
        }
        // evaluate the failure criterion at the interface for left and right elements
        // vector interfaceMaterial_bufferForComm stores the information to decide if the cohesive
        // law (traction separation law) has to be used or not.
        InterfaceMaterialFailureCriterionEvaluation(e, q, P_L_new, F_L_new, P_R_new, F_R_new,
                                                    normalL, interfaceMaterial_bufferForComm);

        // copy to global array
        // left traction
        std::copy(left_traction.begin(), left_traction.end(), TJ);
        // displacement jump
        std::copy(jumpU.begin(), jumpU.end(), TJ + _dof_node);
        // h_left
        TJ[2 * _dof_node] = inradius_left;
        // h_right
        TJ[2 * _dof_node + 1] = inradius_right;
        // buffer from interface material
        std::copy(interfaceMaterial_bufferForComm.begin(), interfaceMaterial_bufferForComm.end(),
                  TJ + 2 * _dof_node + 2);

        for (size_t i = 0; i < _avg_stab_coef_L->dim(); ++i) {
            TJ[2 * _dof_node + 2 + interfaceMaterial_bufferForComm.size() + i] =
              _avg_stab_coef_L->local(e, q)[i] / inradius_left +
              _avg_stab_coef_R->local(e, q)[i] / inradius_right;
        }

        TJ += bufferSizeForComm();

        // update if necessary
        if (update) {
            this->StoreQuadraturePointsVariables(
              e, q, ul, dt, P_L_new, F_L_new, internal_L_new, _stresses_L, _strains_L, _internals_L,
              P_R_new, F_R_new, internal_R_new, _stresses_R, _strains_R, _internals_R);
        }
    }

    // end of method
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::Interpolate(NodalField<real> const& nodal_field,
                                                   ElementQuadratureField<real>& quad_field) const
{
    // dimension of the nodal field
    const size_t ndim = nodal_field.dim();
    // check if the dimension of the nodal field is equivalent to the one of the quadrature field
    assert(ndim == quad_field.dim());
    // number of quadrature points per element
    size_t nquad = _element_set->nquad();
    // instantiate a local container
    size_t nen = _element_set->nodes_element();
    std::vector<real> local_nodal_field(ndim * nen);

    // loop over the elements constituting the region
    size_t nElements = _element_set->elements();
    for (elem_t e(0); e < nElements; ++e) {
        // extract local part of unknown
        _element_set->Localize(nodal_field, e, local_nodal_field);

        // loop over the quadrature points
        for (quad_t q(0); q < nquad; ++q) {
            real* local_quad_field = quad_field.local(e, q);
            // loop over the values of local quad field
            for (size_t i = 0; i < ndim; ++i) {
                local_quad_field[i] = 0.0;
                for (lnode_t a(0); a < nen; ++a) {
                    // Divide by two the contribution, since ElementSetInterfaceTwoSided have twice
                    // the shape functions on the interface
                    // Here, the out-of-interface shape function always contributes with 0
                    local_quad_field[i] +=
                      local_nodal_field[ndim * a + i] * _element_set->shape(e, q, a) / 2;
                }
            }
        }  // end of loop over the quadrature points
    }      // end of loop over the elements

    // end of method
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::StoreQuadraturePointsVariables(
  elem_t e,
  quad_t q,
  std::vector<real> const& ul,
  const real dt,
  std::vector<real> const& P_L,
  std::vector<real> const& F_L,
  std::vector<real> const& internal_data_L,
  ElementQuadratureField<real>* stress_L,
  ElementQuadratureField<real>* strain_L,
  ElementQuadratureField<real>* internals_L,
  std::vector<real> const& P_R,
  std::vector<real> const& F_R,
  std::vector<real> const& internal_data_R,
  ElementQuadratureField<real>* stress_R,
  ElementQuadratureField<real>* strain_R,
  ElementQuadratureField<real>* internals_R)
{
    std::copy(P_L.begin(), P_L.end(), stress_L->local(e, q));
    std::copy(F_L.begin(), F_L.end(), strain_L->local(e, q));
    std::copy(internal_data_L.begin(), internal_data_L.end(), internals_L->local(e, q));
    std::copy(P_R.begin(), P_R.end(), stress_R->local(e, q));
    std::copy(F_R.begin(), F_R.end(), strain_R->local(e, q));
    std::copy(internal_data_R.begin(), internal_data_R.end(), internals_R->local(e, q));
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::ElementaryResidual(elem_t e,
                                                          const std::vector<real>& ul,
                                                          const real* TJ_ElementaryResidual,
                                                          std::vector<real>& rl,
                                                          const bool update)
{
    // DG flux (size of container is _dof_node)
    const real* dg_traction;
    // displacement jump (size of container is _dof_node)
    const real* jumpU;
    // traction compute with cohesive law (size of container is _dof_node)
    const real* cohesive_traction;
    // coefficients to activate the traction-separation-law (TSL) (size of container is 2)
    const real* coefficients;
    // vector containing the normal (size of container is _dof_node)
    const real* normal;
    // vector containing the contracted stabilization coefficient
    // (size of container is _dof_node * _dof_node)
    const real* averg_hs_C_q;

    // integrate over quadrature points
    for (quad_t q(0); q < _element_set->nquad(); ++q) {
        // extract P_AVG_new (DG flux)
        // from TJ_ElementaryResidual to TJ_ElementaryResidual + _dof_node
        dg_traction = TJ_ElementaryResidual;

        // extract jumpU (displacement jump)
        // from TJ_ElementaryResidual + _dof_node to TJ_ElementaryResidual + 2 * _dof_node
        jumpU = TJ_ElementaryResidual + _dof_node;

        // extract cohesive traction
        // from TJ_ElementaryResidual+2*_dof_node+2 to TJ_ElementaryResidual+3*_dof_node+2
        cohesive_traction = TJ_ElementaryResidual + 2 * _dof_node + 2;

        // extract coefficients to activate the TSL and the contact under compression
        // from TJ_ElementaryResidual+3*_dof_node+2 to TJ_ElementaryResidual+3*_dof_node+2+2
        coefficients = TJ_ElementaryResidual + 3 * _dof_node + 2;

        // extract the normal in the deformed configuration
        // from TJ_ElementaryResidual+3*_dof_node+2+2 to TJ_ElementaryResidual+4*_dof_node+2+2
        normal = TJ_ElementaryResidual + 3 * _dof_node + 2 + 2;

        // extract the contracted stabilization coefficient
        // from TJ_ElementaryResidual+4*_dof_node+2+2
        // to   TJ_ElementaryResidual+4*_dof_node+2+2+_dof_node * _dof_node
        averg_hs_C_q = TJ_ElementaryResidual + 3 * _dof_node + 4 + _element_set->dim();

        // compute the part of the residual at the current quadrature point
        _residualIntegrand(e, q, ul, averg_hs_C_q, jumpU, dg_traction, cohesive_traction,
                           coefficients, normal, rl);

        // increment to the next quadrature point
        TJ_ElementaryResidual += ComputeDimensionForTJElementaryResidual();
    }

    // all done
    return;
}

summit::Region::IVsheet_t summit::ReactionDiffusionInterfaceRegionFullButterfly::getSideInternalVariableFields(
  std::string const& name, SIDE side) const
{
    // position of the property in the internal variable container
    int position;
    // size of material property: scalar, vector, tensor
    size_t size;
    // ask the material if he can process the current material property from the internal
    // variable and set the size which is passed by reference.
    bool gotIt = false;
    switch (side) {
        case LEFT:
            gotIt = _material_L.GetLocationInInternalTable(name, position, size);
            return std::make_tuple(gotIt, position, size, _internals_L);
            break;
        case RIGHT:
            gotIt = _material_R.GetLocationInInternalTable(name, position, size);
            return std::make_tuple(gotIt, position, size, _internals_R);
            break;
        default:
            std::cout << "Error in ReactionDiffusionInterfaceRegionFullButterfly : cannot create a side of type: " << side
                    << std::endl;
            exit(1);
            return std::make_tuple(false, position, size, _internals_R);
            break;
    }
    // all done
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::setSideInternalVariableFields(
  Region::IVsheet_t const& ivField,
  std::string const& name, SIDE side)
{
    // position of the property in the solid internal variable container
    int position;
    // size of material property: scalar, vector, tensor; for the solid
    size_t size;
    switch (side) {
        case LEFT:{
            bool solidGotIt = _material_L.GetLocationInInternalTable(name, position, size);
            // I dont understand why this is a compilation error?
            bool GotIt = std::get<0>(ivField);
            if (!GotIt) {
                // say something useful
                std::cout
                << "ReactionDiffusionInterfaceRegionFullButterfly::setInternalVariableFields: ivField does not have the information you wanted"
                << std::endl;
                // complain
                throw std::logic_error("Bad Transfer");
            }
            // local position in the container of internal variable
            int Position = std::get<1>(ivField);
            // local size of internal variable
            size_t Size = (size_t)(std::get<2>(ivField));
            if (Size != size) {
                // say something useful
                std::cout << "ReactionDiffusionInterfaceRegionFullButterfly::setSideInternalVariableFields: trying to pass\n"
                        << "data of different size" << std::endl;
                // complain
                throw std::logic_error("Inconsistent Sizes");
            }
            // loop over the elements of the element set
            for (elem_t e(0); e < this->element_set()->elements(); ++e) {
                for (quad_t q(0); q < this->element_set()->nquad(); ++q) {
                    // local value of internal variables
                    real* internal = _internals_L->local(e, q);
                    // local value of internal variables
                    real const* Internal = std::get<3>(ivField)->local(e, q);
                    // loop over the size
                    for (size_t j = 0; j < size; ++j) {
                        internal[position + j] = Internal[Position + j];
                    }
                }
            } 
            return;
        }
        case RIGHT:{
            bool solidGotIt = _material_R.GetLocationInInternalTable(name, position, size);
            // I dont understand why this is a compilation error?
            bool GotIt = std::get<0>(ivField);
            if (!GotIt) {
                // say something useful
                std::cout
                << "ReactionDiffusionInterfaceRegionFullButterfly::setInternalVariableFields: ivField does not have the information you wanted"
                << std::endl;
                // complain
                throw std::logic_error("Bad Transfer");
            }
            // local position in the container of internal variable
            int Position = std::get<1>(ivField);
            // local size of internal variable
            size_t Size = (size_t)(std::get<2>(ivField));
            if (Size != size) {
                // say something useful
                std::cout << "ReactionDiffusionInterfaceRegionFullButterfly::setSideInternalVariableFields: trying to pass\n"
                        << "data of different size" << std::endl;
                // complain
                throw std::logic_error("Inconsistent Sizes");
            }
            // loop over the elements of the element set
            for (elem_t e(0); e < this->element_set()->elements(); ++e) {
                for (quad_t q(0); q < this->element_set()->nquad(); ++q) {
                    // local value of internal variables
                    real* internal = _internals_R->local(e, q);
                    // local value of internal variables
                    real const* Internal = std::get<3>(ivField)->local(e, q);
                    // loop over the size
                    for (size_t j = 0; j < size; ++j) {
                        internal[position + j] = Internal[Position + j];
                    }
                }
            } 
            return;
        }
        default:
            std::cout << "Error in ReactionDiffusionInterfaceRegionFullButterfly : cannot create a side of type: " << side
                    << std::endl;
            exit(1);
            return;
    }
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::_residualIntegrand(elem_t e,
                                                          quad_t q,
                                                          const std::vector<real>& ul,
                                                          const real* averg_hs_C,
                                                          const real* dispJump,
                                                          const real* dgTraction,
                                                          const real* cohesive_traction,
                                                          const real* coefficients,
                                                          const real* normal,
                                                          std::vector<real>& rl)
{
    std::vector<real> my_damage(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q), my_damage.data());
    std::vector<int> my_types(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceTypes(_internals->local(e, q), my_types.data());
    std::vector<real> my_values(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceValues(_internals->local(e, q), my_values.data());

    // coefficients to activate/deactivate the DG fluxes and the TSL
    // by default, the TSL is considered not to have been activated
    // activate the consistency term
    real coeff_consistencyTerm = 1.0;// - (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q));
    // activate the compatibility term
    real coeff_compatibilityTerm = 1.0;// - (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q));
    // activate the stabilization term
    real coeff_stabilizationTerm = 1.0;// - (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q));
    // de-activate the cohesive term
    real coeff_cohesiveLawTerm = 0.0;

    bool deleteArrays = false;

    std::vector<real> rightConcentration(_dof_node);
    std::fill(rightConcentration.begin(), rightConcentration.end(), 0.);
    for (lnode_t b = static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeStart(); b != static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeDone(); ++b) {
        for (int c = 0; c < _dof_node; c++) {
            rightConcentration[c] += _element_set->shape(e, q, b) * ul[c + b * _dof_node];
        }
    }
    std::vector<real> leftConcentration(_dof_node);
    std::fill(leftConcentration.begin(), leftConcentration.end(), 0.);
    for (lnode_t b = _element_set->leftNodeStart(); b != _element_set->leftNodeDone(); ++b) {
        for (int c = 0; c < _dof_node; c++) {
            leftConcentration[c] += _element_set->shape(e, q, b) * ul[c + b * _dof_node];
        }
    }

    // loop over the nodes of the left element
    for (lnode_t c = _element_set->leftNodeStart(); c != _element_set->leftNodeDone(); ++c) {
        const real test_u = _element_set->shape(e, q, c);
        const real* test_du = _element_set->dShape(e, q, c);

        // loop over its degrees of freedom
        for (size_t i = 0; i < _dof_node; ++i) {
            // compute the index of the current dof in the local displacement vector
            size_t ci = _dof_node * c + i;
            // Here, we compute MINUS the internal forces because the assemble function only adds
            // to the residual: residual = f^ext - f^int
            // update the consistency term
            rl[ci] -= coeff_consistencyTerm * (1.0 - my_damage[i]) *
                      TractionResidualTerm<summit::LEFT>(e, q, test_u, i, dgTraction);
// update the compatibility term
#if IMR_COMPATIBILITY
            rl[ci] -=
              coeff_compatibilityTerm * (1.0 - my_damage[i]) *
              CompatibilityResidualTerm(e, q, test_du, test_u, i, dispJump, (_bulkC_L.second)->local(e, q), _kappa_ref_L.data());
#endif  // IMR_COMPATIBILITY
        // update the penalty term
            if (_usingFullStabilizationTerm) {
                rl[ci] -= coeff_stabilizationTerm * (1.0 - my_damage[i]) * StabilizationResidualTermFULL<summit::LEFT>(
                                                      e, q, test_u, i, dispJump, averg_hs_C);
            }
            else {
                rl[ci] -= coeff_stabilizationTerm * (1.0 - my_damage[i]) * StabilizationResidualTerm<summit::LEFT>(
                                                      e, q, test_u, i, dispJump, averg_hs_C[i]);
            }
            // update the cohesive law
            if(my_types[i]==0){
                rl[ci] -= my_damage[i] *
                          StabilizationResidualTerm<summit::LEFT>(e, q, test_u, i, dispJump, my_values[i]);
            }else{
                real myJump = -(leftConcentration[i] - my_values[i]);
                myJump *= _element_set->jac(e, q);
                // std::cout << "myJump: " << myJump <<std::endl;
                rl[ci] -= my_damage[i] *
                          StabilizationResidualTerm<summit::LEFT>(e, q, test_u, 0, &myJump, averg_hs_C[i]);
            }
        }
    }

    if (_computeRightPart) {
        // loop over the nodes of the right element
        for (lnode_t d =
               static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeStart();
             d != static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeDone();
             ++d) {
            const real test_u = _element_set->shape(e, q, d);
            const real* test_du = _element_set->dShape(e, q, d);

            // loop over its degrees of freedom
            for (size_t i = 0; i < _dof_node; ++i) {
                // compute the index of the current dof in the local displacement vector
                size_t di = _dof_node * d + i;
                // Here, we compute MINUS the internal forces because the assemble function only
                // adds to the residual: residual = f^ext - f^int
                // update the consistency term
                rl[di] -= coeff_consistencyTerm * (1.0 - my_damage[i]) *
                          TractionResidualTerm<summit::RIGHT>(e, q, test_u, i, dgTraction);
// update the compatibility term
#if IMR_COMPATIBILITY
                rl[di] -= coeff_compatibilityTerm * (1.0 - my_damage[i]) *
                          CompatibilityResidualTerm(e, q, test_du, test_u, i, dispJump,
                                                    (_bulkC_R.second)->local(e, q), _kappa_ref_R.data());
#endif  // IMR_COMPATIBILITY
        // update the penalty term
                if (_usingFullStabilizationTerm) {
                    rl[di] -=
                      coeff_stabilizationTerm * (1.0 - my_damage[i]) * StabilizationResidualTermFULL<summit::RIGHT>(
                                                  e, q, test_u, i, dispJump, averg_hs_C);
                }
                else {
                    rl[di] -= coeff_stabilizationTerm * (1.0 - my_damage[i]) * StabilizationResidualTerm<summit::RIGHT>(
                                                          e, q, test_u, i, dispJump, averg_hs_C[i]);
                }
                // update the cohesive law
                if(my_types[i]==0){
                    rl[di] -= my_damage[i] *
                          StabilizationResidualTerm<summit::RIGHT>(e, q, test_u, i, dispJump, my_values[i]);
                }else{
                    real myJump = (rightConcentration[i] - my_values[i]);
                    myJump *= _element_set->jac(e, q);
                    // std::cout << "myJump: " << myJump <<std::endl;
                    rl[di] -= my_damage[i] *
                          StabilizationResidualTerm<summit::RIGHT>(e, q, test_u, 0, &myJump, averg_hs_C[i]);
                }
            }
        }
    }

    if (deleteArrays) {
        delete[] dispJump;
        delete[] dgTraction;
        delete[] cohesive_traction;
    }

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::QuadratureResidual(
  elem_t e,
  quad_t q,
  const std::vector<real>& ul,
  real alpha,
  std::vector<real> const& jumpU,
  std::vector<real> const& left_traction,
  std::vector<real> const& cohesive_traction,
  std::vector<real> const& coefficients,
  std::vector<real>& rl)
{
    // say something useful
    std::cout << "To avoid calling a virtual function at each quadrature point, "
              << "ReactionDiffusionInterfaceRegionFullButterfly::QuadratureResidual has been replaced by "
              << "ReactionDiffusionInterfaceRegionFullButterfly::_residualIntegrand" << std::endl;

    // complain
    throw std::logic_error("Not implemented");
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::ElementaryStiffness(elem_t e,
                                                           real dt,
                                                           std::vector<real> const& ul,
                                                           std::vector<real> const& u0l,
                                                           std::vector<real>& rl,
                                                           std::vector<real>& Kl)
{
#if 1
    size_t const nquads = _element_set->nquad();

    // Compute the left traction and the displacement jump
    // for all Gauss point at the same time using elementary constitutive
   ElementQuadratureField<real> TJ;
    TJ.resize(1, _element_set->nquad(), this->bufferSizeForComm());
    ElementaryConstitutive(e, dt, false, ul, u0l, TJ.local(elem_t(0), quad_t(0)));

    // array for the interface material tangent computed in case of fracture
   std::vector<real> interface_tangent;
    interface_tangent.resize(_element_set->nquad() * _dof_node * _dof_node);
    std::fill(interface_tangent.begin(), interface_tangent.end(), 0.0);

    // Interface computation
    // summation of dimensions of (i), (ii), (iii), (iv), and (v)
    size_t dim_TJ_ElementaryResidual = ComputeDimensionForTJElementaryResidual();
   ElementQuadratureField<real> TJ_ElementaryResidual(1, nquads, dim_TJ_ElementaryResidual);
    TJ_ElementaryResidual.resize(1, nquads, dim_TJ_ElementaryResidual);
    real* tjer = TJ_ElementaryResidual.local(elem_t(0), quad_t(0));
    this->ElementaryInterfaceConstitutive(e, dt, true /*update*/, false /*timeUpdate*/,
                                          TJ.local(elem_t(0), quad_t(0)), tjer,
                                          interface_tangent.data());

    // DG flux (size of container is _dof_node)
    real* dg_traction;
    // displacement jump (size of container is _dof_node)
    real* jumpU;
    // traction compute with cohesive law (size of container is _dof_node)
    const real* cohesive_traction;
    // coefficients to activate the traction-separation-law (TSL) (size of container is 2)
    const real* coefficients;
    // vector containing the normal (size of container is _dof_node)
    const real* normal;
    // vector containing the contracted stabilization coefficient
    // (size of container is _dof_node * _dof_node)
    const real* averg_hs_C_q;

    // integrate over quadrature points
    for (quad_t q(0); q < nquads; ++q) {
        tjer = TJ_ElementaryResidual.local(elem_t(0), q);

        // extract P_AVG_new (DG flux)
        // from tjer to tjer + _dof_node
        dg_traction = tjer;

        // extract jumpU (displacement jump)
        // from tjer + _dof_node to tjer + 2 * _dof_node
        jumpU = tjer + _dof_node;

        // extract cohesive traction
        // from tjer+2*_dof_node+2 to tjer+3*_dof_node+2
        cohesive_traction = tjer + 2 * _dof_node + 2;

        // extract coefficients to activate the TSL and the contact under compression
        // from tjer+3*_dof_node+2 to tjer+3*_dof_node+2+2
        coefficients = tjer + 3 * _dof_node + 2;

        // extract the normal in the deformed configuration
        // from TJ_ElementaryResidual+3*_dof_node+2+2 to TJ_ElementaryResidual+4*_dof_node+2+2
        normal = tjer + 3 * _dof_node + 2 + 2;

        // extract the contracted stabilization coefficient
        // from tj+2*_dof_node+2+materialBuffer
        // to   tj+2*_dof_node+2+materialBuffer+_dof_node*_dof_node
        // averg_hs_C_q = &TJ.local(
        //   elem_t(0),
        //   q)[2 * _dof_node + 2 + _material.materialBufferSizeForComm(_element_set->dim())];

        averg_hs_C_q = tjer + 3 * _dof_node + 4 + _element_set->dim();

        if (coefficients[0] < 0.5) {
            _uncrackedIntegrand(e, q, ul, dg_traction, jumpU, interface_tangent.data(), averg_hs_C_q,
                                rl, Kl);
        }
        // recontact
        else {
            // regular case, no recontact
            if (coefficients[1] < 0.5) {
                _crackedIntegrand(e, q, cohesive_traction, interface_tangent.data(), rl, Kl);
            }
            else {
                // compute the projection of the displacement jump and the dg traction onto the
                // normal to the interface.
                real u_n = 0.0;
                real t_dg_n = 0.0;
                for (size_t i = 0; i < _dof_node; ++i) {
                    u_n += jumpU[i] * normal[i];
                    t_dg_n += dg_traction[i] * normal[i];
                }
                for (size_t i = 0; i < _dof_node; ++i) {
                    jumpU[i] = u_n * normal[i];
                    dg_traction[i] = t_dg_n * normal[i];
                }

                _uncrackedIntegrand(e, q, ul, dg_traction, jumpU, interface_tangent.data(),
                                    averg_hs_C_q, rl, Kl);
            }
        }

// update the stresses and the internal variables
// MH: I think there should not be any update here because you don't know whether you have
//     converged
#if 0
        std::copy(P_L_new.begin(),P_L_new.end(),_stresses_L->local(e,q));
        std::copy(P_R_new.begin(),P_R_new.end(),_stresses_R->local(e,q));
        std::copy(internal_L_new.begin(),internal_L_new.end(),_internals_L->local(e,q));
        std::copy(internal_R_new.begin(),internal_R_new.end(),_internals_R->local(e,q));
#endif
    }

#else
    size_t const nen = _element_set->nodes_element();
    int const residual_dim = nen * _element_set->dim();
    size_t const nquads = _element_set->nquad();
    size_t const buffSize = this->bufferSizeForComm();

    // allocate memory for the local displacement (previous and current)
    // the element residual and stiffness
   std::vector<real> ulpert;
    ulpert.resize(residual_dim);
   std::vector<real> respertp;
    respertp.resize(residual_dim);
   std::vector<real> respertm;
    respertm.resize(residual_dim);
    // store P_AVG, displacement jump in only for the current element
   ElementQuadratureField<real> Pavg_JumpU;  // nelements, nquads, buffSize);
    Pavg_JumpU.resize(1, nquads, buffSize);

    // reset residual and stiffness
    std::fill(rl.begin(), rl.end(), 0.0);
    std::fill(Kl.begin(), Kl.end(), 0.0);

    bool update = false;

    // bundle of (i)   DG fluxes (interface traction) per quadrature points
    // --> dimension = _dof_node
    //           (ii)  displacement jumps per quadrature points --> dimension = _dof_node
    //           (iii) traction-separation-law (TSL: interface traction from cohesive law) per
    //           quadrature points    --> dimension = _dof_node
    //           (iv)  coefficient to activate the traction-separation-law --> dimension = 1
    //           (v)   coefficient to reactivate the contact under compression --> dimension = 1
    size_t dim_TJ_ElementaryResidual =
      ComputeDimensionForTJElementaryResidual();  // summation of dimensions of (i), (ii), (iii),
                                                  // (iv), and (v)
   ElementQuadratureField<real> TJ_ElementaryResidual(1, nquads, dim_TJ_ElementaryResidual);
    TJ_ElementaryResidual.resize(1, nquads, dim_TJ_ElementaryResidual);
    real* tjer = TJ_ElementaryResidual.local(elem_t(0), quad_t(0));

    // Compute the residual
    this->ElementaryConstitutive(e, dt, false, ul, u0l, Pavg_JumpU.local(elem_t(0), quad_t(0)));
    this->ElementaryInterfaceConstitutive(e, dt, update, false /*timeUpdate*/,
                                          Pavg_JumpU.local(elem_t(0), quad_t(0)), tjer);
    this->ElementaryResidual(e, ul, tjer, rl);

    // Compute matrix by perturbation
    std::copy(ul.begin(), ul.end(), ulpert.begin());
    std::fill(Kl.begin(), Kl.end(), 0.);
    real eps_pert = 1.e-8;
    real halfinveps = 0.5 / eps_pert;
    for (int j = 0; j < residual_dim; ++j)  // the perturbation also applied on the shell dofs
    {
        std::fill(respertm.begin(), respertm.end(), 0.);
        ulpert[j] -= eps_pert;
        this->ElementaryConstitutive(e, dt, false, ulpert, u0l,
                                     Pavg_JumpU.local(elem_t(0), quad_t(0)));
        this->ElementaryInterfaceConstitutive(e, dt, update, false /*timeUpdate*/,
                                              Pavg_JumpU.local(elem_t(0), quad_t(0)), tjer);
        this->ElementaryResidual(e, ulpert, tjer, respertm);
        std::fill(respertp.begin(), respertp.end(), 0.);
        ulpert[j] += eps_pert + eps_pert;
        this->ElementaryConstitutive(e, dt, false, ulpert, u0l,
                                     Pavg_JumpU.local(elem_t(0), quad_t(0)));
        this->ElementaryInterfaceConstitutive(e, dt, update, false /*timeUpdate*/,
                                              Pavg_JumpU.local(elem_t(0), quad_t(0)), tjer);
        this->ElementaryResidual(e, ulpert, tjer, respertp);
        ulpert[j] -= eps_pert;
        for (int i = 0; i < residual_dim; ++i) {
            Kl[i + j * residual_dim] = (respertm[i] - respertp[i]) * halfinveps;
        }
    }

#endif  // 0
#if 0   // matrix print
    for(int i=0; i<residual_dim;++i)
        for(int j=0;j<residual_dim;++j)
            std::cout << "K[" << i <<","  << j <<"]=" << Kl[i+j*residual_dim] << std::endl;
#endif  // 0
    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::ElementaryPrepareUpdate(elem_t e,
                                                               real dt,
                                                               std::vector<real> const& ul,
                                                               std::vector<real> const& u0l,
                                                               ElementQuadratureField<real>* TJf)
{
    // strain dimension
    int const strain_dim = _strains_L->dim();
    // dimension of left and right internal variables
    size_t const internal_L_dim = _material_L.nInt();
    // spatial dimension
    size_t spatial_dim = _element_set->dim();

    // memory allocation
   std::vector<real> P_L_new;
    P_L_new.resize(strain_dim);
   std::vector<real> f_L_new;
    f_L_new.resize(strain_dim);
    // memory allocation
   std::vector<real> F_L_new;
    // GB: before nen in place of _dof_node
    F_L_new.resize(spatial_dim * _dof_node);
   std::vector<real> internal_L_new;
    internal_L_new.resize(internal_L_dim);

   std::vector<real> interfaceMaterial_bufferForComm;
    interfaceMaterial_bufferForComm.resize(_material.materialBufferSizeForComm(spatial_dim));

   std::vector<real> xl;
    xl.resize(_element_set->nodes_element() * _element_set->dim());
    //    localize the coordintes
    _element_set->Localize(_coordinates, e, xl);

    //    characteristic lengths of left element
    // (if I'm here, the side of the element set is always left)
    real inRadius_left = _element_set->InRadiusElement(&xl[0]);

    real* TJ = _TJ->local(elem_t(e), quad_t(0));

    // ... for the jump in displacement
   std::vector<real> jumpU;
    jumpU.resize(_dof_node);
    // ... for the left traction
   std::vector<real> left_traction;
    left_traction.resize(_dof_node);

    // Now, do the right part
    // strain dimension
    int const strain_R_dim = _strains_R->dim();
    // dimension of left and right internal variables
    size_t const internal_R_dim = _material_R.nInt();

   std::vector<real> P_R_new;
    P_R_new.resize(strain_R_dim);
   std::vector<real> f_R_new;
    f_R_new.resize(strain_dim);

    std::vector<real> concentration_L(_dof_node);
    std::vector<real> concentration_L0(_dof_node);
    std::vector<real> concentration_R(_dof_node);
    std::vector<real> concentration_R0(_dof_node);
    // memory allocation
   std::vector<real> F_R_new;
    F_R_new.resize(spatial_dim * _dof_node);
   std::vector<real> internal_R_new;
    internal_R_new.resize(internal_R_dim);

    // integrate over quadrature points
    for (quad_t q(0); q < _element_set->nquad(); ++q) {
        // constitutive update of the left element
	    for (size_t i = 0; i < _CField_L->dim(); ++i) {
            _CField_L->local(e, q)[i] = 0.0;
            _C_L[i] = 0.0;
        }
        for (size_t i = 0; i < _CField_R->dim(); ++i) {
            _CField_R->local(e, q)[i] = 0.0;
            _C_R[i] = 0.0;
        }
        for (size_t i = 0; i < _dPdUField_R->dim(); ++i) {
            _dPdUField_R->local(e, q)[i] = 0.0;
            _dPdU_R[i] = 0.0;
        }
        for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
            _dPdUField_L->local(e, q)[i] = 0.0;
            _dPdU_L[i] = 0.0;
        }
	    for (size_t i = 0; i < _dFField_L->dim(); ++i) {
            _dFField_L->local(e, q)[i] = 0.0;
	        _dF_L[i] =0.0;
        }
        for (size_t i = 0; i < _dFField_R->dim(); ++i) {
            _dFField_R->local(e, q)[i] = 0.0;
            _dF_R[i] = 0.0;
        }
        LeftConstitutiveUpdate(e, q, ul, u0l, concentration_L0, concentration_L, dt, P_L_new, f_L_new, F_L_new, internal_L_new, _C_L.data(), _dPdU_L.data(), _dF_L.data());
        // constitutive update of the right element
        RightConstitutiveUpdate(e, q, ul, u0l, concentration_R0, concentration_R, dt, P_R_new, f_R_new, F_R_new, internal_R_new, _C_R.data(), _dPdU_R.data(), _dF_R.data());
        for (size_t i = 0; i < _CField_L->dim(); ++i) {
            _CField_L->local(e, q)[i] = _C_L[i];
        }
        for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
            _dPdUField_L->local(e, q)[i] = _dPdU_L[i] + _dF_L[i];
        }
        for (size_t i = 0; i < _CField_R->dim(); ++i) {
            _CField_R->local(e, q)[i] = _C_R[i];
        }
        for (size_t i = 0; i < _dPdUField_R->dim(); ++i) {
            _dPdUField_R->local(e, q)[i] = _dPdU_R[i] + _dF_R[i];
        }
        for (size_t i = 0; i < _dFField_L->dim(); ++i) {
            _dFField_L->local(e, q)[i] = _dF_L[i];
        }
        for (size_t i = 0; i < _dFField_R->dim(); ++i) {
            _dFField_R->local(e, q)[i] = _dF_R[i];
        }
        // update the stresses and the internal variables
        std::copy(P_L_new.begin(), P_L_new.end(), _stresses_L->local(e, q));
        std::copy(F_L_new.begin(), F_L_new.end(), _strains_L->local(e, q));
        std::copy(internal_L_new.begin(), internal_L_new.end(), _internals_L->local(e, q));

        // update the stresses and the internal variables
        std::copy(P_R_new.begin(), P_R_new.end(), _stresses_R->local(e, q));
        std::copy(F_R_new.begin(), F_R_new.end(), _strains_R->local(e, q));
        std::copy(internal_R_new.begin(), internal_R_new.end(), _internals_R->local(e, q));

        // reference to the left normal
        const real* normalL = _element_set->normalL(e, q);

        InterfaceMaterialFailureCriterionEvaluation(e, q, P_L_new, F_L_new, P_R_new, F_R_new,
                                                    normalL, interfaceMaterial_bufferForComm);
        // jacobians (with quadrature weights)
        real jac = _element_set->jac(e, q);

        // compute the displacement jump at the current quad point
        DisplacementJump(e, q, jac, ul, jumpU);

        // AverageTraction(P_L_new,P_R_new,jac,normalL,left_traction);
        std::fill(left_traction.begin(), left_traction.end(), 0.);

        // project the average PK-I onto the normal to get the left traction
        for (size_t i = 0; i < _dof_node; ++i) {
            for (dim_t j(0); j < spatial_dim; ++j) {
                size_t ij = spatial_dim * i + j;
                left_traction[i] += (0.5 * jac * (P_L_new[ij] + f_L_new[ij])) * normalL[j];
            }
        }

        // copy to global array
        // left traction
        std::copy(left_traction.begin(), left_traction.end(), TJ);
        // displacement jump
        std::copy(jumpU.begin(), jumpU.end(), TJ + _dof_node);
        // h_left
        TJ[2 * _dof_node] = inRadius_left;
        // h_right
        TJ[2 * _dof_node + 1] = 0.0;  // inRadius_right;
        // buffer from interface material
        std::copy(interfaceMaterial_bufferForComm.begin(), interfaceMaterial_bufferForComm.end(),
                  TJ + 2 * _dof_node + 2);
        TJ += bufferSizeForComm();
    }

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::ElementaryComputeUpdate(elem_t e,
                                                               ElementQuadratureField<real>* TJ,
                                                               real dt)
{
    int dim_TJ_ElementaryResidual = ComputeDimensionForTJElementaryResidual();
   ElementQuadratureField<real> TJ_ElementaryResidualField;
    TJ_ElementaryResidualField.resize(1, _element_set->nquad(), dim_TJ_ElementaryResidual);
    real* TJ_ElementaryResidual = TJ_ElementaryResidualField.local(elem_t(0), quad_t(0));

    // Step to compute the DG fluxes or TSL for the interface element
    ElementaryInterfaceConstitutive(e, dt, true /*update*/, true /*timeUpdate*/,
                                    _TJ->local(e, quad_t(0)), TJ_ElementaryResidual);

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::ElementaryComputeUpdateSubIteration(
  elem_t e, ElementQuadratureField<real>* TJ, real dt)
{
    int dim_TJ_ElementaryResidual = ComputeDimensionForTJElementaryResidual();
   ElementQuadratureField<real> TJ_ElementaryResidualField;
    TJ_ElementaryResidualField.resize(1, _element_set->nquad(), dim_TJ_ElementaryResidual);
    real* TJ_ElementaryResidual = TJ_ElementaryResidualField.local(elem_t(0), quad_t(0));

    // Step to compute the DG fluxes or TSL for the interface element
    ElementaryInterfaceConstitutive(e, dt, true /*update*/, false /*timeUpdate*/,
                                    _TJ->local(e, quad_t(0)), TJ_ElementaryResidual);

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::ElementaryComputeUpdate(elem_t e,
                                                               ElementQuadratureField<real>* TJ,
                                                               real dt,
                                                               std::string const& name)
{
    int dim_TJ_ElementaryResidual = ComputeDimensionForTJElementaryResidual();
   ElementQuadratureField<real> TJ_ElementaryResidualField;
    TJ_ElementaryResidualField.resize(1, _element_set->nquad(), dim_TJ_ElementaryResidual);
    real* TJ_ElementaryResidual = TJ_ElementaryResidualField.local(elem_t(0), quad_t(0));

    // Step to compute the DG fluxes or TSL for the interface element
    ElementaryInterfaceConstitutive(e, dt, true, true /*timeUpdate*/, name,
                                    _TJ->local(e, quad_t(0)), TJ_ElementaryResidual);

    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::RightConstitutiveUpdate(elem_t e,
                                                               quad_t q,
                                                               std::vector<real> const& ul,
                                                               std::vector<real> const& u0l,
                                                               std::vector<real>& concentration0,
                                                               std::vector<real>& concentration,
                                                               real dt,
                                                               std::vector<real>& P_R_new,
                                                               std::vector<real>& f_R_new,
                                                               std::vector<real>& F_R_new,
                                                               std::vector<real>& internal_R_new,
                                                               real* C_R,
                                                               real* dPdU_R,
                                                               real* df_R)
{
    // spatial dimension
    size_t const spatial_dim = _element_set->dim();
    // strain dimension
    int const strain_dim = _strains_R->dim();
    // dimension of left internal variables
    size_t const internal_R_dim = _material_R.nInt();
    // To know if the tangent has to be computed
    bool const compute_tangent = (C_R ? true : false);

    size_t const nen = _element_set->connectivities_element();
    // Compute the left gradient
    static_cast<ElementSetInterfaceTwoSided const*>(_element_set)
      ->Gradient_R(e, q, ul.data(), _dof_node, F_R_new.data());
    // std::vector<real> concentration(_dof_node);
    // std::vector<real> concentration0(_dof_node);
    std::fill(concentration.begin(), concentration.end(), 0.);
    std::fill(concentration0.begin(), concentration0.end(), 0.);

    

    for (lnode_t b = static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeStart(); b != static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeDone(); ++b) {
        for (int c = 0; c < _dof_node; c++) {
            concentration[c] += _element_set->shape(e, q, b) * ul[c + b * _dof_node];
            concentration0[c] += _element_set->shape(e, q, b) * u0l[c + b * _dof_node];
        }
    }

    // pointer to local value of stress at previous time step
    real* P_R = _stresses_R->local(e, q);
    // copy to local Right stresses
    std::copy(P_R, P_R + strain_dim, P_R_new.begin());

    // local value of internal variables at previous time step
    real* internal_R = _internals_R->local(e, q);
    // copy to local Right internal variables
    std::copy(internal_R, internal_R + internal_R_dim, internal_R_new.begin());

    // local value of old deformation gradient at previous time step
    real* F0_R = _strains_R->local(e, q);

    // Constitutive update
    _material_R.Constitutive(&concentration0[0], &concentration[0], F0_R, F_R_new.data(), P_R_new.data(),
                             internal_R_new.data(), C_R, dPdU_R, dt, _dof_node, spatial_dim,
                             compute_tangent, false);
    //_material_R.Source(&concentration0[0], &concentration[0], internal_R_new.data(), &dt, &f_R_new[0], &df_R[0], _dof_node);
    //this line updates internal variable processes needed to compute source terms
    //source terms themselves are not relevant for the trace quadrature points
    //line needed for consistency with bulk models 
    std::vector<real> sources(_dof_node);
    std::vector<real> dsources(_dof_node * _dof_node);
    _material_R.Source(&concentration0[0], &concentration[0], internal_R_new.data(), &dt, &sources[0], &dsources[0], _dof_node);

    _material_R.ConvectiveFlux(&concentration0[0], &concentration[0], &internal_R_new[0],
                            &dt, f_R_new.data(), &df_R[0], _dof_node, spatial_dim);
    // all done
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::AverageTraction(std::vector<real> const& P_L_new,
                                                       std::vector<real> const& P_R_new,
                                                       const real jac,
                                                       const real* normalL,
                                                       std::vector<real>& left_traction)
{
    // spatial dimension
    size_t const spatial_dim = _element_set->dim();

    std::fill(left_traction.begin(), left_traction.end(), 0.);

    // project the average PK-I onto the normal to get the left traction
    for (size_t i = 0; i < _dof_node; ++i) {
        for (dim_t j(0); j < spatial_dim; ++j) {
            size_t ij = spatial_dim * i + j;
#if 0
            left_traction[i] += (0.5 * jac * ( P_L_new[ij] + P_R_new[ij] )) * normalL[j];
#else
            left_traction[i] +=
              0.5 * jac * P_L_new[ij] * normalL[j] + 0.5 * jac * P_R_new[ij] * normalL[j];
#endif
        }
    }

    // all done
    return;
}


void summit::ReactionDiffusionInterfaceRegionFullButterfly::InterfaceMaterialFailureCriterionEvaluation(
  elem_t e,
  quad_t q,
  std::vector<real>& P_L_new,
  std::vector<real>& F_L_new,
  std::vector<real>& P_R_new,
  std::vector<real>& F_R_new,
  const real* normalL,
  std::vector<real>& interfaceMaterial_bufferForComm)
{
    // spatial dimension
    size_t const spatial_dim = _element_set->dim();

    // local value (at the quadrature point) of internal variables at previous time step
    const real* internal = _internals->local(e, q);

    // Evaluate the failure criterion for the specific Gauss point
    // Notice that:
    //   - Internal variables are not modified
    //   - interfaceMaterial_bufferForComm stores the information to decide if the failure
    //     criterion is satisfied at the quadrature point
    //     This information will be used by
    //     summit::commonReactionDiffusionInterfaceRegionFullButterfly::ComputeResidual(...)
    _material.EvaluateFailureCriterion(
      P_L_new.data(), F_L_new.data(), P_R_new.data(), F_R_new.data(), internal, normalL,
      summit::INTER, spatial_dim /* should be _dof_node*/, interfaceMaterial_bufferForComm.data());

    // all done
    return;
}

summit::ElementQuadratureField<real>* summit::ReactionDiffusionInterfaceRegionFullButterfly::internalVariables()
{
    return _internals;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::_uncrackedIntegrand(elem_t e,
                                                           quad_t q,
                                                           const std::vector<real>& ul,
                                                           const real* left_traction,
                                                           const real* jumpU,
                                                           real* interface_tangent,
                                                           const real* averg_hs_C,
                                                           std::vector<real>& rl,
                                                           std::vector<real>& Kl)
{
    std::vector<real> my_damage(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q), my_damage.data());
    std::vector<int> my_types(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceTypes(_internals->local(e, q), my_types.data());
    std::vector<real> my_values(_dof_node);
    (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceValues(_internals->local(e, q), my_values.data());

    // size of the local residual
    int const residual_dim = _element_set->nodes_element() * _dof_node;
    real ConFactor = 1.0;// - (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q));
    real ComFactor = 1.0;// - (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q));
    real StabFactor = 1.0;// - (dynamic_cast<const UpwindInterfaceDG&>(_material)).getInterfaceDamage(_internals->local(e, q));

    std::vector<real> rightConcentration(_dof_node);
    std::fill(rightConcentration.begin(), rightConcentration.end(), 0.);
    for (lnode_t b = static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeStart(); b != static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeDone(); ++b) {
        for (int c = 0; c < _dof_node; c++) {
            rightConcentration[c] += _element_set->shape(e, q, b) * ul[c + b * _dof_node];
        }
    }
    std::vector<real> leftConcentration(_dof_node);
    std::fill(leftConcentration.begin(), leftConcentration.end(), 0.);
    for (lnode_t b = _element_set->leftNodeStart(); b != _element_set->leftNodeDone(); ++b) {
        for (int c = 0; c < _dof_node; c++) {
            leftConcentration[c] += _element_set->shape(e, q, b) * ul[c + b * _dof_node];
        }
    }
    // loop over the nodes of the left element -> test function
    for (lnode_t c = _element_set->leftNodeStart(); c != _element_set->leftNodeDone(); ++c) {
        const real test_u = _element_set->shape(e, q, c);
        const real* test_du = _element_set->dShape(e, q, c);
        // loop over its degrees of freedom
        for (size_t i = 0; i < _dof_node; ++i) {
            // compute the index of the current dof in the local displacement vector
            size_t ci = _dof_node * c + i;

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling r^L
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            //
            // Compute the "left" interface residual
            //
            // Here, we compute MINUS the internal forces because the assemble function only
            // adds to the residual: residual = f^ext - f^int
            // update the consistency term
            rl[ci] -= ConFactor * (1.0 - my_damage[i]) * TractionResidualTerm<summit::LEFT>(e, q, test_u, i, left_traction);
            // update the compatibility term
#if IMR_COMPATIBILITY
            rl[ci] -= ComFactor * (1.0 - my_damage[i]) * 
              CompatibilityResidualTerm(e, q, test_du, test_u, i, jumpU, (_bulkC_L.second)->local(e, q), _kappa_ref_L.data());
#endif  // IMR_COMPATIBILITY
        // update the penalty term
            if (_usingFullStabilizationTerm) {
                rl[ci] -= StabFactor * (1.0 - my_damage[i]) *
                  StabilizationResidualTermFULL<summit::LEFT>(e, q, test_u, i, jumpU, averg_hs_C);
            }
            else {
                rl[ci] -= StabFactor * (1.0 - my_damage[i]) *
                  StabilizationResidualTerm<summit::LEFT>(e, q, test_u, i, jumpU, averg_hs_C[i]);
            }
            if(my_types[i]==0){
                rl[ci] -= my_damage[i] *
                  StabilizationResidualTerm<summit::LEFT>(e, q, test_u, i, jumpU, my_values[i]);
            }else{
                real myJump = -(leftConcentration[i] - my_values[i]);
                myJump *= _element_set->jac(e, q);
                // std::cout << "myJump: " << myJump <<std::endl;
                rl[ci] -= my_damage[i] *
                          StabilizationResidualTerm<summit::LEFT>(e, q, test_u, 0, &myJump, averg_hs_C[i]);
            }

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^LL
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            //
            // Compute the left/left interface stiffness
            //
            // loop over the nodes of the left element -> displacement
            for (lnode_t a = _element_set->leftNodeStart(); a != _element_set->leftNodeDone();
                 ++a) {
                const real disp_u = _element_set->shape(e, q, a);
                const real* disp_du = _element_set->dShape(e, q, a);
                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t ak = _dof_node * a + k;
                    // compute the index of the current entry in the local stiffness array
                    size_t ciak = ci * residual_dim + ak;
                    // update the consistency term
                    Kl[ciak] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentMatrixTerm<summit::LEFT>(
                      e, q, test_u, i, disp_du, k, (_bulkC_L.second)->local(e, q));
                    Kl[ciak] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentSecondMatrixTerm<summit::LEFT>(
                      e, q, test_u, i, disp_u, k, (_bulkdPdU_L.second)->local(e, q));
                    // update the compatibility term
#if IMR_COMPATIBILITY
                    Kl[ciak] += ComFactor * (1.0 - my_damage[i]) * CompatibilityTangentMatrixTerm<summit::LEFT>(
                      e, q, test_du, i, disp_u, k, (_bulkC_L.second)->local(e, q), _kappa_ref_L.data());
#endif  // IMR_COMPATIBILITY

                    // update the penalty term
                    if (_usingFullStabilizationTerm) {
                        Kl[ciak] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTermFULL<summit::LEFT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C);
                    }
                    else {
                        Kl[ciak] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTerm<summit::LEFT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C[i]);
                    }
                    if(my_types[i]==0){
                        Kl[ciak] += my_damage[i] * StabilizationTangentMatrixTerm<summit::LEFT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, my_values[i]);
                    }else{
                        Kl[ciak] += my_damage[i] * StabilizationTangentMatrixTerm<summit::LEFT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C[i]);
                    }
                }
            }

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^LR
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // loop over the nodes of the right element -> displacement
            for (lnode_t b =
                   static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeStart();
                 b !=
                 static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeDone();
                 ++b) {
                const real disp_u = _element_set->shape(e, q, b);
                const real* disp_du = _element_set->dShape(e, q, b);
                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t bk = _dof_node * b + k;
                    // compute the index of the current entry in the local stiffness array
                    size_t cibk = ci * residual_dim + bk;
                    // update the consistency term

                    Kl[cibk] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentMatrixTerm<summit::LEFT>(
                      e, q, test_u, i, disp_du, k, (_bulkC_R.second)->local(e, q));
                    Kl[cibk] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentSecondMatrixTerm<summit::LEFT>(
                      e, q, test_u, i, disp_u, k, (_bulkdPdU_R.second)->local(e, q));
                    // update the compatibility term
#if IMR_COMPATIBILITY

                    Kl[cibk] += ComFactor * (1.0 - my_damage[i]) * CompatibilityTangentMatrixTerm<summit::RIGHT>(
                      e, q, test_du, i, disp_u, k, (_bulkC_L.second)->local(e, q), _kappa_ref_L.data());
#endif  // IMR_COMPATIBILITY
        // update the penalty term
                    if (_usingFullStabilizationTerm) {
                        Kl[cibk] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTermFULL<summit::LEFT, summit::RIGHT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C);
                    }
                    else {
                        Kl[cibk] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTerm<summit::LEFT, summit::RIGHT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C[i]);
                    }
                    if(my_types[i]==0){
                        Kl[cibk] += my_damage[i] * StabilizationTangentMatrixTerm<summit::LEFT, summit::RIGHT>(
                          e, q, test_u, i, disp_u, k, my_values[i]);
                    }
                    //weak dirichlet does not depend on the other side of the element!
                }
            }
        }
    }

    // loop over the nodes of the right element -> test function
    for (lnode_t d =
           static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeStart();
         d != static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeDone(); ++d) {
        const real test_u = _element_set->shape(e, q, d);
        const real* test_du = _element_set->dShape(e, q, d);

        // loop over its degrees of freedom
        for (size_t i = 0; i < _dof_node; ++i) {
            // compute the index of the current dof in the local displacement vector
            size_t di = _dof_node * d + i;

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling r^R
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            //
            // Compute the "right" interface residual
            //
            // update the consistency term
            rl[di] -= ConFactor * (1.0 - my_damage[i]) * TractionResidualTerm<summit::RIGHT>(e, q, test_u, i, left_traction);
            // Here, we compute MINUS the internal forces because the assemble function
            // only adds to the residual: residual = f^ext - f^int
            // update the compatibility term
#if IMR_COMPATIBILITY
            rl[di] -= ComFactor * (1.0 - my_damage[i]) * 
              CompatibilityResidualTerm(e, q, test_du, test_u, i, jumpU, (_bulkC_R.second)->local(e, q), _kappa_ref_R.data());
#endif  // IMR_COMPATIBILITY
        // update the penalty term
            if (_usingFullStabilizationTerm) {
                rl[di] -= StabFactor * (1.0 - my_damage[i]) * 
                  StabilizationResidualTermFULL<summit::RIGHT>(e, q, test_u, i, jumpU, averg_hs_C);
            }
            else {
                rl[di] -= StabFactor * (1.0 - my_damage[i]) * 
                  StabilizationResidualTerm<summit::RIGHT>(e, q, test_u, i, jumpU, averg_hs_C[i]);
            }
            if(my_types[i]==0){
                rl[di] -= my_damage[i] *
                  StabilizationResidualTerm<summit::RIGHT>(e, q, test_u, i, jumpU, my_values[i]);
            }else{
                real myJump = (rightConcentration[i] - my_values[i]);
                myJump *= _element_set->jac(e, q);
                // std::cout << "myJump: " << myJump <<std::endl;
                rl[di] -= my_damage[i] *
                          StabilizationResidualTerm<summit::RIGHT>(e, q, test_u, 0, &myJump, averg_hs_C[i]);
            }


            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^RL
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            //
            // Compute the right/left interface stiffness
            //
            // loop over the nodes of the left element -> displacement
            for (lnode_t a = _element_set->leftNodeStart(); a != _element_set->leftNodeDone();
                 ++a) {
                const real disp_u = _element_set->shape(e, q, a);
                const real* disp_du = _element_set->dShape(e, q, a);
                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t ak = _dof_node * a + k;
                    // compute the index of the current entry in the local stiffness
                    // array
                    size_t diak = di * residual_dim + ak;


                    // update the consistency term
                    Kl[diak] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentMatrixTerm<summit::RIGHT>(
                      e, q, test_u, i, disp_du, k, (_bulkC_L.second)->local(e, q));
                    Kl[diak] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentSecondMatrixTerm<summit::RIGHT>(
                      e, q, test_u, i, disp_u, k, (_bulkdPdU_L.second)->local(e, q));

                    // update the compatibility term
#if IMR_COMPATIBILITY
                    Kl[diak] += ComFactor * (1.0 - my_damage[i]) * CompatibilityTangentMatrixTerm<summit::LEFT>(
                      e, q, test_du, i, disp_u, k, (_bulkC_R.second)->local(e, q), _kappa_ref_R.data());
#endif  // IMR_COMPATIBILITY

                    // update the penalty term
                    if (_usingFullStabilizationTerm) {
                        Kl[diak] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTermFULL<summit::RIGHT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C);
                    }
                    else {
                        Kl[diak] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTerm<summit::RIGHT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C[i]);
                    }
                    if(my_types[i]==0){
                        Kl[diak] += my_damage[i] * StabilizationTangentMatrixTerm<summit::RIGHT, summit::LEFT>(
                          e, q, test_u, i, disp_u, k, my_values[i]);
                    }
                    //weak dirichlet does not depend on the other side of the element!
                }
            }

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^RR
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // loop over the nodes of the right element -> displacement
            for (lnode_t b =
                   static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeStart();
                 b !=
                 static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeDone();
                 ++b) {
                const real disp_u = _element_set->shape(e, q, b);
                const real* disp_du = _element_set->dShape(e, q, b);
                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t bk = _dof_node * b + k;
                    // compute the index of the current entry in the local stiffness
                    // array
                    size_t dibk = di * residual_dim + bk;
                    // update the consistency term

                    Kl[dibk] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentMatrixTerm<summit::RIGHT>(
                      e, q, test_u, i, disp_du, k, (_bulkC_R.second)->local(e, q));
                    Kl[dibk] += ConFactor * (1.0 - my_damage[i]) * ConsistencyTangentSecondMatrixTerm<summit::RIGHT>(
                      e, q, test_u, i, disp_u, k, (_bulkdPdU_R.second)->local(e, q));                    

                    // update the compatibility term
#if IMR_COMPATIBILITY
                    Kl[dibk] += ComFactor * (1.0 - my_damage[i]) * CompatibilityTangentMatrixTerm<summit::RIGHT>(
                      e, q, test_du, i, disp_u, k, (_bulkC_R.second)->local(e, q), _kappa_ref_R.data());
#endif  // IMR_COMPATIBILITY
        // update the penalty term
                    if (_usingFullStabilizationTerm) {
                        Kl[dibk] += StabFactor * (1.0 - my_damage[i]) * 
                          StabilizationTangentMatrixTermFULL<summit::RIGHT, summit::RIGHT>(
                            e, q, test_u, i, disp_u, k, averg_hs_C);
                    }
                    else {
                        Kl[dibk] += StabFactor * (1.0 - my_damage[i]) * StabilizationTangentMatrixTerm<summit::RIGHT, summit::RIGHT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C[i]);
                    }
                    if(my_types[i]==0){
                        Kl[dibk] += my_damage[i] * StabilizationTangentMatrixTerm<summit::RIGHT, summit::RIGHT>(
                          e, q, test_u, i, disp_u, k, my_values[i]);
                    }else{
                        Kl[dibk] += my_damage[i] * StabilizationTangentMatrixTerm<summit::RIGHT, summit::RIGHT>(
                          e, q, test_u, i, disp_u, k, averg_hs_C[i]);
                    }
                }
            }
        }
    }
    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::_crackedIntegrand(elem_t e,
                                                         quad_t q,
                                                         const real* cohesive_traction,
                                                         real* interface_tangent,
                                                         std::vector<real>& rl,
                                                         std::vector<real>& Kl)
{
    // size of the local residual
    int const residual_dim = _element_set->nodes_element() * _dof_node;

    real* dFdU = interface_tangent + _dof_node * _dof_node * q;

    // loop over the nodes of the left element -> test function
    for (lnode_t c = _element_set->leftNodeStart(); c != _element_set->leftNodeDone(); ++c) {
        const real test_u = _element_set->shape(e, q, c);

        // loop over its degrees of freedom
        for (size_t i = 0; i < _dof_node; ++i) {
            // compute the index of the current dof in the local displacement vector
            size_t ci = _dof_node * c + i;

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling r^L
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            //
            // Compute the "left" interface residual
            //
            // Here, we compute MINUS the internal forces because the assemble function only
            // adds to the residual: residual = f^ext - f^int
            // update the consistency term
            rl[ci] -= TractionResidualTerm<summit::LEFT>(e, q, test_u, i, cohesive_traction);

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^LL
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // loop over the nodes of the left element -> test displacement
            // tangent matrix for fracture
            for (lnode_t a = _element_set->leftNodeStart(); a != _element_set->leftNodeDone();
                 ++a) {
                const real disp_u = _element_set->shape(e, q, a);

                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t ak = _dof_node * a + k;
                    // compute the index of the current entry in the local stiffness array
                    size_t ciak = ci * residual_dim + ak;

                    Kl[ciak] += CohesiveTangentMatrixTerm<summit::LEFT, summit::LEFT>(
                      e, q, test_u, i, disp_u, k, dFdU);
                }
            }

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^LR
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // loop over the nodes of the right element -> test displacement
            for (lnode_t a =
                   static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeStart();
                 a !=
                 static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeDone();
                 ++a) {
                const real disp_u = _element_set->shape(e, q, a);

                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t ak = _dof_node * a + k;
                    // compute the index of the current entry in the local stiffness array
                    size_t ciak = ci * residual_dim + ak;

                    Kl[ciak] += CohesiveTangentMatrixTerm<summit::LEFT, summit::RIGHT>(
                      e, q, test_u, i, disp_u, k, dFdU);
                }
            }
        }
    }

    // loop over the nodes of the right element -> test function
    for (lnode_t d =
           static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeStart();
         d != static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeDone(); ++d) {
        const real test_u = _element_set->shape(e, q, d);

        // loop over its degrees of freedom
        for (size_t i = 0; i < _dof_node; ++i) {
            // compute the index of the current dof in the local displacement vector
            size_t di = _dof_node * d + i;

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling r^R
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            //
            // Compute the "right" interface residual
            //
            // update the consistency term
            rl[di] -= TractionResidualTerm<summit::RIGHT>(e, q, test_u, i, cohesive_traction);

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^RL and r^R
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // loop over the nodes of the left element -> displacement
            // tangent matrix for fracture
            for (lnode_t a = _element_set->leftNodeStart(); a != _element_set->leftNodeDone();
                 ++a) {
                const real disp_u = _element_set->shape(e, q, a);

                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t ak = _dof_node * a + k;
                    // compute the index of the current entry in the local stiffness
                    // array
                    size_t diak = di * residual_dim + ak;

                    Kl[diak] += CohesiveTangentMatrixTerm<summit::RIGHT, summit::LEFT>(
                      e, q, test_u, i, disp_u, k, dFdU);
                    ;
                }
            }

            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // assembling k^RR
            //~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
            // loop over the nodes of the right element -> displacement
            for (lnode_t a =
                   static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeStart();
                 a !=
                 static_cast<ElementSetInterfaceTwoSided const*>(_element_set)->rightNodeDone();
                 ++a) {
                const real disp_u = _element_set->shape(e, q, a);

                // loop over its degrees of freedom
                for (size_t k = 0; k < _dof_node; ++k) {
                    // compute the index of the current dof in the local test function
                    // vector
                    size_t ak = _dof_node * a + k;
                    // compute the index of the current entry in the local stiffness array
                    size_t diak = di * residual_dim + ak;

                    Kl[diak] += CohesiveTangentMatrixTerm<summit::RIGHT, summit::RIGHT>(
                      e, q, test_u, i, disp_u, k, dFdU);
                }
            }
        }
    }

    return;
}

void summit::ReactionDiffusionInterfaceRegionFullButterfly::WriteForRestart(Checkpoint* checkpoint,
                                                       const char* name,
                                                       const char* tag) const
{
    // Call write of super class (this creates the group for this region)
    if (tag == nullptr) {
        ReactionDiffusionInterfaceRegion::WriteForRestart(checkpoint, name, "ReactionDiffusionInterfaceRegionFullButterfly");
    }
    else {
        ReactionDiffusionInterfaceRegion::WriteForRestart(checkpoint, name, tag);
    }

    // Write stresses_R
    _stresses_R->WriteForRestart(checkpoint, "stresses_R");
    _F_R->WriteForRestart(checkpoint, "f_R");
    // Write strains_R
    _strains_R->WriteForRestart(checkpoint, "strains_R");

    // Write internals_R
    _internals_R->WriteForRestart(checkpoint, "internals_R");

    // Write CField_R
    _CField_R->WriteForRestart(checkpoint, "CField_R");
    _dPdUField_R->WriteForRestart(checkpoint, "dPdUField_R");
    _dFField_R->WriteForRestart(checkpoint, "dfField_R");
    // Write avg_stab_coef_R
    _avg_stab_coef_R->WriteForRestart(checkpoint, "avg_stab_coef_R");

    // Write bulkC_R_second
    _bulkC_R.second->WriteForRestart(checkpoint, "bulkC_R_second");
    _bulkdPdU_R.second->WriteForRestart(checkpoint, "bulkdPdU_R_second");

    int matIndex_R = _material_R.GetMaterialIndex();
    checkpoint->write<int>("matIndex_R", matIndex_R);

    std::vector<int> dims(1);
    // Write C_R
    dims[0] = _C_R.size();
    checkpoint->WriteDataSet("C_R", dims, SUMMIT_REAL, _C_R.data());

    // Write bulkC_R_first
    int bulkC_R_first = _bulkC_R.first;
    dims[0] = 1;
    checkpoint->WriteDataSet("bulkC_R_first", dims, SUMMIT_INT, &bulkC_R_first);

    // Write bulkdPdU_R_first
    int bulkdPdU_R_first = _bulkdPdU_R.first;
    dims[0] = 1;
    checkpoint->WriteDataSet("bulkdPdU_R_first", dims, SUMMIT_INT, &bulkdPdU_R_first);

    // Write computeRightPart
    dims[0] = 1;
    checkpoint->WriteDataSet("computeRightPart", dims, SUMMIT_INT, &_computeRightPart);

    // tag with material label
    std::string label = _material_R.GetLabel();
    checkpoint->location()->tag("material_R", label.data());

    // all done
    return;
}

REGISTER(Region, ReactionDiffusionInterfaceRegionFullButterfly);
// end of file
