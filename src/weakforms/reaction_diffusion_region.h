// -*- C++ -*-
//
// summit development team
// massachusetts institute of technology
// (c) 2011-2014 all rights reserved
//

#ifndef SUMMIT_REACTION_DIFFUSION_REGION_H
#define SUMMIT_REACTION_DIFFUSION_REGION_H

#include "../elements/element_set_bulk.h"
#include "../mathlib/functor.h"

#include "region.h"

namespace summit {

/**
 * Forward declarations
 */
class ReactionDiffusionMaterial;
class Stiffness;

class ReactionDiffusionRegion : public Region {
  public:
    /**
     * Typedefs
     */
    typedef ElementSet::field_t field_t;

  public:
    /**
     * Constructor
     * @param[in] es set of bulk elements
     * @param[in] dof_node the number of degrees of freedom per node
     * @param[in] material material class
     * @param[in] coordinates the nodal coordinates
     * @param[in] gravityTerm the Gravity Term
     */
    ReactionDiffusionRegion(ElementSetBulk const* es,
                           int dof_node,
                           ReactionDiffusionMaterial const& material,
                           NodalField<real> const& coordinates);

    /**
     * Destructor
     */
    virtual ~ReactionDiffusionRegion();

  private:
    /**
     * Default constructor
     */
    ReactionDiffusionRegion();

    /**
     * Copy constructor
     * @param[in] a const reference to a ReactionDiffusionRegion
     */
    ReactionDiffusionRegion(ReactionDiffusionRegion const&);

    /**
     * Assignment operator
     * @param[in] a const reference to a ReactionDiffusionRegion
     * @return a reference to a ReactionDiffusionRegion
     */
    ReactionDiffusionRegion& operator=(Region const&);

  public:
    /**
     * Method to compute the residual of 1 element
     * @param[in] e the local index of the element
     * @param[in] concentrationl the primal variableof the element
     * @param[in] concentration0l the primal variableof the element at the previous step
     * @param[in] xl the current position of the nodes of the elements
     * @param[in] dt a time-step
     * @param[in] update whether to update the internal variables, stresses and strains
     * @param[out] rl a vector containing the local residual of the element
     */
    virtual void ElementaryResidual(elem_t e,
                                    std::vector<real> const& concentrationl,
                                    std::vector<real> const& concentration0l,
                                    std::vector<real> const& xl,
                                    real dt,
                                    bool update,
                                    std::vector<real>& rl);

    /**
     * Method to compute the residual over the region
     * @param[in] concentration, a nodal field of the primal variable
     * @param[in] concentration0, a nodal field of the primal variable at the previous time-step
     * @param[in] dt, a time-step
     * @param[in] update, whether to update the internal variables, stresses and strains
     * @param[in] commManager a CommunicationManager
     * @param[in,out] residual, a nodal field filled in within the method
     */
    virtual void Residual(NodalField<real> const& concentration,
                          NodalField<real> const& concentration0,
                          real dt,
                          bool update,
                          CommunicationManager const& commManager,
                          NodalField<real>& residual);

    /**
     * Method to interpolate and set the primal variable as internal variable at each quadrature
     * point
     * @param[in] T an input nodal field of the primal variable
     */
    virtual void InterpolateAndSetPrimalVariableAsInternalVariable(NodalField<real> const& T);

    /**
     * Method to compute the new stress needed to compute the residual over
     * the region.
     * @param[in] T a nodal field of the primal variable
     * @param[in] T0 a nodal field of the primal variable at the previous time-step
     * @param[in] dt a time-step
     * @param[in] update whether to update the quadrature fields
     * @param[out] P_new new streee needed to compute the residual
     */
    virtual void PrepareResidual(NodalField<real> const& T,
                                 NodalField<real> const& T0,
                                 real dt,
                                 bool update,
                                 ElementQuadratureField<real>* P_new);

    /**
     * Method to get internal variable element quadrature field and their position, size as well as
     * the number of quadrature points
     * @param[in] name string identifying the internal variable you want
     * @return a vector of tuple containing the position and size of the desired internal
     *         variable in the element quadrature field passed as the third argument of the tuple
     *         as a const pointer
     */
    virtual IVsheet_t getInternalVariableFields(std::string const& name) const;
    
    /**
     * Method to compute the residual of the region form the new stress.
     * @param[in] T The current primal variable
     * @param[in] P_new new stress needed to compute the residual. To know
     *            why it is non-const see comment in the base class.
     * @param[in] dt a time-step
     * @param[in] update whether to update the quadrature fields
     * @param[in,out] residual, a nodal field filled in within the method
     */
    virtual void ComputeResidual(const NodalField<real>& T,
                                 ElementQuadratureField<real>* P_new,
                                 NodalField<real>& residual,
                                 real dt = 1.0,
                                 bool update = false);

    /**
     * Method to compute the stiffness matrix and the residual over the region
     * @param[in] T, a nodal field of the primal variable
     * @param[in] T0, a nodal field of the primal variable at the previous time-step
     * @param[in] dt, a time-step
     * @param[in,out] stiffness, a matric filled in within the method
     * @param[in,out] residual, a nodal field filled in within the method
     */
    virtual void ComputeStiffness(NodalField<real> const& T,
                                  NodalField<real> const& T0,
                                  real dt,
                                  Stiffness& stiffness,
                                  NodalField<real>& residual);

    /**
     * Method to update the region
     * @param[in] T, a nodal field of the primal variable
     * @param[in] T0, a nodal field of the primal variable at the previous time-step
     * @param[in] dt, a time-step
     */
    virtual void Update(NodalField<real> const& T, NodalField<real> const& T0, real dt);

    /**
     * Method to compute the acceleration with a diagonal by block mass matrix (DG mass matrix)
     * @param[in] residual the global nodal field in which the residual is stored
     * @param[in] boundaryConditionType the global nodal field in which the type of boundary
     *            condition is stored
     * @param[out] acceleration the global nodal field in which the acceleration is stored
     * @param[in] commManager a CommunicationManager
     * @param[in] gammaDt Newmark's gamma parameter multiplied by the time step
     */
    void ComputeAccelerationWithBlockMassMatrix(NodalField<real> const& residual,
                                                NodalField<int> const& boundaryConditionType,
                                                NodalField<real>& acceleration,
                                                const CommunicationManager& commManager,
                                                const real gammaDt = 0.0);

    void ApplyMassMatrixInverse(NodalField<real> const& residual, NodalField<real>& acceleration);

    /**
     * Method to compute a stable time-step
     * @param[in] coordinates nodal coordinates
     * @param[in] T a nodal field of the primal variable
     * @return a stable time-step
     */
    virtual real StableTimeStep(NodalField<real> const& coordinates, NodalField<real> const& T);

    /**
     * Method to assemble the mass matrix
     * @param[in/out] mass the global nodal field in which the mass matrix is written.
     */
    virtual void AssembleMass(NodalField<real>& mass) const;

    /**
     * Method to interpolate from nodes to quadrature points
     * @param nodal_field, a NodalField of real
     * @param quad_field, an ElementQuadratureField of real
     */
    virtual void Interpolate(const NodalField<real>& nodal_field,
                             ElementQuadratureField<real>& quad_field) const;

    /**
     * Accessor to the element set
     */
    virtual ElementSet const* const element_set() const { return _element_set; }

    /**
     *
     */
    virtual bool GetNodalField(std::string const& name,
                               int numNode,
                               NodalField<real>& weights,
                               NodalField<real>& nodal_field) const;

    /**
     * Method to get the globalId of each element of the region
     * @param[in,out] globalId a vector whose component i is the globalId of element i
     */
    virtual void GetGlobalIdByElement(std::vector<int>& globalId) const;

    /**
     * Method to set all the material parameters element by element in a region
     * @param[in] sheet a PropertySheet_t containing the material parameters
     */
    virtual void SetMaterialParameters(PropertySheet_t const& sheet);

    /**
     * Method that clones (at least the memory allocation) the ElementQuadratureField that needs to
     * be computed when computing the residual. This ElementQuadratureField can be PK1 in bulk
     * continuum mechanics, the pair (tractions, jumps) in interface continuum mechanics and the
     * corresponding quantities in shell structural mechanics.
     */
    ElementQuadratureField<real>* CloneVariablesForResidual() const;

    /**
     * Method that returns a pointer to the ElementQuadratureField that needs to be computed
     * when computing the residual. This ElementQuadratureField can be PK1 in bulk continuum
     * mechanics, the pair (tractions, jumps) in interface continuum mechanics and the corresponding
     * quantities in shell structural mechanics.
     */
    ElementQuadratureField<real>* VariablesForResidual();

    /**
     * Accessor to region's material
     */
    virtual Material const* material() const;

  protected:
    /**
     * Accessor region's internal variables
     */
    virtual ElementQuadratureField<summit::real> const* internals() const;

    /**
     * Method to compute the contribution at 1 quadrature point to the residual
     * @param[in] e the local index of the element
     * @param[in] q the local index of the quadrature point
     * @param[in] P a vector containing the stress tensor at the quadrature
     *            point
     * @param[out] rl a vector containing the local residual of the element
     */
    virtual void _residualIntegrand(elem_t e,
                                    quad_t q,
                                    size_t nen,
                                    size_t spatial_dim,
                                    std::vector<real> const& P,
                                    std::vector<real> const& C,
                                    std::vector<real> const& dPdu,
                                    std::vector<real> const& F,
                                    std::vector<real> f,
                                    std::vector<real>& rl,
                                    std::vector<real>& cap,
                                    std::vector<real>& dprimal,
                                    real dt) const;
    /**
     * Method to compute the contribution at 1 quadrature point to the residual and the tangent
     * matrix
     * @param[in] e the local index of the element
     * @param[in] q the local index of the quadrature point
     * @param[in] P a vector containing the 1st Piola-Kirchhoff stress tensor at the quadrature
     *            point
     * @param[in] C a vector containing the Lagrangian tangent modulus tensor at the quadrature
     *            point
     * @param[out] rl a vector containing the local residual of the element
     * @param[out] kl a vector containing the local tangent matrix of the element
     */
    virtual void _stiffnessIntegrand(elem_t e,
                                     quad_t q,
                                     std::vector<real> const& P,
                                     std::vector<real> const& C,
                                     std::vector<real> const& dPdu,
                                     std::vector<real> const& F,
                                     std::vector<real> const& dF,
                                     std::vector<real>& bodyForce,
                                     std::vector<real>& dbodyForce,
                                     std::vector<real>& dfdGrad, 
                                     std::vector<real>& kl,
                                     std::vector<real>& cap,
                                     real dt) const;
  private:
    /**
     * Accessor/mutator to the internal variable array, a purely virtual method in the base class
     * @return pointer to the internal variable field ElementQuadratureField *
     */
    virtual ElementQuadratureField<real>* internalVariables();

    // Attributes
  protected:
    /**
     * Pointer to a set of solid (CG) elements
     */
    ElementSetBulk const* _element_set;

    /**
     * Reference to a material
     */
    ReactionDiffusionMaterial const& _material;


};
}  // namespace summit
#endif  // SUMMIT_THERMAL_MODEL_BULK_REGION_H

// end of file
