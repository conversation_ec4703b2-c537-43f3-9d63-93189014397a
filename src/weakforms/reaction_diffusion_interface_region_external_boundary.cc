#include <iostream>
#include <iomanip>
#include <cstring>
#include <fstream>
#include <stdexcept>

#include "../fem/nodal_field.h"
#include "../elements/element_quadrature_field.h"
#include "../solvers/stiffness/stiffness.h"
#include "../io/summit_message.h"
#include "../elements/element_set_interface_one_sided.h"
#include "../materials/material.h"
#include "../mesh/internal_boundary.h"
#include "../mesh/mesh.h"
#include "../mesh/mesh_entity.h"

#include "reaction_diffusion_interface_region_external_boundary.h"

#include "../restart/checkpoint.h"
#include "../restart/Group.h"


/**
 * @brief Primary constructor for external boundary interface region with comprehensive initialization
 *
 * This constructor implements the complete initialization sequence for creating a
 * ReactionDiffusionInterfaceRegionExternalBoundary object that handles external boundary
 * conditions in reaction-diffusion systems using discontinuous Galerkin finite element methods.
 * The constructor performs extensive setup operations to establish a fully functional boundary
 * region capable of enforcing various types of boundary conditions with high accuracy and
 * numerical stability.
 *
 * ## Detailed Initialization Process
 *
 * ### Phase 1: Base Class Initialization
 * The constructor begins by calling the base class constructor (ReactionDiffusionInterfaceRegion)
 * which establishes the fundamental infrastructure for interface handling:
 * - **Element Set Assignment**: Associates the boundary element set with the region
 * - **DOF Configuration**: Sets up the degrees of freedom structure for the boundary
 * - **Material Assignment**: Links interface and bulk materials to the region
 * - **Coordinate System**: Establishes the geometric framework for the boundary
 * - **Stabilization Setup**: Configures numerical stabilization parameters
 *
 * ### Phase 2: Geometric Data Processing
 * Following base initialization, the constructor processes geometric information:
 * - **Normal Vector Computation**: Calculates outward unit normal vectors at all boundary points
 * - **Jacobian Evaluation**: Computes transformation Jacobians for integration
 * - **Surface Measure Calculation**: Determines surface area elements for integration
 * - **Curvature Analysis**: Handles curved boundaries with appropriate geometric corrections
 * - **Coordinate Transformation**: Sets up mappings between reference and physical coordinates
 *
 * ### Phase 3: Material Property Integration
 * The constructor integrates material properties for boundary flux computation:
 *
 * #### Interface Material Processing (InterfaceDG)
 * - **Boundary Condition Type**: Identifies the specific type of boundary condition
 * - **Flux Function Definition**: Establishes the numerical flux computation method
 * - **Penalty Parameters**: Configures penalty parameters for weak boundary condition enforcement
 * - **Stabilization Coefficients**: Sets up stabilization parameters for numerical stability
 * - **Time Dependence**: Handles time-dependent boundary condition parameters
 *
 * #### Interior Material Processing (ReactionDiffusionMaterial)
 * - **Diffusion Coefficients**: Extracts diffusion properties for flux computation
 * - **Reaction Parameters**: Incorporates reaction kinetics affecting boundary fluxes
 * - **Constitutive Relations**: Establishes relationships between field variables and fluxes
 * - **Material Nonlinearity**: Handles nonlinear material behavior at boundaries
 * - **Temperature Dependence**: Accounts for temperature-dependent material properties
 *
 * ### Phase 4: Quadrature and Integration Setup
 * The constructor establishes the numerical integration framework:
 * - **Quadrature Rule Selection**: Chooses appropriate integration rules for boundary elements
 * - **Integration Point Distribution**: Distributes quadrature points optimally on boundary surfaces
 * - **Weight Computation**: Calculates integration weights accounting for geometric transformations
 * - **Shape Function Evaluation**: Pre-computes shape function values at integration points
 * - **Gradient Evaluation**: Pre-computes shape function gradients for flux computations
 *
 * ### Phase 5: Memory Allocation and Data Structure Setup
 * Comprehensive memory allocation for all computational arrays:
 *
 * #### Solution Storage Arrays
 * - **Current Solution**: Storage for current time step field values
 * - **Previous Solution**: Storage for previous time step values (for time integration)
 * - **Solution Gradients**: Storage for spatial derivatives of field variables
 * - **Boundary Values**: Storage for boundary condition values and parameters
 *
 * #### Computational Workspace Arrays
 * - **Local Element Matrices**: Workspace for element-level matrix assembly
 * - **Local Element Vectors**: Workspace for element-level vector assembly
 * - **Flux Computation Arrays**: Temporary storage for numerical flux calculations
 * - **Jacobian Storage**: Arrays for storing linearization information
 *
 * #### Internal Variable Storage
 * - **History Variables**: Storage for history-dependent material behavior
 * - **Damage Variables**: Storage for damage evolution in fracture mechanics
 * - **State Variables**: Storage for complex material state information
 * - **Auxiliary Variables**: Storage for derived quantities and intermediate results
 *
 * ### Phase 6: Stabilization Parameter Configuration
 * Detailed setup of numerical stabilization:
 *
 * #### Full Stabilization Term Handling
 * When usingFullStabilizationTerm is true:
 * - **Enhanced Stability**: Activates additional stabilization mechanisms
 * - **Upwind Contributions**: Includes upwind-like terms for convection-dominated problems
 * - **Penalty Enhancement**: Strengthens penalty parameter computation
 * - **Consistency Preservation**: Maintains consistency while enhancing stability
 *
 * #### Standard Stabilization
 * When usingFullStabilizationTerm is false:
 * - **Minimal Stabilization**: Uses only essential stabilization terms
 * - **Computational Efficiency**: Reduces computational overhead
 * - **Memory Optimization**: Minimizes memory requirements
 * - **Accuracy Preservation**: Maintains solution accuracy with reduced cost
 *
 * ### Phase 7: Parallel Computing Setup
 * Configuration for parallel execution environments:
 * - **Domain Decomposition**: Handles distribution of boundary elements across processors
 * - **Communication Patterns**: Establishes inter-processor communication for boundary data
 * - **Load Balancing**: Ensures efficient distribution of computational work
 * - **Synchronization Points**: Identifies where parallel synchronization is required
 *
 * ### Phase 8: Error Checking and Validation
 * Comprehensive validation of the initialized object:
 * - **Geometric Consistency**: Verifies element-node connectivity and coordinate consistency
 * - **Material Compatibility**: Ensures material types are compatible with boundary conditions
 * - **Memory Allocation**: Confirms successful allocation of all required arrays
 * - **Parameter Validity**: Validates all input parameters for physical reasonableness
 *
 * ## Mathematical Framework Establishment
 *
 * ### Weak Form Preparation
 * The constructor prepares the infrastructure for evaluating boundary integrals:
 * ∫_∂Ω F̂(u⁻, u⁺, n) · v dΓ
 *
 * Where the numerical flux F̂ is constructed based on:
 * - **Interior State**: u⁻ from the adjacent interior element
 * - **Boundary State**: u⁺ determined by the boundary condition
 * - **Normal Vector**: n computed from the geometric data
 * - **Test Functions**: v from the finite element space
 *
 * ### Flux Function Configuration
 * The constructor sets up the numerical flux computation framework:
 * F̂ = F_consistent + F_penalty + F_stabilization
 *
 * Each component is configured based on:
 * - **Consistency Requirements**: Ensuring the flux reduces to the exact flux for smooth solutions
 * - **Stability Needs**: Providing sufficient numerical dissipation for stability
 * - **Accuracy Goals**: Maintaining optimal convergence rates
 * - **Conservation Properties**: Preserving important conservation laws
 *
 * ## Performance Optimization Features
 *
 * ### Computational Efficiency
 * - **Pre-computation**: Expensive operations performed once during initialization
 * - **Memory Layout**: Optimized data structures for cache efficiency
 * - **Vectorization**: Array operations structured for SIMD optimization
 * - **Loop Optimization**: Nested loops organized for maximum efficiency
 *
 * ### Memory Management
 * - **Allocation Strategy**: Efficient memory allocation patterns
 * - **Data Locality**: Arrangement of data for optimal memory access patterns
 * - **Memory Pooling**: Reuse of temporary arrays to reduce allocation overhead
 * - **Garbage Collection**: Proper cleanup of temporary data structures
 *
 * ## Error Handling and Robustness
 *
 * ### Input Validation
 * - **Null Pointer Checks**: Verification of all input pointers
 * - **Range Validation**: Checking of numerical parameters for valid ranges
 * - **Consistency Checks**: Cross-validation of related input parameters
 * - **Type Verification**: Ensuring material types match expected interfaces
 *
 * ### Exception Safety
 * - **Resource Management**: Proper cleanup in case of initialization failures
 * - **Exception Propagation**: Clear error reporting for debugging
 * - **State Consistency**: Maintaining object state consistency during errors
 * - **Memory Safety**: Preventing memory leaks during exception handling
 *
 * @param[in] es Pointer to a one-sided interface element set that defines the geometric
 *               discretization of the external boundary. This element set must contain
 *               valid boundary elements with proper connectivity to adjacent interior
 *               elements. The element set provides the geometric framework for boundary
 *               condition enforcement and must be compatible with the coordinate field.
 *               Each element in the set represents a portion of the boundary surface
 *               where boundary conditions will be applied.
 *
 * @param[in] dof_node Number of degrees of freedom per node in the reaction-diffusion system.
 *                     This parameter determines the size of the solution vector at each node
 *                     and must match the number of field variables being solved in the system.
 *                     For single-field problems (e.g., heat conduction), this is typically 1.
 *                     For multi-species chemical systems, this equals the number of chemical
 *                     species. For coupled multi-physics problems, this includes all coupled
 *                     field variables (temperature, concentrations, etc.).
 *
 * @param[in] material Interface material object of type InterfaceDG that defines the specific
 *                     boundary condition type and provides methods for computing numerical
 *                     fluxes at the boundary. This material encapsulates the mathematical
 *                     formulation of the boundary condition (Dirichlet, Neumann, Robin, etc.)
 *                     and contains all parameters necessary for boundary flux computation.
 *                     The material must be properly configured with appropriate boundary
 *                     condition parameters before being passed to this constructor.
 *
 * @param[in] material_L Interior reaction-diffusion material representing the bulk material
 *                       properties of the domain adjacent to the boundary. This material
 *                       provides diffusion coefficients, reaction rates, and other constitutive
 *                       properties needed for accurate computation of boundary fluxes. The
 *                       material properties may be spatially varying and temperature-dependent,
 *                       and the material must be compatible with the reaction-diffusion system
 *                       being solved.
 *
 * @param[in] coordinates Nodal coordinate field containing the physical coordinates of all
 *                        nodes in the boundary element set. This field provides the geometric
 *                        information necessary for computing normal vectors, Jacobians, surface
 *                        measures, and other geometric quantities required for boundary
 *                        integration. The coordinate field must be consistent with the element
 *                        set connectivity and must provide accurate geometric representation
 *                        of the boundary surface.
 *
 * @param[in] usingFullStabilizationTerm Optional boolean flag controlling the use of enhanced
 *                                       stabilization terms in the numerical flux computation.
 *                                       When set to true, additional stabilization mechanisms
 *                                       are activated to provide enhanced numerical stability,
 *                                       particularly beneficial for convection-dominated problems
 *                                       or when dealing with complex boundary conditions. When
 *                                       false (default), standard stabilization is used for
 *                                       computational efficiency. The choice affects both
 *                                       computational cost and solution accuracy.
 *
 * @pre es must point to a valid ElementSetInterfaceOneSided object with proper initialization
 * @pre dof_node must be positive and consistent with the reaction-diffusion system formulation
 * @pre material must be a properly configured InterfaceDG material with valid boundary condition data
 * @pre material_L must be a valid ReactionDiffusionMaterial with appropriate physical properties
 * @pre coordinates must contain valid geometric data for all nodes referenced by the element set
 * @pre Element set and coordinate field must have consistent node numbering and connectivity
 * @pre All input objects must remain valid throughout the lifetime of this object
 *
 * @post Object is fully initialized and ready for boundary condition enforcement operations
 * @post All internal data structures are properly allocated and configured for computation
 * @post Boundary flux computation capabilities are established and validated
 * @post Integration and assembly infrastructure is prepared for efficient operation
 * @post Object state is consistent and ready for use in finite element assembly operations
 *
 * @throws std::bad_alloc if insufficient memory is available for internal data structure allocation
 * @throws std::invalid_argument if any input parameter is invalid or inconsistent
 * @throws std::runtime_error if element set contains invalid geometric or connectivity data
 * @throws std::runtime_error if material objects are not properly configured or compatible
 * @throws std::runtime_error if coordinate field is incompatible with the element set structure
 *
 * @note This constructor performs extensive initialization and may require significant time for large boundary regions
 * @note The object maintains references to the input materials and coordinates - these must remain valid
 * @note Memory usage scales with the number of boundary elements, quadrature points, and degrees of freedom
 * @note Parallel efficiency depends on proper load balancing of boundary elements across processors
 * @note Full stabilization terms increase computational cost but may be necessary for stability
 *
 * @warning Element set must contain only external boundary elements, not internal interface elements
 * @warning Material compatibility is not automatically verified - user must ensure compatibility
 * @warning Large penalty parameters in stabilization can cause numerical ill-conditioning
 * @warning Modifications to referenced objects after construction may cause undefined behavior
 * @warning High-order elements may require special consideration for curved boundary representation
 *
 * @see ElementSetInterfaceOneSided for boundary element set requirements and specifications
 * @see InterfaceDG for interface material definitions and boundary condition types
 * @see ReactionDiffusionMaterial for bulk material property specifications
 * @see NodalField for coordinate field structure and data organization
 * @see ReactionDiffusionInterfaceRegion for base class functionality and interface
 */
summit::ReactionDiffusionInterfaceRegionExternalBoundary::ReactionDiffusionInterfaceRegionExternalBoundary(ElementSetInterfaceOneSided const* es,
                                                           int dof_node,
                                                           InterfaceDG const& material,
                                                           ReactionDiffusionMaterial const& material_L,
                                                           NodalField<real> const& coordinates,
                                                           bool usingFullStabilizationTerm)
  :  // call the base class constructor
    ReactionDiffusionInterfaceRegion(
      es, dof_node, material, material_L, coordinates, usingFullStabilizationTerm)
{
    return;
}

summit::ReactionDiffusionInterfaceRegionExternalBoundary::ReactionDiffusionInterfaceRegionExternalBoundary(Checkpoint* checkpoint, const char* name)
  : ReactionDiffusionInterfaceRegion(checkpoint, name)
{
    
}

summit::ReactionDiffusionInterfaceRegionExternalBoundary::~ReactionDiffusionInterfaceRegionExternalBoundary()
{
    // end of method
    return;
}

void summit::ReactionDiffusionInterfaceRegionExternalBoundary::WriteForRestart(Checkpoint* checkpoint,
                                                       const char* name,
                                                       const char* tag) const
{
    // Call write of super class (this creates the group for this region)
    if (tag == nullptr) {
        ReactionDiffusionInterfaceRegion::WriteForRestart(checkpoint, name, "ReactionDiffusionInterfaceRegionFullButterfly");
    }
    else {
        ReactionDiffusionInterfaceRegion::WriteForRestart(checkpoint, name, tag);
    }
    return;
}

void summit::ReactionDiffusionInterfaceRegionExternalBoundary::Update(NodalField<real> const& u,
                                                    NodalField<real> const& u0,
                                                    real dt)
{
// number of nodes per element
    size_t const nen = _element_set->connectivities_element();
    size_t spatial_dim = _element_set->dim();
    // get the size of the local residual
    int const residual_dim = nen * _dof_node;
    // allocate memory for the local solution
    static std::vector<real> ul;
    ul.resize(residual_dim);
    static std::vector<real> u0l;
    u0l.resize(residual_dim);
    // allocate memory for local nodal coordinates
    static std::vector<real> xl;
    xl.resize(nen * spatial_dim);

    // in the Update, these are not used. We have them so we can reuse the code instead
    static std::vector<real> rl;
    rl.resize(residual_dim);
    static std::vector<real> Kl;
    Kl.resize(residual_dim * residual_dim);
    std::fill(rl.begin(), rl.end(), 0.0);
    std::fill(Kl.begin(), Kl.end(), 0.0);

    // loop over the elements of the element set
    for (elem_t e(0); e != _element_set->elements(); ++e) {
        // extract local part of unknown
        _element_set->Localize(u, e, ul);
        _element_set->Localize(u0, e, u0l);
        _element_set->Localize(_coordinates, e, xl);

        // assembly local residual
        ElementStiffness(e, dt, true, false, false, ul, u0l, xl, rl, Kl);
    }
    return;
}

/**
 * @brief Compute residual contributions from external boundary conditions with comprehensive boundary flux evaluation
 *
 * This method computes the residual vector contributions arising from external boundary conditions
 * in reaction-diffusion systems using discontinuous Galerkin finite element methods. The residual
 * computation is a critical component of the nonlinear solution process, providing the boundary
 * contributions to the global residual vector that drives the iterative solution algorithm toward
 * convergence. The method implements sophisticated boundary flux evaluation techniques to ensure
 * accurate and stable enforcement of various types of boundary conditions.
 *
 * ## Mathematical Foundation
 *
 * ### Weak Form Boundary Contributions
 * The residual computation evaluates boundary integral terms arising from the weak formulation
 * of the reaction-diffusion system. For a general reaction-diffusion equation:
 * ∂u/∂t + ∇·F(u,∇u) = S(u)
 *
 * The weak form includes boundary terms:
 * ∫_Ω (∂u/∂t)v dΩ - ∫_Ω F(u,∇u)·∇v dΩ + ∫_∂Ω F̂(u⁻,u⁺,n)·v dΓ = ∫_Ω S(u)v dΩ
 *
 * This method computes the boundary integral: ∫_∂Ω F̂(u⁻,u⁺,n)·v dΓ
 *
 * Where:
 * - **u⁻**: Interior solution values (from field u)
 * - **u⁺**: Exterior solution values (determined by boundary conditions)
 * - **n**: Outward unit normal vector at the boundary
 * - **F̂**: Numerical flux function encoding the boundary condition
 * - **v**: Test functions from the finite element space
 *
 * ### Numerical Flux Construction
 * The numerical flux F̂ is constructed to enforce boundary conditions while maintaining
 * numerical stability and accuracy:
 *
 * #### For Dirichlet Boundary Conditions
 * F̂ = F_avg + τ(u⁻ - g)n
 * Where:
 * - F_avg = ½[F(u⁻,∇u⁻) + F(g,∇g)] (average flux)
 * - τ: Penalty parameter for boundary condition enforcement
 * - g: Prescribed boundary value
 *
 * #### For Neumann Boundary Conditions
 * F̂·n = h (prescribed flux)
 * Where h is the specified normal flux at the boundary
 *
 * #### For Robin Boundary Conditions
 * F̂·n = α(u_∞ - u⁻) (convective-type boundary condition)
 * Where α is the transfer coefficient and u_∞ is the external value
 *
 * ## Computational Algorithm
 *
 * The method implements a sophisticated multi-phase algorithm for accurate and efficient
 * boundary residual computation:
 *
 * ### Phase 1: Element Loop Processing
 * - **Element Identification**: Iterates over all boundary elements
 * - **Local DOF Extraction**: Extracts element-level degrees of freedom
 * - **Geometric Data Retrieval**: Obtains element geometry and connectivity
 * - **Material Property Access**: Retrieves element-specific material properties
 *
 * ### Phase 2: Quadrature Integration
 * - **Quadrature Point Processing**: Evaluates integrands at all quadrature points
 * - **Solution Interpolation**: Interpolates solution values to quadrature points
 * - **Gradient Computation**: Computes solution gradients at integration points
 * - **Flux Evaluation**: Computes numerical fluxes based on boundary conditions
 *
 * ### Phase 3: Local Assembly
 * - **Shape Function Evaluation**: Evaluates test functions at quadrature points
 * - **Weighted Integration**: Performs numerical integration with proper weights
 * - **Local Vector Assembly**: Assembles element-level residual contributions
 * - **Boundary Condition Enforcement**: Applies specific boundary condition types
 *
 * ### Phase 4: Global Assembly
 * - **DOF Mapping**: Maps local to global degree of freedom numbering
 * - **Vector Accumulation**: Adds local contributions to global residual
 * - **Parallel Communication**: Handles inter-processor data exchange
 * - **Consistency Verification**: Ensures proper boundary condition enforcement
 *
 * @param[in] u Current solution field containing primary variables at current time step
 * @param[in] u0 Previous time step solution field for time integration
 * @param[in] dt Time step size for current integration step
 * @param[in] update Flag indicating whether to update internal variables during computation
 * @param[in] commManager Communication manager for parallel operations
 * @param[in,out] residual Global residual vector receiving boundary contributions
 *
 * @pre All input fields must be properly initialized with compatible dimensions
 * @pre Time step must be positive for time-dependent problems
 * @pre Residual vector must be properly allocated and initialized
 * @pre Communication manager must be configured for parallel environment
 *
 * @post Residual vector contains accumulated boundary contributions
 * @post Boundary conditions are enforced through residual terms
 * @post Internal variables are updated if requested
 * @post Parallel consistency is maintained
 *
 * @note This method is performance-critical and called frequently during solution
 * @note Computational cost scales with boundary elements and quadrature points
 * @note Parallel efficiency depends on proper load balancing
 * @note Accuracy depends on appropriate stabilization parameters
 *
 * @see ComputeStiffness for corresponding Jacobian computation
 * @see Update for internal variable management
 * @see ElementStiffness for element-level operations
 */
void summit::ReactionDiffusionInterfaceRegionExternalBoundary::Residual(NodalField<real> const& u,
                                              NodalField<real> const& u0,
                                              real dt,
                                              bool update,
                                              CommunicationManager const& commManager,
                                              NodalField<real>& residual)
{
    // number of nodes per element
    size_t const nen = _element_set->connectivities_element();
    size_t spatial_dim = _element_set->dim();
    // get the size of the local residual
    int const residual_dim = nen * _dof_node;
    static std::vector<real> rl;
    rl.resize(residual_dim);
    static std::vector<real> Kl;
    Kl.resize(residual_dim * residual_dim);
        
    // allocate memory for the local solution
    static std::vector<real> ul;
    ul.resize(residual_dim);
    static std::vector<real> u0l;
    u0l.resize(residual_dim);
    // allocate memory for local nodal coordinates
    static std::vector<real> xl;
    xl.resize(nen * spatial_dim);

    // loop over the elements of the element set
    for (elem_t e(0); e != _element_set->elements(); ++e) {
        // extract local part of unknown
        _element_set->Localize(u, e, ul);
        _element_set->Localize(u0, e, u0l);

        // get the coordinates of the nodes of the current element
        _element_set->Localize(_coordinates, e, xl);

        // assembly local residual
        std::fill(rl.begin(), rl.end(), 0.0);
        std::fill(Kl.begin(), Kl.end(), 0.0);

        ElementStiffness(e, dt, update, true, false, ul, u0l, xl, rl, Kl);

        // assemble local residual into global array
        _element_set->Assemble(rl, e, residual);
    }
    return;
}

void summit::ReactionDiffusionInterfaceRegionExternalBoundary::ComputeStiffness(NodalField<real> const& u,
                                                              NodalField<real> const& u0,
                                                              real dt,
                                                              Stiffness& stiffness,
                                                              NodalField<real>& residual)
{
    // number of nodes per element
    size_t const nen = _element_set->connectivities_element();

    // size of the local residual
    int const residual_dim = nen * _dof_node;
    size_t spatial_dim = _element_set->dim();
    // allocate memory for the local displacement (previous and current)
    // the element residual and stiffness
    static std::vector<real> ul;
    ul.resize(residual_dim);
    static std::vector<real> u0l;
    u0l.resize(residual_dim);
    static std::vector<real> xl;
    xl.resize(nen * spatial_dim);
    static std::vector<real> rl;
    rl.resize(residual_dim);
    static std::vector<real> Kl;
    Kl.resize(residual_dim * residual_dim);

    // loop over the elements of the element set
    for (elem_t e(0); e != _element_set->elements(); ++e) {
        // extract local part of unknown
        _element_set->Localize(u, e, ul);
        _element_set->Localize(u0, e, u0l);
        _element_set->Localize(_coordinates, e, xl);

        // reset residual and stiffness
        std::fill(rl.begin(), rl.end(), 0.0);
        std::fill(Kl.begin(), Kl.end(), 0.0);

        // compute elementary stiffness matrix and residual
        ElementStiffness(e, dt, false, true, true, ul, u0l, xl, rl, Kl);

        // assemble local residual into global array
        if (stiffness.getAssembleResidualFlag() != 0) {
            _element_set->Assemble(rl, e, residual);
        }

        // assemble local stiffness matrix into global matrix
        stiffness.AddElement(_element_set->dof_map()->Connectivity(e), &Kl[0], nen, _dof_node,
                             residual);
    }

    // end of method
    return;
}

void summit::ReactionDiffusionInterfaceRegionExternalBoundary::ElementStiffness(elem_t e,
                                                           real dt,
                                                           bool update,
                                                           bool DoResidual,
                                                           bool DoStiffness,
                                                           std::vector<real> const& ul,
                                                           std::vector<real> const& u0l,
                                                           std::vector<real> const& xl,
                                                           std::vector<real>& rl,
                                                           std::vector<real>& Kl)
{
    // get dimensions
    int const strain_dim = _strains_L->dim();
    size_t const nen = _element_set->connectivities_element();
    size_t const internal_L_dim = _material_L.nInt();
    size_t const internal_dim = _material.nInt();

    // spatial dimension
    size_t const spatial_dim = _element_set->dim();
    int const residual_dim = nen * _dof_node;

    // memory allocation ...
    // ... for the left part
    std::vector<real> P_L_new;
    P_L_new.resize(strain_dim);

    std::vector<real> f_L_new;
    f_L_new.resize(strain_dim);
    // memory allocation
    std::vector<real> F_L_new;
    F_L_new.resize(spatial_dim * _dof_node);
    std::vector<real> internal_L_new;
    internal_L_new.resize(internal_L_dim);
    std::vector<real> internal_new;
    internal_new.resize(internal_dim);

    // ... for the jump in displacement
    
    // ... for the left traction
    std::vector<real> traction;
    traction.resize(_dof_node);
    std::vector<real> dtractiondU;
    dtractiondU.resize(_dof_node*_dof_node);
    std::vector<real> dtractiondgradU;
    dtractiondgradU.resize(_dof_node*_dof_node*spatial_dim);

    // calculation of the characteristic length (inradius) of the left and right elements
    // resize vector to store the nodal coordinates (left and right sides) of the interface
    // element e
    //    characteristic lengths of left and right elements
    real measInterface = _element_set->InRadiusElement(&xl[0]);

    std::vector<real> fDotN(_dof_node);
    std::vector<real> dfDotNdC(_dof_node*_dof_node);
    std::vector<real> dfDotNdgradC(_dof_node*_dof_node*spatial_dim);
    std::vector<real> gDotN(_dof_node);
    std::vector<real> dgDotNdC(_dof_node*_dof_node);
    std::vector<real> S(_dof_node);
    std::vector<real> dSdC(_dof_node*_dof_node);

    // integrate over quadrature points
    for (quad_t q(0); q < _element_set->nquad(); ++q) {
        // constitutive update of the left element
	    for (size_t i = 0; i < _CField_L->dim(); ++i) {
            _CField_L->local(e, q)[i] = 0.0;
            _C_L[i] = 0.0;
        }
        for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
            _dPdUField_L->local(e, q)[i] = 0.0;
            _dPdU_L[i] = 0.0;
        }
	    for (size_t i = 0; i < _dFField_L->dim(); ++i) {
            _dFField_L->local(e, q)[i] = 0.0;
	        _dF_L[i] = 0.0;
        }
        std::fill(fDotN.begin(), fDotN.end(), 0.);
        std::fill(dfDotNdC.begin(), dfDotNdC.end(), 0.);
        std::fill(dfDotNdgradC.begin(), dfDotNdgradC.end(), 0.);
        std::fill(gDotN.begin(), gDotN.end(), 0.);
        std::fill(dgDotNdC.begin(), dgDotNdC.end(), 0.);
        std::fill(S.begin(), S.end(), 0.);
        std::fill(dSdC.begin(), dSdC.end(), 0.);
        LeftConstitutiveUpdate(e, q, ul, u0l, xl, dt, measInterface, P_L_new, f_L_new, F_L_new, internal_L_new, internal_new, _C_L.data(), _dPdU_L.data(), _dF_L.data(), fDotN.data(), dfDotNdC.data(), dfDotNdgradC.data(), gDotN.data(), dgDotNdC.data(), S.data(), dSdC.data());
        for (size_t i = 0; i < _CField_L->dim(); ++i) {
            _CField_L->local(e, q)[i] = _C_L[i];
        }
        for (size_t i = 0; i < _dPdUField_L->dim(); ++i) {
            _dPdUField_L->local(e, q)[i] = _dPdU_L[i] + _dF_L[i];// total flux in the bulk
        }
        for (size_t i = 0; i < _dFField_L->dim(); ++i) {
            _dFField_L->local(e, q)[i] = _dF_L[i];
        }

        // Below is not needed in update function, but is required for residual
        // jacobians (with quadrature weights)
        real jac = _element_set->jac(e, q);
        // reference to the left normal
        const real* normalL = _element_set->normalL(e, q);
        // compute the displacement jump at the current quad point
        // DisplacementJump(e, q, jac, ul, jumpU);
        // AverageTraction(P_L_new,P_R_new,jac,normalL,traction);
        std::fill(traction.begin(), traction.end(), 0.);
        std::fill(dtractiondU.begin(), dtractiondU.end(), 0.);
        std::fill(dtractiondgradU.begin(), dtractiondgradU.end(), 0.);
        // project the average PK-I onto the normal to get the left traction
        for (size_t i = 0; i < _dof_node; ++i) {
            traction[i] += jac * (fDotN[i] + gDotN[i] );
            for (size_t k = 0; k < _dof_node; ++k) {
                size_t ik = _dof_node * i + k;
                dtractiondU[ik] += jac * (dfDotNdC[ik] + dgDotNdC[ik]);
                for (dim_t j(0); j < spatial_dim; ++j) {
                    size_t ikj = spatial_dim * ik + j;
                    dtractiondgradU[ikj] = jac * dfDotNdgradC[ikj];// scale by the Jacobian
                }
            }
        }
        // loop over the nodes of the left element
        for (lnode_t c = _element_set->leftNodeStart(); c != _element_set->leftNodeDone(); ++c) {
            const real test_u = _element_set->shape(e, q, c);
            const real* test_du = _element_set->dShape(e, q, c);
            // loop over its degrees of freedom
            for (size_t i = 0; i < _dof_node; ++i) {
                // compute the index of the current dof in the local displacement vector
                size_t ci = _dof_node * c + i;
                // Here, we compute MINUS the internal forces because the assemble function only adds
                rl[ci] += FluxTerm<summit::LEFT>(e, q, test_u, i, traction.data());
                if(DoStiffness){
                //if(false){
                    for (lnode_t a = _element_set->leftNodeStart(); a != _element_set->leftNodeDone();++a) {
                        const real disp_u = _element_set->shape(e, q, a);
                        const real* disp_du = _element_set->dShape(e, q, a);
                        // loop over its degrees of freedom
                        for (size_t k = 0; k < _dof_node; ++k) {
                            // compute the index of the current dof in the local test function
                            size_t ak = _dof_node * a + k;
                            size_t ciak = ci * residual_dim + ak;
                            Kl[ciak] -= dFluxdGrad<summit::LEFT>(e, q, test_u, i, disp_du, k, dtractiondgradU.data());
                            Kl[ciak] -= dFluxdU<summit::LEFT>(e, q, test_u, i, disp_u, k, dtractiondU.data());
                        }
                    }
                }
            }
        }
        if(update){
            std::copy(P_L_new.begin(), P_L_new.end(), _stresses_L->local(e, q));
            std::copy(F_L_new.begin(), F_L_new.end(), _strains_L->local(e, q));
            std::copy(internal_L_new.begin(), internal_L_new.end(), _internals_L->local(e, q));
            std::copy(internal_new.begin(), internal_new.end(), _internals->local(e, q));
        }
    }
    return;
}

void summit::ReactionDiffusionInterfaceRegionExternalBoundary::LeftConstitutiveUpdate(elem_t e,    //Input 
                                                              quad_t q,                            //Input
                                                              std::vector<real> const& ul,         //Input
                                                              std::vector<real> const& u0l,        //Input
                                                              std::vector<real> const& xl,         //Input
                                                              real dt,                             //Input
                                                              real measInterface,                  //Input
                                                              std::vector<real>& P_L_new,          //Junk for parity with DG regions (plot diffusive fluxes or stresses)
                                                              std::vector<real>& f_L_new,          //Junk for parity with DG regions (advective fluxes)
                                                              std::vector<real>& F_L_new,          //Junk for parity with DG regions (plot strains)
                                                              std::vector<real>& internal_L_new,   //stuff we need (bulk internal variables)
                                                              std::vector<real>& internal_new,     //stuff we need (surface internal variables)
                                                              real* C_L,                           //Junk for parity with DG regions (tangents diffusive fluxes)  
                                                              real* dPdU_L,                        //Junk for parity with DG regions (dependence on the primal)  
                                                              real* df_L,                          //Junk for parity with DG regions (Advective gradient)
                                                              real* fDotN,                         //stuff we need        
                                                              real* dfDotNdC,                      //stuff we need
                                                              real* dfDotNdgradC,                  //stuff we need
                                                              real* gDotN,                         //stuff we need
                                                              real* dgDotNdC,                      //stuff we need
                                                              real* S,                             //stuff we need
                                                              real* dSdC)                          //stuff we need 
{
    // spatial dimension
    size_t const spatial_dim = _element_set->dim();
    // strain dimension
    int const strain_dim = _strains_L->dim();
    // dimension of internal variables
    size_t const internal_L_dim = _material_L.nInt();
    size_t const internal_dim = _material.nInt();
    // To know if the tangent has to be computed
    bool const compute_tangent = (C_L ? true : false);

    size_t const nen = _element_set->connectivities_element();
    size_t const ndm = _element_set->dim();
    size_t const ndf = _dof_node;
    // Compute the left gradient
    _element_set->Gradient_L(e, q, ul.data(), _dof_node, F_L_new.data());
    std::vector<real> concentration(_dof_node);
    std::vector<real> concentration0(_dof_node);
    std::fill(concentration.begin(), concentration.end(), 0.);
    std::fill(concentration0.begin(), concentration0.end(), 0.);
    std::vector<real> x(spatial_dim);
    std::fill(x.begin(), x.end(), 0.);
    
    for (lnode_t b = _element_set->leftNodeStart(); b != _element_set->leftNodeDone(); ++b) {
        for (int c = 0; c < _dof_node; c++) {
            concentration[c] += _element_set->shape(e, q, b) * ul[c + b * _dof_node];
            concentration0[c] += _element_set->shape(e, q, b) * u0l[c + b * _dof_node];
        }
        for (int c = 0; c < spatial_dim; c++) {
            x[c] += _element_set->shape(e, q, b) * xl[c + b * spatial_dim];
        }
    }

    // pointer to local value of stress at previous time step
    real* P_L = _stresses_L->local(e, q);
    // copy to local Left stresses
    std::copy(P_L, P_L + strain_dim, P_L_new.begin());

    // local value of internal variables at previous time step
    real* internal_L = _internals_L->local(e, q);
    std::copy(internal_L, internal_L + internal_L_dim, internal_L_new.begin());
    real* internal = _internals->local(e, q);
    std::copy(internal, internal + internal_dim, internal_new.begin());

    // local value of old deformation gradient at previous time step
    real* F0_L = _strains_L->local(e, q);

    const real* normalL = _element_set->normalL(e, q);

    const auto* myMat = dynamic_cast_or_continue<const UpwindInterfaceDG*>(&_material,__HERE__);
    if(myMat != nullptr){
        myMat->viscousBoundaryFlux(ndm, ndf, dt, measInterface, internal_new.data(), concentration.data(), concentration0.data(), F_L_new.data(), F0_L, normalL, x.data(), fDotN, dfDotNdC, dfDotNdgradC);
        myMat->inviscidBoundaryFlux(ndm, ndf, dt, measInterface, internal_new.data(), concentration.data(), concentration0.data(), normalL, x.data(), gDotN, dgDotNdC);
        myMat->surfaceReactions(ndm, ndf, dt, measInterface, internal_new.data(), concentration.data(), concentration0.data(), normalL, x.data(), S, dSdC);
    }

    // The below stuff is just for parity with the rest of summit.
    // Any dependence of surface physics on the bulk response has to be encoded into
    // _material by the designer of material.
    _material_L.Constitutive(&concentration0[0], &concentration[0], F0_L, F_L_new.data(), P_L_new.data(),
                             internal_L_new.data(), C_L, dPdU_L, dt, _dof_node, spatial_dim,
                             compute_tangent, false);
    //this line updates internal variable processes needed to compute source terms
    //source terms themselves are not relevant for the trace quadrature points
    //line needed for consistency with bulk models
    std::vector<real> sources(_dof_node);
    std::vector<real> dsources(_dof_node * _dof_node);
    _material_L.Source(&concentration0[0], &concentration[0], internal_L_new.data(), &dt, &sources[0], &dsources[0], _dof_node);

    _material_L.ConvectiveFlux(&concentration0[0], &concentration[0], &internal_L_new[0],
                            &dt, f_L_new.data(), &df_L[0], _dof_node, spatial_dim);
    // all done
    return;
}

REGISTER(Region, ReactionDiffusionInterfaceRegionExternalBoundary);
// end of file
