// -*- C++ -*-
//
// summit development team
// massachusetts institute of technology
// (c) 2011-2014 all rights reserved
//

#include <pyre/journal.h>

#include <iostream>
#include <assert.h>
#include <stdexcept>

#include "../fem/nodal_field.h"
#include "../solvers/stiffness/stiffness.h"
#include "../elements/element_quadrature_field.h"

#include "../utils/math-util.h"
#include "../elements/element_set_bulk.h"
#include "../materials/reaction_diffusion_material.h"
#include "../mesh/mesh.h"
#include "../mathlib/functor.h"

#include "reaction_diffusion_region.h"

#include "../summit_enum.h"

summit::ReactionDiffusionRegion::ReactionDiffusionRegion(ElementSetBulk const* es,
                                                       int dof_node,
                                                       ReactionDiffusionMaterial const& material,
                                                       NodalField<real> const& coordinates)
  : Region(dof_node, coordinates),
    _element_set(es),
    _material(material)
{
    // id
    _myid = es->getRegionKey();
    // get the number of elements, the number of quadrature points and the
    // in the spatial dimension of the element set
    size_t nElements = _element_set->elements();
    size_t nQuadPoints = _element_set->nquad();

    // register jacobian field
    RegisterField("jacobian", es->jac());

    // allocate memory for internal variables
    _internals = new ElementQuadratureField<real>(nElements, nQuadPoints, _material.nInt());
    // register the internal variables as a field
    RegisterField("internal", _internals);

    // end of method
    return;
}

summit::ReactionDiffusionRegion::~ReactionDiffusionRegion()
{
    // delete the pointer to the element quadrature field for internal variables
    delete _internals;

    // all done
    return;
}

void summit::ReactionDiffusionRegion::ElementaryResidual(elem_t e,
                                                        std::vector<real> const& concentrationl,
                                                        std::vector<real> const& concentration0l,
                                                        std::vector<real> const& xl,
                                                        real dt,
                                                        bool update,
                                                        std::vector<real>& rl)
{
    // number of internal variables
    int ninternal = _internals->dim();
    // strain dimension
    size_t strain_dim = _dof_node * _element_set->dim();
    // number of nodes per element
    size_t nen = _element_set->nodes_element();

    std::vector<real> concentration(_dof_node);
    std::vector<real> concentration0(_dof_node);
    std::vector<real> Dconcentration(strain_dim);
    std::vector<real> Dconcentration0(strain_dim);
    std::vector<real> P_new(strain_dim);
    std::vector<real> internal_new(ninternal);
    size_t spatial_dim = _element_set->dim();
    std::vector<real> C(_dof_node * spatial_dim * _dof_node * spatial_dim);
    std::vector<real> dPdu(_dof_node * spatial_dim * _dof_node);
    std::vector<real> F(_dof_node * spatial_dim);
    std::vector<real> dF(_dof_node * spatial_dim * _dof_node);    
    // loop over quadrature points
    for (quad_t q(0); q < _element_set->nquad(); ++q) {
        std::fill(dPdu.begin(), dPdu.end(), 0.0);
        std::fill(C.begin(), C.end(), 0.0);
        std::fill(F.begin(), F.end(), 0.0);
        std::fill(dF.begin(), dF.end(), 0.0);

        std::fill(concentration.begin(), concentration.end(), 0.);
        std::fill(Dconcentration.begin(), Dconcentration.end(), 0.);
        std::fill(concentration0.begin(), concentration0.end(), 0.);
        std::fill(Dconcentration0.begin(), Dconcentration0.end(), 0.);
        //   loop over the nodes of the elements
        for (int c = 0; c < _dof_node; c++) {
            for (lnode_t b(0); b < nen; ++b) {
                // add contribution of the node b
                concentration[c] += _element_set->shape(e, q, b) * concentrationl[c + b * _dof_node];
                concentration0[c] += _element_set->shape(e, q, b) * concentration0l[c + b * _dof_node];
            }
        }
        //   loop over the nodes of the elements
        _element_set->Gradient(e, q, &concentration0l[0], _dof_node, &Dconcentration0[0]);
        _element_set->Gradient(e, q, &concentrationl[0], _dof_node, &Dconcentration[0]);
        
        // local value of internal variables at previous time step
        real* internal = _internals->local(e, q);
        // make local copy of internal variables at this quadrature point
        std::copy(internal, internal + ninternal, internal_new.begin());
        
        _material.Constitutive(&concentration0[0], &concentration[0], &Dconcentration0[0], &Dconcentration[0], &P_new[0], &internal_new[0],
                               C.data(), dPdu.data(), dt, _dof_node, spatial_dim);

        // jacobians (with quadrature weights)
        real jac = _element_set->jac(e, q);

        // pre-multiply the heat flux by the jacobian
        for (size_t i = 0; i < strain_dim; ++i) {
            P_new[i] *= jac;
        }
        for (int i = 0; i < _dof_node * spatial_dim * _dof_node * spatial_dim; ++i) {
            C[i] *= jac;
        }
        for (int i = 0; i < _dof_node * spatial_dim * _dof_node; ++i) {
            dPdu[i] *= jac;
                
        }
        std::vector<real> f(_dof_node);
        std::fill(f.begin(), f.end(), 0.);
        std::vector<real> df(_dof_node * _dof_node);
        std::fill(df.begin(), df.end(), 0.);
        std::vector<real> dfdGrad(_dof_node * strain_dim);
        std::fill(dfdGrad.begin(), dfdGrad.end(), 0.);
        _material.Source(&concentration0[0], &concentration[0], &Dconcentration0[0], &Dconcentration[0], &internal_new[0], &dt, &f[0], &df[0], &dfdGrad[0], _element_set->dim(), _dof_node);
        for (size_t i = 0; i < _dof_node; ++i) {
            f[i] *= jac;
        }
        _material.ConvectiveFlux(&concentration0[0], &concentration[0], &internal_new[0],
                            &dt, F.data(), dF.data(), _dof_node, spatial_dim);

        for (size_t i = 0; i < _dof_node * spatial_dim; ++i) {
            F[i] *= jac;
        }
        for (size_t i = 0; i < _dof_node * spatial_dim * _dof_node; ++i) {
            dF[i] *= jac;
        }

        std::vector<real> cap(_dof_node);
        std::vector<real> dprimal(_dof_node);
        for (int j = 0; j < _dof_node; j++){
            cap[j] = jac * _material.capacity(_internals->local(e, q), j);
            dprimal[j] = concentration[j] - concentration0[j];
        }
        if(update){
            //copy updated internals into the memory of internal variables
            std::copy(internal_new.begin(), internal_new.end(), internal);
        }
        // assemble quadrature point contribution to local residual
        _residualIntegrand(e, q, nen, spatial_dim, P_new, C, dPdu, F, f, rl, cap, dprimal, dt);
    }  // end loop over quad points
    return;
}

void summit::ReactionDiffusionRegion::Residual(NodalField<real> const& concentration,
                                              NodalField<real> const& concentration0,
                                              real dt,
                                              bool update,
                                              CommunicationManager const& commManager,
                                              NodalField<real>& residual)
{
    // get the number of nodes per element
    size_t nen = _element_set->nodes_element();
    // get the spatial dimension
    size_t spatial_dim = _element_set->dim();
    // compute size for local residual
    //    u.dim()=_dof_node is the number of degrees of freedom per node
    //    in this case u.dim() is 1 because temperature is a scalar
    int residual_dim = nen * _dof_node;

    // allocate memory for local residual
    static std::vector<real> rl;
    rl.resize(residual_dim);
    // allocate memory for local temperature
    static std::vector<real> concentrationl;
    concentrationl.resize(residual_dim);
    static std::vector<real> concentration0l;
    concentration0l.resize(residual_dim);
    // allocate memory for local nodal coordinates
    static std::vector<real> xl;
    xl.resize(nen * spatial_dim);

    // loop over the elements of the element set
    for (elem_t e(0); e < _element_set->elements(); ++e) {
        // reset residual
        std::fill(rl.begin(), rl.end(), 0.0);

        // extract local part of unknown
        _element_set->Localize(concentration, e, concentrationl);
        _element_set->Localize(concentration0, e, concentration0l);

        // get the coordinates of the nodes of the current element
        _element_set->Localize(_coordinates, e, xl);

        // assembly local residual
        this->ElementaryResidual(e, concentrationl, concentration0l, xl, dt, update, rl);

        // assemble local residual into global array
        _element_set->Assemble(rl, e, residual);

    }  // end loop over elements

    // end of method
    return;
}

void summit::ReactionDiffusionRegion::PrepareResidual(NodalField<real> const& T,
                                                     NodalField<real> const& T0,
                                                     real dt,
                                                     bool update,
                                                     ElementQuadratureField<real>* P_new)
{
    // say something useful...
    std::cout << "in ReactionDiffusionRegion class: PrepareResidual method \n"
              << "=> It has not been implemented yet " << std::endl;
    // ... and die!
    exit(1);

    // end of method
    return;
}

void summit::ReactionDiffusionRegion::ComputeResidual(const NodalField<real>& T,
                                                     ElementQuadratureField<real>* P_new,
                                                     NodalField<real>& residual,
                                                     real dt,
                                                     bool update)
{
    // say something useful...
    std::cout << "in ReactionDiffusionRegion class: ComputeResidual method \n"
              << "=> It has not been implemented yet " << std::endl;
    // ... and die!
    exit(1);

    // end of method
    return;
}

void summit::ReactionDiffusionRegion::ComputeStiffness(NodalField<real> const& T,
                                                      NodalField<real> const& T0,
                                                      real dt,
                                                      Stiffness& stiffness,
                                                      NodalField<real>& residual)
{
    // get the number of nodes per element
    size_t nen = _element_set->nodes_element();

    // get the spatial dimension from the element set
    size_t spatial_dim = _element_set->dim();

    // get the size of the local residual
    int residual_dim = nen * _dof_node;

    // get the size of the internal variable array stored at each Gauss point
    size_t nInternals = _internals->dim();

    // allocate memory for the element residual and
    // the local displacement (previous and current)
    std::vector<real> Tl(residual_dim);
    std::vector<real> T0l(residual_dim);
    std::vector<real> rl(residual_dim);

    // allocate element stiffness
    std::vector<real> kl(residual_dim * residual_dim);

    // allocate memory for local temperature gradients
    std::vector<real> Du(residual_dim * spatial_dim);

    // allocate vector of local fluxes
    std::vector<real> P_new(_dof_node * spatial_dim);

    // allocate local internal variable array
    std::vector<real> internal_new(nInternals);

    // create a new vector for derivative of fluxes with respect to gradient in concentration
    std::vector<real> C(_dof_node * spatial_dim * _dof_node * spatial_dim);
    std::vector<real> F(_dof_node * spatial_dim);
    std::vector<real> dF(_dof_node * spatial_dim * _dof_node);    

    // derivative of flux with the primal
    std::vector<real> dPdu(_dof_node * spatial_dim * _dof_node);
    // allocate for the coordinates of the nodes of an element
    std::vector<real> xl(nen * spatial_dim);

    std::vector<real> Dconcentration(_dof_node * spatial_dim);
    std::vector<real> Dconcentration0(_dof_node * spatial_dim);
    std::vector<real> concentration(_dof_node);
    std::vector<real> concentration0(_dof_node);
    for (elem_t e(0); e < _element_set->elements(); ++e) {
        // reset residual and stiffness
        std::fill(rl.begin(), rl.end(), 0.0);
        std::fill(kl.begin(), kl.end(), 0.0);

        // extract local part of unknown
        _element_set->Localize(T, e, Tl);
        _element_set->Localize(T0, e, T0l);

        // get the coordinates of the nodes of the current element, this is used in the body
        // forces, and therefore should be in the reference configuration
        _element_set->Localize(_coordinates, e, xl);


        this->ElementaryResidual(e, Tl, T0l, xl, dt, false, rl);

        // assemble local residual into global array
        _element_set->Assemble(rl, e, residual);


        // integrate over quadrature points


        for (quad_t q(0); q < _element_set->nquad(); ++q) {
            
            std::fill(C.begin(), C.end(), 0.0);
            std::fill(dPdu.begin(), dPdu.end(), 0.0);
            std::fill(F.begin(), F.end(), 0.0);
            std::fill(dF.begin(), dF.end(), 0.0);
            // THIS IS ALL MY STUFF FROM THE ELEMENTARY RESIDUAL


            std::fill(concentration.begin(), concentration.end(), 0.);
            std::fill(Dconcentration.begin(), Dconcentration.end(), 0.);
            std::fill(concentration0.begin(), concentration0.end(), 0.);
            std::fill(Dconcentration0.begin(), Dconcentration0.end(), 0.);
            //   loop over the nodes of the elements
            for (int c = 0; c < _dof_node; c++) {
                for (lnode_t b(0); b < nen; ++b) {
                    // add contribution of the node b
                    concentration[c] += _element_set->shape(e, q, b) * Tl[c + b * _dof_node];
                    concentration0[c] += _element_set->shape(e, q, b) * T0l[c + b * _dof_node];
                    // loop over the spatial dimension
                }
            }

            _element_set->Gradient(e, q, &T0l[0], _dof_node, &Dconcentration0[0]);
            _element_set->Gradient(e, q, &Tl[0], _dof_node, &Dconcentration[0]);
            // local value of internal variables at previous time step
            real* internal = _internals->local(e, q);
            // make local copy of internal variables at this quadrature point
            std::copy(internal, internal + nInternals, internal_new.begin());

            _material.Constitutive(&concentration0[0], &concentration[0], &Dconcentration0[0], &Dconcentration[0], &P_new[0], &internal_new[0],
                               C.data(), dPdu.data(), dt, _dof_node, spatial_dim, true);

            
            // jacobians (with quadrature weights)
            real jac = _element_set->jac(e, q);
            for (size_t i = 0; i < _dof_node * spatial_dim; ++i) {
                P_new[i] *= jac;
            }
            for (int i = 0; i < _dof_node * spatial_dim * _dof_node * spatial_dim; ++i) {
                C[i] *= jac;
            }
            for (int i = 0; i < _dof_node * spatial_dim * _dof_node; ++i) {
                dPdu[i] *= jac;
                
            }
            std::vector<real> f(_dof_node);
            std::fill(f.begin(), f.end(), 0.);
            std::vector<real> df(_dof_node * _dof_node);
            std::fill(df.begin(), df.end(), 0.);
            std::vector<real> dfdGrad(_dof_node * _dof_node * spatial_dim);
            std::fill(dfdGrad.begin(), dfdGrad.end(), 0.);
            _material.Source(&concentration0[0], &concentration[0], &Dconcentration0[0], &Dconcentration[0], &internal_new[0], &dt, &f[0], &df[0], &dfdGrad[0], _element_set->dim(), _dof_node);
            for (size_t i = 0; i < _dof_node; ++i) {
                f[i] *= jac;
            }
            for (size_t i = 0; i < _dof_node * _dof_node; ++i) {
                df[i] *= jac;
            }

            _material.ConvectiveFlux(&concentration0[0], &concentration[0], &internal_new[0],
                               &dt, F.data(), dF.data(), _dof_node, spatial_dim);

            for (size_t i = 0; i < _dof_node * spatial_dim; ++i) {
                F[i] *= jac;
            }
            for (size_t i = 0; i < _dof_node * spatial_dim * _dof_node; ++i) {
                dF[i] *= jac;
            }

            std::vector<real> cap(_dof_node);
            std::vector<real> dprimal(_dof_node);
            for (int j = 0; j < _dof_node; j++){
                cap[j] = jac * _material.capacity(_internals->local(e, q), j);
                dprimal[j] = concentration[j] - concentration0[j];
            }
            _stiffnessIntegrand(e, q, P_new, C, dPdu, F, dF, f, df, dfdGrad, kl, cap, dt);

        }
        stiffness.AddElement(_element_set->dof_map()->Connectivity(e), &kl[0], nen, _dof_node,
                             residual);
    }
    return;
}


void summit::ReactionDiffusionRegion::_stiffnessIntegrand(elem_t e,
                                                  quad_t q,
                                                  std::vector<real> const& P,
                                                  std::vector<real> const& C,
                                                  std::vector<real> const& dPdu,
                                                  std::vector<real> const& F,
                                                  std::vector<real> const& dF,
                                                  std::vector<real>& sources,
                                                  std::vector<real>& dsources,
                                                  std::vector<real>& dfdGrad,                                                  
                                                  std::vector<real>& kl,
                                                  std::vector<real>& cap,
                                                  real dt) const
{
    // get the number of nodes per element
    size_t nen = _element_set->nodes_element();

    // get the spatial dimension from the element set
    size_t spatial_dim = _element_set->dim();

    // get the strain dimension
    int strain_dim = spatial_dim * _dof_node;

    // get the size of the local residual
    int residual_dim = nen * _dof_node;
    // derivative of fluxes with concentration gradients
    // loop over the nodes of the element
    for (lnode_t a(0); a < nen; ++a) {
        for (size_t i = 0; i < _dof_node; ++i) {
            for (lnode_t b(0); b < nen; ++b) {
                for (size_t k = 0; k < _dof_node; ++k) {
                    for (dim_t j(0); j < spatial_dim; ++j) {
                        for (dim_t l(0); l < spatial_dim; ++l) {
                            kl[(a * _dof_node  + i) * residual_dim + b * _dof_node + k] += C[(i * spatial_dim + j) * strain_dim + k * spatial_dim  + l] *
                                                           _element_set->dShape(e, q, a, j) *
                                                           _element_set->dShape(e, q, b, l);
                        }
                        kl[(a * _dof_node + i) * residual_dim + _dof_node * b + k] += dPdu[(i * spatial_dim + j) * _dof_node + k] *
                                                           _element_set->dShape(e, q, a, j) *
                                                           _element_set->shape(e, q, b);
                        kl[(a * _dof_node + i) * residual_dim + _dof_node * b + k] += dF[(i * spatial_dim + j) * _dof_node + k] *
                                                           _element_set->dShape(e, q, a, j) *
                                                           _element_set->shape(e, q, b);
                    }
                    //std::cout << "dsources[i * _dof_node  + k]: " << dsources[i * _dof_node  + k] << std::endl;
                    kl[(a * _dof_node + i) * residual_dim + _dof_node * b + k] += (-dsources[i * _dof_node  + k]) *
                                                           _element_set->shape(e, q, a) *
                                                           _element_set->shape(e, q, b);
                }
                kl[(a * _dof_node + i) * residual_dim + _dof_node * b + i] += (+ cap[i] / dt) *
                                                           _element_set->shape(e, q, a) *
                                                           _element_set->shape(e, q, b);
            }
        }
    }
    return;
}

void summit::ReactionDiffusionRegion::Update(NodalField<real> const& T,
                                            NodalField<real> const& T0,
                                            real dt)
{
    // get the number of nodes per element
    size_t nen = _element_set->nodes_element();
    // get the spatial dimension
    size_t spatial_dim = _element_set->dim();
    // compute size for local residual
    //    u.dim()=_dof_node is the number of degrees of freedom per node
    //    in this case u.dim() is 1 because temperature is a scalar
    int residual_dim = nen * _dof_node;
    // allocate memory for local residual
    static std::vector<real> rl;
    rl.resize(residual_dim);
    // allocate memory for local temperature
    static std::vector<real> concentrationl;
    concentrationl.resize(residual_dim);
    static std::vector<real> concentration0l;
    concentration0l.resize(residual_dim);
    // allocate memory for local nodal coordinates
    static std::vector<real> xl;
    xl.resize(nen * spatial_dim);
    bool update = true;
    // loop over the elements of the element set
    for (elem_t e(0); e < _element_set->elements(); ++e) {
        // reset residual
        std::fill(rl.begin(), rl.end(), 0.0);
        // extract local part of unknown
        _element_set->Localize(T, e, concentrationl);
        _element_set->Localize(T0, e, concentration0l);
        // get the coordinates of the nodes of the current element
        _element_set->Localize(_coordinates, e, xl);
        this->ElementaryResidual(e, concentrationl, concentration0l, xl, dt, true, rl);
    }  // end loop over elements

    // end of method
    return;
}

summit::Region::IVsheet_t summit::ReactionDiffusionRegion::getInternalVariableFields(
  std::string const& name) const
{
    // position of the property in the internal variable container
    int position;
    // size of material property: scalar, vector, tensor
    size_t size;
    // ask the material if he can process the current material property from the internal
    // variable and set the size which is passed by reference.
    bool gotIt = _material.GetLocationInInternalTable(name, position, size);

    // all done
    return std::make_tuple(gotIt, position, size, _internals);
}

summit::real summit::ReactionDiffusionRegion::StableTimeStep(NodalField<real> const& coordinates,
                                                            NodalField<real> const& T)
{
    // get the number of nodes per element
    size_t nen = _element_set->nodes_element();
    //  get the number of quadrature points per element
    size_t nquad = _element_set->nquad();
    // get the spatial dimension from the element set
    // this should really be 3
    size_t spatial_dim = _element_set->dim();

    // allocate for the coordinates of element nodes
    std::vector<real> xl(nen * spatial_dim);

    // set the minimum time step to a huge value
    real min_dt = SUMMIT_REAL_MAX;

    for (elem_t e(0); e < _element_set->elements(); ++e) {
        // get the coordinates of the nodes of the current element
        _element_set->Localize(coordinates, e, xl);

        // determine relevant element length
        real L = _element_set->InRadiusElement(&xl[0]);

        // loop over quadrature points
        for (quad_t q(0); q < nquad; ++q) {
            // local value of internal variables at previous time step
            real* internal = _internals->local(e, q);
            real d;
            real maxd;
            // compute the maximum diffusivity in [ Meter^2 / Second ]
            // loop over the different concentrations
            for (int c=0;c< _dof_node;c++){
                d = _material.diffusivity(NULL, internal, spatial_dim, c);
                maxd = MathUtil<real>::max(maxd, d);
            }

            // compute the CFL critical time-step
            real dt = (maxd > 0.) ? L * L / maxd : SUMMIT_REAL_MAX;

            // keep it if it is the smallest one
            min_dt = MathUtil<real>::min(min_dt, dt);
        }
    }

    // end of method return the min element stable time-step
    return min_dt;
}

void summit::ReactionDiffusionRegion::AssembleMass(NodalField<real>& mass) const
{
    // number of quadrature points per element
    size_t nquad = _element_set->nquad();
    // number of nodes per element
    size_t nen = _element_set->nodes_element();
    // dimension of the mass nodal field
    const int ndim = mass.dim();

    // container for the elementary mass
    std::vector<real> elem_mass(nen * ndim, 0.);
    std::vector<real> elem_lump_mass_weight_by_nodes(nen, 0.);

    // loop over the elements constituting the region
    size_t nElements = _element_set->elements();
    for (elem_t e(0); e < nElements; ++e) {
        for (int j = 0; j < ndim; ++j) {   // loop over the components of the diffusion problem
            real tot_mass;
            tot_mass = 0;
            for (quad_t q(0); q < nquad; ++q) {
                // jacobians (with quadrature weights)
                real jac = _element_set->jac(e, q);
                tot_mass += jac * _material.capacity(_internals->local(e, q), j);
            }
            _element_set->LumpMass(tot_mass, elem_lump_mass_weight_by_nodes);
            real* massNode = &elem_lump_mass_weight_by_nodes[0];
            //real* elemMass = &elem_mass[0 + j * nen];
            real* elemMass = &elem_mass[j];
            for (size_t i(0); i < nen; ++i, ++massNode) {
                *elemMass = *massNode;
                elemMass += ndim;
            }
        }
        _element_set->Assemble(elem_mass, e, mass);
    }

    // all done
    return;
}

void summit::ReactionDiffusionRegion::Interpolate(const NodalField<real>& nodal_field,
                                                 ElementQuadratureField<real>& quad_field) const
{
    // say something useful...
    std::cout << "in ReactionDiffusionRegion class: Interpolate method \n"
              << "=> It has not been implemented yet " << std::endl;
    // ... and die!
    exit(1);

    // all done
    return;
}

// bool summit::ReactionDiffusionRegion::GetNodalField(std::string const& name,
//                                                    int numNode,
//                                                    NodalField<real>& weights,
//                                                    NodalField<real>& nodal_field) const
// {
//     int position;
//     size_t size;
//     bool didItWork = material()->GetLocationInInternalTable(name, position, size);
//     // if the material model has 'name' as internal variable
//     if (didItWork) {
//         // then, copy the values from internals
//         ElementQuadratureField<real> field(_element_set->elements(), _element_set->nquad(), size);
//         // loop over the elements
//         for (elem_t e(0); e < _element_set->elements(); ++e) {
//             // loop over the quadrature points
//             for (quad_t q(0); q < _element_set->nquad(); ++q) {
//                 // local value of internal variables
//                 real* internal = _internals->local(e, q);
//                 real* field_local = field.local(e, q);
//                 // loop over the size
//                 for (size_t j = 0; j < size; ++j) {
//                     field_local[j] = internal[position + j];
//                 }
//             }
//         }
//         // resize nodal field to the dimension of the internal variable times the number of mesh
//         // nodes
//         nodal_field.resize(size, numNode);
//         // extrapolate the quadrature field to the mesh nodes
//         _element_set->Extrapolate(field, nodal_field, weights);
//         return true;
//     }
//     // all done
//     return false;
// }

bool summit::ReactionDiffusionRegion::GetNodalField(std::string const& name,
                                            int numNode,
                                            NodalField<real>& weights,
                                            NodalField<real>& nodal_field) const
{
    // see if 'name' is an internal variable
    int position;
    size_t size;
    bool didItWork = material()->GetLocationInInternalTable(name, position, size);
    // if the material model has 'name' as internal variable
    if (didItWork) {
        // then, copy the values from internals
        ElementQuadratureField<real> field(_element_set->elements(), _element_set->nquad(), size);
        // loop over the elements
        for (elem_t e(0); e < _element_set->elements(); ++e) {
            // loop over the quadrature points
            for (quad_t q(0); q < _element_set->nquad(); ++q) {
                // local value of internal variables
                real* internal = _internals->local(e, q);
                real* field_local = field.local(e, q);
                // loop over the size
                for (size_t j = 0; j < size; ++j) {
                    field_local[j] = internal[position + j];
                }
            }
        }

        // resize nodal field to the dimension of the internal variable times the number of mesh
        // nodes
        nodal_field.resize(size, numNode);

        // extrapolate the quadrature field to the mesh nodes
        _element_set->Extrapolate(field, nodal_field, weights);
        return true;
    } else {
        // grab the current dimension
        summit::ElementQuadratureField<real> const* field = this->GetField(name);
        if (field == NULL) {
            return false;
            // std::cout << "The field you try to get does not exist..." << std::endl;
            // exit(1);
        }
        int maxDim = field->dim();

        // temporary fix:
        // Here we should check whether we are in plane strain and there only to grab the
        // out-of-plane stress
        // To be done later...
        if (name.compare(0, 6, "stress") == 0 && maxDim == 4) {
            // make sure that dim is 5 to account for sig_33
            maxDim += 1;
        }

        // try to see if there is an actual ElementQuadratureField with the name
        fieldMapConstIt_t it = _element_quadrature_fields.find(name);

        // if the field is found
        if (it != _element_quadrature_fields.end()) {
            // resize the nodal field if needed
            if (nodal_field.dim() != (int)(maxDim)) {
                nodal_field.resize(maxDim, numNode);
            }
            // check wether the member element set is of type INTERFACE
            if (_element_set->type() != INTERFACE) {
                
                _element_set->Extrapolate(*(it->second), nodal_field, weights);

            }
        } else {
            return false;
        }
    }
    // end of method
    return true;
}

void summit::ReactionDiffusionRegion::GetGlobalIdByElement(std::vector<int>& globalId) const
{
    // all done
    return _element_set->globalId(globalId);
}

void summit::ReactionDiffusionRegion::SetMaterialParameters(PropertySheet_t const& sheet)
{
    // get the number of integration points
    size_t nquad = _element_set->nquad();
    // loop over the PropertySheet_t
    for (PropertySheet_t::const_iterator pIt = sheet.begin(); pIt != sheet.end(); ++pIt) {
        // position of the property in the internal variable container
        int position;
        // size of material property: scalar, vector, tensor
        size_t size;
        // ask the material if he can process the current material property from the internal
        // variable
        bool gotIt = _material.GetLocationInInternalTable(pIt->first, position, size);
        // if it does
        if (gotIt) {
            // Here, without knowledge of all the element across all the partitions,
            // it is impossible to do size checking
            // then, build the vector of global element index
            std::vector<int> globId;
            _element_set->globalId(globId);
            // container for the property of the current element
            std::vector<real> elmProp(size);
            // loop over the elements in the element set
            for (elem_t e(0); e < _element_set->elements(); ++e) {
                // fill the container with the values in the property sheet
                for (size_t i = 0; i < size; ++i) {
                    // fill each component of the current property with the value corresponding to
                    // the global element id in the property sheet
                    elmProp[i] = pIt->second[globId[e] * size + i];
                }
                // loop over quadrature points
                for (quad_t q(0); q < nquad; ++q) {
                    // grab the local value of internal variables at the current quadratue point
                    real* internal = _internals->local(e, q);
                    // transfer the material property to the current internal variable
                    for (size_t i = 0; i < size; ++i) {
                        // transfer component by component
                        internal[position + i] = elmProp[i];
                    }
                }
            }
        }
    }

    // all done
    return;
}

summit::ElementQuadratureField<summit::real>*
summit::ReactionDiffusionRegion::CloneVariablesForResidual() const
{
    // all done
    return NULL;
}

summit::ElementQuadratureField<summit::real>* summit::ReactionDiffusionRegion::VariablesForResidual()
{
    // all done
    return NULL;
}

void summit::ReactionDiffusionRegion::ComputeAccelerationWithBlockMassMatrix(
  NodalField<real> const& residual,
  NodalField<int> const& boundaryConditionsTypes,
  NodalField<real>& acceleration,
  const CommunicationManager& commManager,
  const real gammaDt)
{
    // say something useful...
    std::cout << "in ReactionDiffusionRegion class: ComputeAccelerationWithBlockMassMatrix method \n"
              << "=> It has not been implemented yet " << std::endl;
    // ... and die!
    exit(1);

    // all done
    return;
}

void summit::ReactionDiffusionRegion::ApplyMassMatrixInverse(
  NodalField<real> const& residual,
  NodalField<real>& acceleration)
{
    // ~> create variables to hold frequently used element set related quantities
    // get the number of nodes per element
    size_t nen = _element_set->nodes_element();
    // get the number of spatial dimensions of the problem
    size_t nsd = _element_set->dim();
    // get the number of quadrature points per element
    size_t nq = _element_set->nquad();
    size_t p_dim = residual.dim();
    // compute the number of element unknowns
    size_t neu = nen * p_dim;

    // ~> allocate memory for element arrays involved in solving the eq.(9.1.12) from Hughes2000 in
    //    an element-by-element way that takes advantage of DGFEM block diagonal global mass matrix
    // array to hold element nodes accelerations
    static std::vector<real> ae;
    ae.resize(neu);
    // array to hold element nodes residuals
    static std::vector<real> re;
    re.resize(neu);
    // array to hold the element nodes accelerations
    static std::vector<real> ae_sd;
    ae_sd.resize(nen);
    // array to hold the element nodes residuals
    static std::vector<real> re_sd;
    re_sd.resize(nen);
    // array to hold the elements of the inverse of the element mass matrix in element mass matrix
    // compressed storage form
    static std::vector<real> LHSe_inv;
    LHSe_inv.resize(nen * nen);
    // ~> set up the factor to correct the "volume" of the element mass matrix
    // - the coordinates of the reference simplex are in the range [-1,1], and then the mass of the
    // mass matrix is not 1
    // - the jacobian used to multiply the mass matrix of the reference system is also computed
    // considering a range [-1,1], it means that there is a "volumetric factor" that appears two
    // times, one in the calculation of the jacobian and another in the computation of the mass
    // matrix
    // - as consequence, the resultant dynamic is slower that the real dynamics, and thus a
    // correction factor is needed to consider only once the "volumetric factor"
    real correctionFactor;
    if (nsd == 2) {
        correctionFactor = 2.0;
    }
    else if (nsd == 3) {
        correctionFactor = 4.0 / 3.0;
    }
    else {
        pyre::journal::firewall_t error("summit.weakforms.reaction_diffusion_region");
        error << pyre::journal::at(__HERE__)
              << "Error in summit::ReactionDiffusionRegion::ApplyMassMatrixInverse()"
              << " --> this method has not been implemented yet for a problem with " << nsd
              << " spatial dimensions" << pyre::journal::endl;
    }

    // ~> loop over the elements
    for (elem_t e(0); e < _element_set->elements(); ++e) {
        // ~> get element arrays from global ones
        _element_set->Localize(residual, e, re);

        // ~> compute element mass matrix
        real density = 1.0;// _material.density(_internals->local(e, quad_t(0)));
        // compute element mass
        real elementVolume = 0.0;
        for (quad_t q(0); q < nq; ++q) {
            elementVolume += _element_set->jac(e, q);
        }
        real elementMass = elementVolume * density / correctionFactor;
        // get the mass matrix of the reference element
        std::vector<real> LHSe_inv = _element_set->inverseBlockMassMatrixReferenceElement();
        // adjust the reference element mass matrix to the element in question
        for (size_t i = 0; i < LHSe_inv.size(); ++i) {
            LHSe_inv[i] = LHSe_inv[i] / elementMass;
        }

        // ~> solve the system of linear equations LHSe*ae=re
        // loop over spatial directions
        for (size_t sd = 0; sd < p_dim; ++sd) {
            // get element nodes accelerations and residuals corresponding to a definite cartesian
            // direction
            for (size_t row = 0; row < nen; ++row) {
                re_sd[row] = re[p_dim * row + sd];
            }

            // compute the element nodes accelerations corresponding to this spatial direction
            for (size_t row = 0; row < nen; ++row) {
                ae_sd[row] = 0.0;
                for (size_t col = 0; col < nen; ++col) {
                    ae_sd[row] += LHSe_inv[row * nen + col] * re_sd[col];
                }
            }

            // copy the element nodes accelerations corresponding to this spatial direction into the
            // element acceleration array
            for (size_t row = 0; row < nen; ++row) {
                ae[p_dim * row + sd] = ae_sd[row];
            }
        }  // end of solve the system of linear equations LHSe*ae=re
        // ~> assemble the element acceleration array into the global one
        _element_set->Assemble(ae, e, acceleration);
    }  // end loop over the elements

    // end of method
    return;
}

summit::ElementQuadratureField<summit::real> const* summit::ReactionDiffusionRegion::internals()
  const
{
    // all done
    return _internals;
}

void summit::ReactionDiffusionRegion::InterpolateAndSetPrimalVariableAsInternalVariable(
  NodalField<real> const& T)
{
    // say something useful...
    std::cout << "in ReactionDiffusionRegion class: "
                 "InterpolateAndSetPrimalVariableAsInternalVariable method \n"
              << "=> It has not been implemented yet " << std::endl;
    // ... and die!
    exit(1);

    // all done
    return;
}

summit::Material const* summit::ReactionDiffusionRegion::material() const { return &_material; }

void summit::ReactionDiffusionRegion::_residualIntegrand(elem_t e,
                                                        quad_t q,
                                                        size_t nen,
                                                        size_t spatial_dim,
                                                        std::vector<real> const& P,
                                                        std::vector<real> const& C,
                                                        std::vector<real> const& dPdu,
                                                        std::vector<real> const& F,
                                                        std::vector<real> f,
                                                        std::vector<real>& rl,
                                                        std::vector<real>& cap,
                                                        std::vector<real>& dprimal,
                                                        real dt) const
{
    // loop over the nodes of the element
    for (int c = 0; c < _dof_node; c++){
        for (lnode_t a(0); a < nen; ++a) {
            // loop over the parametric dimension
            // add contribution of P (-f^int)
            for (dim_t beta(0); beta < spatial_dim; ++beta) {
                rl[c + a * _dof_node] -= P[c* spatial_dim + beta] * _element_set->dShape(e, q, a, beta);
                //Contribution from the convective flux
                rl[c + a * _dof_node] -= F[c* spatial_dim + beta] * _element_set->dShape(e, q, a, beta);
            }
            //uncomment out this line to do consistent mass matrix and implicit time integration!
            //or comment out this line to do lumped mass and explicit time integration!
            rl[c + a * _dof_node] += (f[c] - cap[c] * dprimal[c] / dt) * _element_set->shape(e, q, a);
        }
    }

    // all done
    return;
}

summit::ElementQuadratureField<real>* summit::ReactionDiffusionRegion::internalVariables()
{
    // all done
    return _internals;
}

// end of file
