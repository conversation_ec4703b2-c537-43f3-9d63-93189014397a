/**
 * @file mechanics_interface_region_external_boundary.h
 * @brief External boundary interface region for solid mechanics systems using discontinuous Galerkin methods
 * <AUTHOR> Development Team
 * @date 2025
 *
 * This file contains the MechanicsInterfaceRegionExternalBoundary class which implements
 * external boundary conditions for solid mechanics systems using discontinuous Galerkin (DG)
 * finite element methods. External boundaries represent the interfaces between the computational
 * domain and the external environment, where various types of boundary conditions must be enforced
 * to properly model the mechanical system.
 *
 * The implementation provides a comprehensive framework for handling all major types of boundary
 * conditions encountered in solid mechanics systems, including displacement, traction, and coupled
 * multi-physics boundary conditions. The class leverages the DG framework's natural ability to
 * handle discontinuities and provides robust, accurate, and efficient boundary condition enforcement.
 */

#ifndef SUMMIT_MECHANICS_INTERFACE_REGION_EXTERNAL_BOUNDARY_H
#define SUMMIT_MECHANICS_INTERFACE_REGION_EXTERNAL_BOUNDARY_H

#include "dirichlet_mechanics_region.h"

namespace summit {

/**
 * Forward declarations
 */
class Checkpoint;
class Material;

/**
 * @brief External boundary interface region for solid mechanics systems using discontinuous Galerkin methods
 *
 * The MechanicsInterfaceRegionExternalBoundary class provides a comprehensive implementation
 * for handling external boundary conditions in solid mechanics systems using discontinuous Galerkin
 * finite element methods. This class extends DirichletMechanicsRegion to specifically
 * handle domain boundaries where the computational domain interfaces with the external environment.
 *
 * ## Theoretical Foundation
 *
 * ### Discontinuous Galerkin Boundary Treatment
 * In discontinuous Galerkin methods, boundary conditions are naturally enforced through numerical
 * fluxes at the domain boundaries. Unlike continuous Galerkin methods where essential boundary
 * conditions are enforced by directly modifying the degrees of freedom, DG methods treat all
 * boundary conditions as natural boundary conditions through the weak formulation.
 *
 * The weak form of the equilibrium equation:
 * ∇·σ + b = 0
 *
 * When integrated by parts becomes:
 * ∫_Ω σ:∇v dΩ - ∫_∂Ω t̂·v dΓ = ∫_Ω b·v dΩ
 *
 * Where t̂ is the numerical traction at the boundary, which encodes the boundary condition.
 *
 * ### Boundary Condition Types
 *
 * #### 1. Displacement Boundary Conditions (Essential)
 * **Mathematical Form**: u = g(x,t) on Γ_D
 * **Physical Interpretation**: Prescribed displacements
 * **Implementation**: The numerical traction is computed using penalty methods:
 * t̂ = σ(u_interior)·n + τ(u_interior - g)
 * where τ is a penalty parameter ensuring stability and consistency.
 *
 * #### 2. Traction Boundary Conditions (Natural)
 * **Mathematical Form**: σ·n = t(x,t) on Γ_N
 * **Physical Interpretation**: Prescribed surface tractions
 * **Implementation**: The numerical traction directly incorporates the prescribed traction:
 * t̂ = t(x,t)
 *
 * #### 3. Robin Boundary Conditions (Mixed)
 * **Mathematical Form**: α u + β σ·n = γ(x,t) on Γ_R
 * **Physical Interpretation**: Linear combination of displacement and traction
 * **Implementation**: The numerical traction satisfies the Robin condition
 *
 * ## Key Features
 * - Weak enforcement of displacement boundary conditions using penalty methods
 * - Natural enforcement of traction boundary conditions
 * - Support for time-dependent boundary conditions
 * - Compatible with nonlinear material models
 * - Efficient parallel implementation
 * - Restart and checkpointing capabilities
 *
 * @note This class is specifically designed for external boundaries only
 * @note Internal interfaces should use DirichletMechanicsRegion
 * @note Supports both 2D and 3D boundary conditions
 * @note Compatible with adaptive mesh refinement
 *
 * @warning Penalty parameters must be chosen carefully for stability
 * @warning Large penalty parameters can cause ill-conditioning
 * @warning Parallel efficiency depends on boundary element distribution
 *
 * @see DirichletMechanicsRegion for base interface functionality
 * @see InterfaceDG for interface material definitions
 * @see MechanicalMaterial for bulk material properties
 */
class MechanicsInterfaceRegionExternalBoundary : public DirichletMechanicsRegion {
  public:
    /**
     * Typedefs
     */
    typedef ElementSet::field_t field_t;

    /**
     * @brief Primary constructor for external boundary interface region
     *
     * This constructor creates a MechanicsInterfaceRegionExternalBoundary object for handling
     * external boundary conditions in solid mechanics systems using discontinuous Galerkin methods.
     *
     * @param[in] es Pointer to one-sided interface element set defining the boundary geometry
     * @param[in] dof_node Number of degrees of freedom per node (typically 2 for 2D, 3 for 3D)
     * @param[in] material Interface material object (InterfaceDG type) that defines the boundary
     *                     condition type and numerical flux computation
     * @param[in] material_L Interior mechanical material representing the bulk material
     *                       properties adjacent to the boundary
     * @param[in] coordinates Nodal coordinate field providing the geometric information
     * @param[in] usingFullStabilizationTerm Optional flag controlling stabilization terms
     */
    MechanicsInterfaceRegionExternalBoundary(ElementSetInterfaceOneSided const* es,
                             int dof_node,
                             InterfaceDG const& material,
                             MechanicalMaterial const& material_L,
                             NodalField<real> const& coordinates,
                             bool usingFullStabilizationTerm = false);

    /**
     * @brief Restart constructor for external boundary interface region
     *
     * @param[in] checkpoint Pointer to a valid Checkpoint object containing the saved state
     * @param[in] name Character string specifying the name of the data group within the checkpoint
     */
    MechanicsInterfaceRegionExternalBoundary(Checkpoint* checkpoint, const char* name);

    /**
     * @brief Destructor for external boundary interface region
     */
    virtual ~MechanicsInterfaceRegionExternalBoundary();

  private:
    /**
     * Default constructor
     */
    MechanicsInterfaceRegionExternalBoundary();

    /**
     * Copy constructor
     * @param[in] a const reference to a ReactionDiffusionInterfaceRegionExternalBoundary
     */
    MechanicsInterfaceRegionExternalBoundary(MechanicsInterfaceRegionExternalBoundary const&);

    /**
     * Assignment operator
     * @param[in] a const reference to a ReactionDiffusionInterfaceRegionExternalBoundary
     * @return a reference to a ReactionDiffusionInterfaceRegionExternalBoundary
     */
    MechanicsInterfaceRegionExternalBoundary& operator=(MechanicsInterfaceRegionExternalBoundary const&);

  public:

    /**
     * Method to write for restart
     * @param[in] checkpoint object associated with restart
     * @param[in] name the name of the group within the checkpoint object
     * @param[in] tag the tag
     */
    virtual void WriteForRestart(Checkpoint* checkpoint,
                                 const char* name,
                                 const char* tag = nullptr) const;

    /**
     * Method to update the region
     * @param[in] T, a nodal field of the primal variable
     * @param[in] T0, a nodal field of the primal variable at the previous time-step
     * @param[in] dt, a time-step
     */
    void Update(NodalField<real> const& u, NodalField<real> const& u0, real dt);
    
    /**
     * Method to compute the residual over the region
     * @param[in] u, a nodal field of the primal variable
     * @param[in] u, a nodal field of the primal variable at the previous time-step
     * @param[in] dt, a time-step
     * @param[in] update, whether to update the internal variables, stresses and strains
     * @param[in] commManager a CommunicationManager
     * @param[in,out] residual, a nodal field filled in within the method
     */
    void Residual(NodalField<real> const& u,
                          NodalField<real> const& u0,
                          real dt,
                          bool update,
                          CommunicationManager const& commManager,
                          NodalField<real>& residual);

    /**
     * Method to compute the stiffness matrix and the residual over the region
     * @param[in] u, a nodal field of the primal variable
     * @param[in] u0, a nodal field of the primal variable at the previous time-step
     * @param[in] dt, a time-step
     * @param[in,out] stiffness, a matric filled in within the method
     * @param[in,out] residual, a nodal field filled in within the method
     */
    virtual void ComputeStiffness(NodalField<real> const& u,
                                  NodalField<real> const& u0,
                                  real dt,
                                  Stiffness& stiffness,
                                  NodalField<real>& residual);



  protected:

    /**
     * Helper methods for boundary condition implementation
     * These would be implemented based on specific boundary condition requirements
     */

    /**
     * Method to compute the elementary stiffness matrix and residual vector
     */
    void ElementStiffness(elem_t e,
                             real dt,
                             bool DoUpdate,
                             bool DoResidual,
                             bool DoStiffness,
                             std::vector<real> const& ul,
                             std::vector<real> const& u0l,
                             std::vector<real> const& xl,
                             std::vector<real>& rl,
                             std::vector<real>& Kl);

    /**
     * Method to compute the left first Piola-Kirchhoff stress tensor and the left tangent
     * operator tensor from the constitutive update of the left element
     * @param[in] e index of the element
     * @param[in] q index of the quadarature point
     * @param[in] ul local displacement vector
     * @param[in] dt time-step
     * @param[out] P_L_new left first Piola-Kirchoff stress tensor
     * @param[out] F_L_new strains energetically conjugated to P_L_new
     * @param[out] internal_L_new left vector of internal variables
     * @param[out] C_L left tangent operator
     */
    void LeftConstitutiveUpdate(elem_t e,
                                quad_t q,
                                std::vector<real> const& ul,
                                std::vector<real> const& u0l,
                                std::vector<real> const& xl,
                                real dt,
                                real measInterface,
                                std::vector<real>& P_L_new,
                                std::vector<real>& F_L_new,
                                std::vector<real>& internal_L_new,
                                std::vector<real>& internal_new,
                                real* C_L,
                                real* dPdU_L,
                                real* tractionDotN,
                                real* dTractiondU,
                                real* dTractiondGrad);

  private:
    static Register<Region, MechanicsInterfaceRegionExternalBoundary> reg;
};

}  // namespace summit

#endif  // SUMMIT_MECHANICS_INTERFACE_REGION_EXTERNAL_BOUNDARY_H
