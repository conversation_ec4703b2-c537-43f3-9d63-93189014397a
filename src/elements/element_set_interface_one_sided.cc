#include <iostream>
#include <cstdlib>
#include <cstdio>

#include "element_quadrature_field.h"
#include "../fem/nodal_field.h"
#include "../mathlib/mathvec.h"
#include "element_set_interface_one_sided.h"
#include "../weakforms/dirichlet_mechanics_region.h"
#include "../weakforms/reaction_diffusion_interface_region.h"
#include "../weakforms/reaction_diffusion_interface_region_external_boundary.h"
#include "../io/summit_message.h"

#include "../restart/checkpoint.h"

summit::ElementSetInterfaceOneSided::ElementSetInterfaceOneSided(
  size_t dim,
  size_t topo,
  size_t nElements,
  size_t nen,
  size_t nenLeft,
  size_t pOrder,
  int matLabel,
  int matLabelLeft,
  std::vector<int> const& globalElementId,
  short int bip,
  SIDE side)
  : ElementSetBody(dim, topo, nElements, nen, mat<PERSON>abel),
    _pO<PERSON><PERSON>(pOrder),
    _nen<PERSON><PERSON><PERSON>(nenLeft),
    _matLabelLeft(matLabelLeft),
    _globalElementId(globalElementId),
    _bip(bip),
    _side(side)
{
    // if the number of global element indices is different from the number of elements
    if (_nelements != globalElementId.size()) {
        // say something useful...
        std::cout << "Error in ElementSetInterfaceOneSided::ElementSetInterfaceOneSided :"
                  << " globalElementId.size() != nelements" << std::endl;
        // ... and die
        throw std::exception();
    }

// loop over the elements
#if 0
    std::cout << "------------------ Left Bulk material labels : " << matLabelLeft
              << " ------------------" << std::endl;
#endif
    for (size_t i = 0; i < _nelements; i++) {
        // fill the map with (globalIndex, localIndex)
        _globalToLocalElementMap.insert(std::make_pair(globalElementId[i], i));
#if 0
        std::cout << "global -> local : " << globalElementId[i] << " -> " << i << std::endl;
#endif
    }
#if 0
    std::cout << "-----------------------------------------"
              << "--------------------" << std::endl;
#endif

    // end of method
    return;
}

summit::ElementSetInterfaceOneSided::ElementSetInterfaceOneSided(Checkpoint* checkpoint,
                                                                 const char* name)
  : ElementSetBody(checkpoint, name)
{
    // Read pOrder
    DataSet* ds = checkpoint->OpenDataSet("pOrder");
    ds->read(&_pOrder);

    // Read number of quadrature points
    ds = checkpoint->OpenDataSet("nquad");
    ds->read(&_nquad);

    // Read number of nodes per element (left element)
    ds = checkpoint->OpenDataSet("nenLeft");
    ds->read(&_nenLeft);

    // Read left element material label
    ds = checkpoint->OpenDataSet("matLabelLeft");
    ds->read(&_matLabelLeft);

    // Read interface type
    ds = checkpoint->OpenDataSet("bip");
    ds->read(&_bip);

    // Read side of the element with respect to the interface
    ds = checkpoint->OpenDataSet("side");
    int sideInt;
    ds->read(&sideInt);
    _side = SIDE(sideInt);

    // Read subdivision connectivity
    ds = checkpoint->OpenDataSet("globalElementId");
    std::vector<int> dims = ds->dims();
    _globalElementId.resize(dims[0]);
    if (_globalElementId.size() > 0) {
        ds->read(_globalElementId.data());
    }
    return;
}

summit::ElementSetInterfaceOneSided::ElementSetInterfaceOneSided(const ElementSetInterfaceOneSided& elementSetInterfaceOneSided)
    : ElementSetBody(elementSetInterfaceOneSided), 
    _pOrder(elementSetInterfaceOneSided._pOrder),
    _nquad(elementSetInterfaceOneSided._nquad),
    _nenLeft(elementSetInterfaceOneSided._nenLeft),
    _matLabelLeft(elementSetInterfaceOneSided._matLabelLeft),
    _globalElementId(elementSetInterfaceOneSided._globalElementId),
    _bip(elementSetInterfaceOneSided._bip),
    _side(elementSetInterfaceOneSided._side)
{
    return;
}

summit::ElementSetInterfaceOneSided::~ElementSetInterfaceOneSided() {}

summit::Region* summit::ElementSetInterfaceOneSided::newRegion(
  const REGION region,
  MaterialLibrary const& material_lib,
  const NodalField<real>& coordinates,
  const Functor<real>* sourceTerm) const
{
    auto* interlaw = dynamic_cast_or_continue<const InterfaceDG*>(material_lib.material(_materialLabel - 1),__HERE__);
    if (interlaw != nullptr){  // otherwise it is not an interface law
        std::cout << "IsBoundary: " << interlaw->IsBoundary() << std::endl;
        if(interlaw->IsBoundary()){
            return new ReactionDiffusionInterfaceRegionExternalBoundary(this,
               static_cast<const ReactionDiffusionMaterial&>(*(material_lib.material(_matLabelLeft - 1))).number_unknowns(),  //the number of unknowns is set by the bulk material currently all will be treated the same way
               //*(material_lib.material(_materialLabel - 1)),
               dynamic_cast_or_fail<const InterfaceDG&>(*(material_lib.material(_materialLabel - 1)),__HERE__),
               //the material model for this material
               dynamic_cast_or_fail<const ReactionDiffusionMaterial&>(*(material_lib.material(_matLabelLeft - 1)), __HERE__),                   //the material model for the left material in the bulk
               coordinates);
        }
    }

    switch (region) {
        case MECHANICS:
            return new DirichletMechanicsRegion(
              this, _dim, *(material_lib.material(_materialLabel - 1)),
              dynamic_cast<const MechanicalMaterial&>(*(material_lib.material(_matLabelLeft - 1))),
              coordinates);
        case REACTION_DIFFUSION:
            return new ReactionDiffusionInterfaceRegion(this,
               static_cast<const ReactionDiffusionMaterial&>(*(material_lib.material(_matLabelLeft - 1))).number_unknowns(),  //the number of unknowns is set by the bulk material currently all will be treated the same way
               *(material_lib.material(_materialLabel - 1)),                                                                  //the material model for this material
               dynamic_cast<const ReactionDiffusionMaterial&>(*(material_lib.material(_matLabelLeft - 1))),                   //the material model for the left material in the bulk
               coordinates);
            break;
        case POISSONMANIFOLD:
            Message::Fatal(
              "ElementSetInterfaceOneSided::newRegion has not been implemented yet for FLUIDFLOW!");
        default:
            std::cout << "ElementSetInterfaceOneSided: No interface region for REGION=" << region
                      << std::endl;
            exit(1);
            return NULL;
    }
}

size_t summit::ElementSetInterfaceOneSided::pOrder() const { return _pOrder; }

summit::ElemType summit::ElementSetInterfaceOneSided::type() const { return INTERFACE; }

int summit::ElementSetInterfaceOneSided::vtkType() const { return 0; }

int summit::ElementSetInterfaceOneSided::materialLabelLeft() const { return _matLabelLeft; }

/**
 * @brief Compute the gradient of field u on the left side of the interface
 *
 * This method computes the spatial gradient of a field u at a specific quadrature point
 * on the left side of the interface element. The gradient is computed using the shape
 * function derivatives and the nodal values of the field.
 *
 * @param[in] e Element index within the element set
 * @param[in] q Quadrature point index within the element
 * @param[in] u Pointer to the nodal values of the field (size: nen * u_dim)
 * @param[in] u_dim Number of components per node (e.g., 1 for scalar, 3 for vector)
 * @param[out] Du Computed gradient tensor stored in row-major format (size: dim * u_dim)
 *                Du[i*u_dim + j] = du_j/dx_i where i is spatial direction, j is component
 *
 * @pre e must be a valid element index (0 <= e < nelements)
 * @pre q must be a valid quadrature point index (0 <= q < nquad)
 * @pre u must point to valid memory of size nen * u_dim
 * @pre Du must point to valid memory of size dim * u_dim
 */
void summit::ElementSetInterfaceOneSided::Gradient_L(
  elem_t e, quad_t q, real const* u, int u_dim, real* Du) const
{
    std::fill(Du, Du + _dim * u_dim, 0.);
    const real* dshp = dShape(e, q);
    real* Du0 = Du;
    // loop over the nodes of the element
    for (lnode_t a = leftNodeStart(); a != leftNodeDone(); ++a, u += u_dim) {
        // loop over the spatial dimension
        for (dim_t j(0); j < _dim; ++j, ++dshp) {
            // loop over the number of nodal values of u
            Du = Du0 + j;
            for (int i = 0; i < u_dim; ++i, Du += _dim) {
                // evaluate the gradient at this node
                (*Du) += u[i] * (*dshp);
            }
        }
    }

    // end of method
    return;
}

/**
 * @brief Interpolate nodal field values to quadrature points
 *
 * This method interpolates values from nodes to quadrature points using the element
 * shape functions. Currently not implemented for interface elements as interpolation
 * is typically handled by the Region class.
 *
 * @param[in] nodal_field Field values defined at the nodes
 * @param[out] quad_field Field values interpolated to quadrature points
 *
 * @note This method is not yet implemented and may be moved to the Region class
 * @todo Implement interpolation functionality or remove if not needed
 */
void summit::ElementSetInterfaceOneSided::Interpolate(
  const NodalField<real>& nodal_field, ElementQuadratureField<real>& quad_field) const
{
    // say something usefull...
    std::cout << "summit::ElementSetInterfaceOneSided::Interpolate not yet implemented\n"
              << "=> Not sure that it will be implemented eventually"
              << "=> should be moved to class Region" << std::endl;
    // ... and die!
    exit(1);

    // end of method
    return;
}

/**
 * @brief Extrapolate quadrature point values to element nodes
 *
 * This method extrapolates field values from quadrature points to element nodes
 * for a single element. This is typically used for visualization purposes or
 * post-processing operations.
 *
 * @param[in] e Element index within the element set
 * @param[in] quad_field Field values defined at quadrature points
 * @param[out] local_nodal_field Extrapolated field values at element nodes
 * @param[out] local_weights Weights for assembly of nodal values
 *
 * @note This method is not yet implemented and may be moved to the Region class
 * @todo Implement extrapolation functionality or remove if not needed
 */
void summit::ElementSetInterfaceOneSided::ElementaryExtrapolate(
  const elem_t e,
  const ElementQuadratureField<real>& quad_field,
  std::vector<real>& local_nodal_field,
  std::vector<real>& local_weights) const
{
    // say something usefull...
    std::cout << "Extrapolation is not yet implemented for your particular interface element_set\n"
              << "=> Not sure that it will be implemented eventually"
              << "=> should be moved to class Region" << std::endl;
    // ... and die!
    exit(1);

    // end of method
    return;
}


#if 0
void
summit::ElementSetInterfaceOneSided::
AverageField(QuadratureField<real>& quad_field, std::vector<real>& sum, double& outputVolume) {

    // say something usefull...
    std::cout << "summit::ElementSetInterfaceOneSided::AverageField not yet implemented\n"
              << "=> Not sure that it will be implemented eventually"
              << "=> should be moved to class Region" << std::endl;
    // ... and die!
    exit(1);

    // end of method
    return;
}
#endif

/**
 * @brief Compute the displacement jump across the interface
 *
 * This method computes the jump (discontinuity) in a field across the interface
 * at a specific quadrature point. For one-sided interfaces, this typically
 * represents the difference between the field value on the interface and
 * a reference value.
 *
 * @param[in] e Element index within the element set
 * @param[in] q Quadrature point index within the element
 * @param[in] ul Local field values for the element
 * @param[out] jumpU Computed jump values at the quadrature point
 * @param[in] _dof_node Degrees of freedom per node mapping (optional)
 *
 * @pre e must be a valid element index (0 <= e < nelements)
 * @pre q must be a valid quadrature point index (0 <= q < nquad)
 * @pre ul must contain valid field values for all element nodes
 * @pre jumpU must be properly sized to store the jump values
 */
void summit::ElementSetInterfaceOneSided::Jump(elem_t e,
                                               quad_t q,
                                               std::vector<real> const& ul,
                                               std::vector<real>& jumpU,
                                               const size_t* _dof_node) const
{
    // reset the displacement jump
    std::fill(jumpU.begin(), jumpU.end(), 0.0);
    if (_dof_node == nullptr) {
        _dof_node = &_dim;
    }
    size_t ci = 0;
    real const* shp = _shape.local(e, q);
    // start with the left element
    for (lnode_t c = leftNodeStart(); c != leftNodeDone(); ++c, ++shp) {
        // one degree of freedom at a time
        for (size_t i = 0; i < *_dof_node; ++i, ++ci) {
            // add the contribution of each left node
            jumpU[i] += ul[ci] * (*shp);
        }
    }

    // compute the opposite if on the left side of the interface
    if (_side == summit::LEFT) {
        for (size_t i = 0; i < *_dof_node; ++i) {
            jumpU[i] = -jumpU[i];
        }
    }

    // end of method
    return;
}


/**
 * @brief Build shape function interpolants for linear interface elements
 *
 * This method constructs the shape function interpolants and their derivatives
 * for linear interface elements. It computes and stores the shape functions,
 * their spatial derivatives, Jacobians, and normal vectors at all quadrature
 * points for all elements in the set.
 *
 * @param[in] coordinates Nodal coordinates for all nodes in the mesh
 *
 * @pre coordinates must contain valid coordinate data for all nodes
 * @pre Element connectivity must be properly initialized
 *
 * @note This version is used specifically for linear interface elements
 * @see BuildInterpolants(NodalField<real> const&, std::vector<int> const&) for higher-order elements
 */
void summit::ElementSetInterfaceOneSided::BuildInterpolants(NodalField<real> const& coordinates)
{
    // resize the container of derivatives of shape functions
    _shape.resize(_nelements, _nquad, _nen);

    // compute the number of evaluation of derivatives of shape functions stored at each
    // quadrature points (_nen = _nenLeft + _nenRight)
    int dShapeDim = _dim * _nen;
    // resize the container of derivatives of shape functions
    _dShape.resize(_nelements, _nquad, dShapeDim);

    // compute the number of jacobian evaluation stored at each quadrature points
    int jacdim = 1;
    // resize the container of jacobians
    _jac.resize(_nelements, _nquad, jacdim);

    // resize the containers of outer left element normal
    _normal_L.resize(_nelements, _nquad, _dim);

    // instantiate a vector of nodal coordinates local to an element
    std::vector<real> xl(_nen * _dim);

    // compute the _RegionKey
    ElementSetBody::computeRegionKey(1, coordinates);

    // loop over the elements
    for (elem_t e(0); e < _nelements; ++e) {
        this->Localize(coordinates, e, xl);
        // evaluate the derivative of the shape functions and the jacobians at each quadrature
        // points and store them
        ElementDShapeAndJac(xl, _shape.local(e, quad_t(0)), _dShape.local(e, quad_t(0)),
                            _jac.local(e, quad_t(0)), _normal_L.local(e, quad_t(0)));
    }

    // end of method
    return;
}

/**
 * @brief Build shape function interpolants for higher-order interface elements
 *
 * This method constructs the shape function interpolants and their derivatives
 * for higher-order interface elements. It computes and stores the shape functions,
 * their spatial derivatives, Jacobians, and normal vectors at all quadrature
 * points for all elements in the set.
 *
 * @param[in] coordinates Nodal coordinates for all nodes in the mesh
 * @param[in] indicesVerticesInterface Indices of the interface vertices for each element
 *
 * @pre coordinates must contain valid coordinate data for all nodes
 * @pre indicesVerticesInterface must contain valid vertex indices for all interface elements
 * @pre Element connectivity must be properly initialized
 *
 * @note This version is used for higher-order interface elements and includes vertex indexing
 * @see BuildInterpolants(NodalField<real> const&) for linear elements
 */
void summit::ElementSetInterfaceOneSided::BuildInterpolants(
  NodalField<real> const& coordinates, std::vector<int> const& indicesVerticesInterface)
{
    // resize the container of derivatives of shape functions
    _shape.resize(_nelements, _nquad, _nen);

    // compute the number of evaluation of derivatives of shape functions stored at each
    // quadrature points (_nen = _nenLeft + _nenRight)
    int dShapeDim = _dim * _nen;
    // resize the container of derivatives of shape functions
    _dShape.resize(_nelements, _nquad, dShapeDim);

    // compute the number of jacobian evaluation stored at each quadrature points
    int jacdim = 1;
    // resize the container of jacobians
    _jac.resize(_nelements, _nquad, jacdim);

    // resize the containers of outer left element normal
    _normal_L.resize(_nelements, _nquad, _dim);

    // get the number of connectivities which can be != from the number of nodes
    const size_t nconn = this->connectivities_element();

    // instantiate a vector of nodal coordinates local to an element
    std::vector<real> xl(nconn * _dim);

    // instantiate a vector of the vertices of the left triangle
    const size_t nleft1st = this->nodes_1stOrder_left_element();
    std::vector<real> xv(nleft1st * _dim);

    // compute the _RegionKey
    this->computeRegionKey(_bip, coordinates, indicesVerticesInterface);

    // loop over the elements
    for (elem_t e(0); e < _nelements; ++e) {
        // local nodal coordinates (coordinates for the element e)
        this->Localize(coordinates, e, xl);

        // loop over the vertices of the left triangle
        for (size_t a = 0; a < nleft1st; a++) {
            // get its index
            int p = indicesVerticesInterface[e * nleft1st + a];

            // loop over its coordinates
            for (size_t i = 0; i < _dim; i++) {
                // fill the vector of vertices of the left triangle
                xv[a * _dim + i] = coordinates(p, i);
            }
        }
        // evaluate the shape functions and its derivatives. the jacobians and
        // the normals to the left element at each quadrature point and store them
        ElementShapeDShapeJacAndNormal(e, xl, xv, _shape.local(e, quad_t(0)),
                                       _dShape.local(e, quad_t(0)), _jac.local(e, quad_t(0)),
                                       _normal_L.local(e, quad_t(0)));
    }

    // end of method
    return;
}

#if 0
void
summit::ElementSetInterfaceOneSided::
_addElements(std::vector<int> const & idElementsNeighborInterface)
{
    size_t n_new_element = idElementsNeighborInterface.size();
    for(size_t e(0); e<n_new_element;++e)
    {
        _idElementsNeighborInterface.push_back(idElementsNeighborInterface[e]);
    }
    // if two sided idElementsNeighborInterface is twice the size of the added element
    if(_bip ==1) // we are interface with 2 sides
        n_new_element/=2;
    _nelements += n_new_element;

    return;
}
#endif

/**
 * @brief Compute the region key for interface element identification
 *
 * This method computes a unique region key based on the interface type and
 * the centroid of the interface vertices. The region key is used to identify
 * and categorize interface elements for material assignment and processing.
 *
 * @param[in] bip Interface type parameter:
 *                - 0: bulk element
 *                - 1: interface element
 *                - 2: parallel interface
 * @param[in] coordinates Nodal coordinates for all nodes in the mesh
 * @param[in] indicesVerticesInterface Indices of the interface vertices
 *
 * @pre coordinates must contain valid coordinate data for all nodes
 * @pre indicesVerticesInterface must contain valid vertex indices
 * @pre bip must be a valid interface type (0, 1, or 2)
 *
 * @note The region key is stored in the _RegionKey member variable
 */
void summit::ElementSetInterfaceOneSided::computeRegionKey(
  const short int bip,
  NodalField<real> const& coordinates,
  std::vector<int> const& indicesVerticesInterface)
{
    // instantiate a vector of the vertices of the left triangle
    const size_t nleft1st = this->nodes_1stOrder_left_element();
    std::vector<real> xv((nleft1st - 1) * 3, 0.0);

    // loop over the vertices of the left triangle
    for (size_t a = 0; a < nleft1st - 1; a++) {
        // get its index
        int p = indicesVerticesInterface[a];
        // loop over its coordinates
        for (size_t i = 0; i < _dim; i++) {
            // fill the vector of vertices of the left triangle
            xv[a * _dim + i] = coordinates(p, i);
        }
    }

    std::vector<real> centroid(3, 0.0);
    for (size_t i = 0; i < nleft1st - 1; ++i) {
        for (size_t j = 0; j < _dim; ++j) {
            centroid[j] += xv[_dim * i + j];
        }
    }
    for (size_t j = 0; j < _dim; ++j) {
        centroid[j] /= (nleft1st - 1);
    }
    _RegionKey = RegionId(bip, centroid[0], centroid[1], centroid[2]);
}


/**
 * @brief Compute the inscribed radius of the interface element
 *
 * This method computes the radius of the largest circle/sphere that can be
 * inscribed within the interface element. This is used for stability analysis
 * and time step calculations in explicit time integration schemes.
 *
 * @param[in] xl Local nodal coordinates of the element
 * @return The inscribed radius of the element
 *
 * @pre xl must contain valid coordinates for all element nodes
 *
 * @note This method is not yet implemented for interface elements
 * @todo Implement inscribed radius calculation for interface elements
 */
summit::real summit::ElementSetInterfaceOneSided::InRadiusElement(const real* xl) const
{
    std::cout << "summit::ElementSetInterfaceOneSided::InRadiusElement has not been implemented yet"
              << std::endl;
    exit(1);
    return 0.0;
}

/**
 * @brief Compute the inscribed radius of the left element
 *
 * This method computes the inscribed radius specifically for the left element
 * adjacent to the interface. For one-sided interfaces, this delegates to
 * the general InRadiusElement method.
 *
 * @param[in] xl Local nodal coordinates of the element
 * @return The inscribed radius of the left element
 *
 * @pre xl must contain valid coordinates for all element nodes
 *
 * @see InRadiusElement(const real*) const
 */
summit::real summit::ElementSetInterfaceOneSided::InRadiusElementLeft(const real* xl) const
{
    return InRadiusElement(xl);
}

/**
 * @brief Compute the barycenter of an interface element
 *
 * This method computes the barycenter (geometric center) of an interface element
 * by averaging the positions of all quadrature points. This provides a representative
 * point for the interface that can be used for various geometric calculations.
 *
 * @param[in] e Element index within the element set
 * @param[in] xl Local nodal coordinates of the element
 * @param[out] barycenter Computed barycenter coordinates (size: dim)
 *
 * @pre e must be a valid element index (0 <= e < nelements)
 * @pre xl must contain valid coordinates for all element nodes
 * @pre barycenter must be properly sized (dim components)
 *
 * @note The barycenter is computed as the average of quadrature point positions
 */
void summit::ElementSetInterfaceOneSided::barycenter(const size_t e,
                                                     const real* xl,
                                                     std::vector<real>& barycenter) const
{
    // Compute the barycenter by computing the baryenter of the GaussPoints
    std::vector<real> GaussXYZ;
    this->computePositionOfIntegrationPoints(elem_t(e), xl, GaussXYZ);

    std::fill(barycenter.begin(), barycenter.end(), 0.);

    for (size_t n(0); n < _nquad; ++n) {
        for (size_t dim(0); dim < _dim; ++dim) {
            barycenter[dim] += GaussXYZ[_dim * n + dim];
        }
    }

    (_dim == 2) ? alphax<2>(1. / real(_nquad), barycenter.data()) :
                  alphax<3>(1. / real(_nquad), barycenter.data());
    return;
}


/**
 * @brief Compute physical positions of integration points (overload with global coordinates)
 *
 * This method computes the physical (X,Y,Z) coordinates of all quadrature points
 * for a given interface element. This overload takes global nodal coordinates
 * and localizes them before computing the integration point positions.
 *
 * @param[in] e Element index within the element set
 * @param[in] coordinates Global nodal coordinates for all nodes in the mesh
 * @param[out] physCoord Physical coordinates of quadrature points (size: dim * nquad)
 *
 * @pre e must be a valid element index (0 <= e < nelements)
 * @pre coordinates must contain valid coordinate data for all nodes
 * @pre physCoord will be resized to accommodate all quadrature point coordinates
 *
 * @see computePositionOfIntegrationPoints(const elem_t, const real*, std::vector<real>&) const
 */
void summit::ElementSetInterfaceOneSided::computePositionOfIntegrationPoints(
  const elem_t e, NodalField<real> const& coordinates, std::vector<real>& physCoord) const
{
    // localize nodal position
    std::vector<real> xl(_dim * _nen);
    this->Localize(coordinates, e, xl);
    this->computePositionOfIntegrationPoints(e, xl.data(), physCoord);
    return;
}

/**
 * @brief Compute physical positions of integration points (overload with local coordinates)
 *
 * This method computes the physical (X,Y,Z) coordinates of all quadrature points
 * for a given interface element using local nodal coordinates. The positions are
 * computed using shape function interpolation from the element nodes.
 *
 * @param[in] e Element index within the element set
 * @param[in] xl Local nodal coordinates of the element (size: dim * nen)
 * @param[out] physCoord Physical coordinates of quadrature points (size: dim * nquad)
 *
 * @pre e must be a valid element index (0 <= e < nelements)
 * @pre xl must contain valid coordinates for all element nodes
 * @pre physCoord will be resized to accommodate all quadrature point coordinates
 *
 * @note This method uses shape function interpolation to map from nodes to quadrature points
 */
void summit::ElementSetInterfaceOneSided::computePositionOfIntegrationPoints(
  const elem_t e, const real* xl, std::vector<real>& physCoord) const
{
    // resize the container
    physCoord.resize(_dim * _nquad);
    // reset the values
    std::fill(physCoord.begin(), physCoord.end(), 0.);

    // loop over the quadrature points
    real* xgauss = physCoord.data();
    const real* Na = this->shape(e, quad_t(0));
    for (quad_t q(0); q < _nquad; ++q, xgauss += _dim) {
        // loop over the nodes
        const real* xa = xl;
        for (lnode_t a(leftNodeStart()); a != leftNodeDone(); ++a, ++Na) {
            for (dim_t i(0); i < _dim; ++i, ++xa)
                xgauss[i] += (*Na) * (*xa);
        }
        Na += (_nen - _nenLeft);  // skip right side for twoSided
    }

    return;
}

/**
 * @brief Compute sparsity pattern for the stiffness matrix
 *
 * This method computes the sparsity pattern of the global stiffness matrix
 * for the interface element set. It determines which matrix entries will be
 * non-zero based on the element connectivity and degrees of freedom mapping.
 *
 * @param[in] eqMap Mapping from local degrees of freedom to global equation numbers
 * @param[in] n_dof_node Number of degrees of freedom per node
 * @param[in] halfButterflyMissingWingEQN Equation numbers for missing wing elements
 * @param[out] sparsity Sparsity pattern as a map of sets (row -> set of column indices)
 *
 * @pre eqMap must contain valid equation mappings for all local DOFs
 * @pre n_dof_node must be positive
 * @pre sparsity map will be populated with non-zero matrix entry locations
 *
 * @note This is used for efficient sparse matrix assembly and storage
 */
void summit::ElementSetInterfaceOneSided::ComputeSparsityPattern(
  const int * eqMap, int n_dof_node,
  summit::ElementQuadratureField<int>& halfButterflyMissingWingEQN,
  std::map<int, std::set<int> >& sparsity) const
{
    // get the number of element node
    size_t const nen = 2 * this->connectivities_element();
    // accessor to halfButterflyMissingWingEQN data
    int* halfButterflyMissingWingEQN_data = halfButterflyMissingWingEQN.data();
    // allocate memory for element equation map and forces
    summit::NodalField<int> elemEqnMap(n_dof_node, nen);

    // loop over the elements of the element set
    for (elem_t e(0); e != _nelements; ++e) {
        // construct the equation map and forces array for this element
        //--fill the left wing
        for (size_t nod = 0; nod < nen / 2; ++nod) {
            for (int i = 0; i < n_dof_node; ++i) {
                int nodeID = _dof_map->Connectivity(e)[nod];
                elemEqnMap(nod, i) = eqMap[nodeID * n_dof_node + i];
            }
        }
        //--fill the right wing
        for (size_t nod = 0; nod < nen / 2; ++nod) {
            for (int i = 0; i < n_dof_node; ++i) {
                elemEqnMap(nod + nen / 2, i) =
                  halfButterflyMissingWingEQN_data[e * nen / 2 * n_dof_node + nod * n_dof_node + i];
            }
        }

        // loop over the nodes of the element
        for (size_t ea = 0; ea < nen; ea++) {
            // loop over the degrees of freedom of the node
            for (int i = 0; i < n_dof_node; i++) {
                // get the equation number associated with the degrees of freedom
                int p = elemEqnMap(ea, i);
                // if the degree of freedom is imposed (i.e. Dirichlet)
                if (p < 0) {
                    // then move on to the next one
                    continue;
                }
                // loop over the nodes of the element
                for (size_t eb = 0; eb < nen; eb++) {
                    // loop over the degrees of freedom of the node
                    for (int j = 0; j < n_dof_node; j++) {
                        // get the equation number associated with the degrees of freedom
                        int q = elemEqnMap(eb, j);
                        // if the degree of freedom is not imposed (i.e. Dirichlet)
                        if (q >= 0) {
                            // find if this row index already exists in the map
                            std::map<int, std::set<int> >::iterator mIt;
                            mIt = sparsity.find(p);
                            // if this row index does not exist
                            if (mIt == sparsity.end()) {
                                std::set<int> colIndex;
                                colIndex.insert(q);
                                sparsity.insert(std::pair<int, std::set<int> >(p, colIndex));
                            }
                            else {  // the row index exists
                                (*mIt).second.insert(q);
                            }
                        }
                    }
                }
            }
        }
    }

    // end of method
    return;
}

/**
 * @brief Write element set data to checkpoint for restart capability
 *
 * This method writes all necessary data for the interface element set to a
 * checkpoint file, enabling simulation restart from a saved state. It stores
 * element properties, connectivity, and material information.
 *
 * @param[in] checkpoint Pointer to the checkpoint object for data storage
 * @param[in] name Name of the group within the checkpoint for this element set
 * @param[in] tag Additional tag for organizing checkpoint data
 *
 * @pre checkpoint must be a valid, opened checkpoint object
 * @pre name must be a valid group name
 * @pre tag must not be nullptr
 *
 * @note This method calls the base class WriteForRestart and adds interface-specific data
 */
void summit::ElementSetInterfaceOneSided::WriteForRestart(Checkpoint* checkpoint,
                                                          const char* name,
                                                          const char* tag) const
{
    assert(tag != nullptr);

    ElementSetBody::WriteForRestart(checkpoint, name, tag);

    std::vector<int> dims(1);
    dims[0] = 1;

    // Write order
    checkpoint->WriteDataSet("pOrder", dims, SUMMIT_SIZE, &_pOrder);

    // Write number of quadrature points
    checkpoint->WriteDataSet("nquad", dims, SUMMIT_SIZE, &_nquad);

    // Write number of nodes per element (left element)
    checkpoint->WriteDataSet("nenLeft", dims, SUMMIT_SIZE, &_nenLeft);

    // Write left element material label
    checkpoint->WriteDataSet("matLabelLeft", dims, SUMMIT_INT, &_matLabelLeft);

    // Write interface type
    checkpoint->WriteDataSet("bip", dims, SUMMIT_SHORT_INT, &_bip);

    // Write side of element with respect to interface
    checkpoint->WriteDataSet("side", dims, SUMMIT_INT, &_side);

    // Write globalElementId
    dims[0] = _globalElementId.size();
    checkpoint->WriteDataSet("globalElementId", dims, SUMMIT_INT, _globalElementId.data());

    // all done
    return;
}

// end of file
