#include <cstdlib>
#include <cstdio>
#include <cmath>
#include <iostream>

#include "element_quadrature_field.h"
#include "element_set_interface_tetP.h"
#include "../fem/reference_p_element.h"

#include "../fem/nodal_field.h"
#include "../mathlib/quadrature_library.h"
#include "../utils/math-util.h"
#include "../utils/spatial_sort.h"

#include "../restart/checkpoint.h"


/**
 * @brief Constructor for tetrahedral interface elements with polynomial order P
 *
 * Creates a set of tetrahedral interface elements for 3D discontinuous Galerkin
 * formulations. These elements handle interfaces between different materials or
 * boundary conditions in 3D finite element simulations.
 *
 * @param[in] topo Topology identifier for the element set
 * @param[in] nElements Number of interface elements in the set
 * @param[in] nNodes_elem Number of nodes per tetrahedral element
 * @param[in] pOrder Polynomial interpolation order for shape functions
 * @param[in] matLabel Material label for the interface material (1-based index)
 * @param[in] matLabelLeft Material label for the left bulk material (1-based index)
 * @param[in] connectivity Element connectivity array (node indices for each element)
 * @param[in] coordinates Global nodal coordinates for all nodes in the mesh
 * @param[in] indicesVerticesInterface Indices of interface vertices for each element
 * @param[in] globalElementId Global element indices for interface elements
 * @param[in] side Side specification for the interface (LEFT, RIGHT, or COMPLETE)
 * @param[in] bip Interface type parameter: 1=interface, 2=parallel
 *
 * @pre All input vectors must have consistent sizes
 * @pre Material labels must be valid indices in the material library
 * @pre Connectivity must reference valid node indices
 */
summit::ElementSetInterfaceTetP::ElementSetInterfaceTetP(
  size_t topo,
  size_t nElements,
  size_t nNodes_elem,
  size_t pOrder,
  int matLabel,
  int matLabelLeft,
  std::vector<int> const& connectivity,
  NodalField<real> const& coordinates,
  std::vector<int> const& indicesVerticesInterface,
  std::vector<int> const& globalElementId,
  SIDE side,
  short int bip)
  : ElementSetInterfaceOneSided(/*dim*/ 3,
                                topo,
                                nElements,
                                /*nenLeft*/ nNodes_elem,
                                /*nenRight*/ nNodes_elem,
                                pOrder,
                                matLabel,
                                matLabelLeft,
                                globalElementId,
                                bip,
                                side)
{
    // populate the DoFMap:
    _dof_map->AddElementSet(connectivity, nNodes_elem, nElements);

    // creation of the gauss coordinates and corresponding weights for the 2D element of reference
    // (r,s>=-1 and r+s<=0)
    _nquad = this->fillInterfaceQuadratureRule<3>(_pOrder, _isoGaussPointsWeight, _coeff_mapp_s1,
                                                  _coeff_mapp_s2, _coeff_mapp_s3, &_coeff_mapp_s4);

    // instantiate a BasisFunctionsReferenceTetP object
    _basisFunctionsTetP = new BasisFunctionsReferenceTetP(_pOrder);

    // container to store the quadrature coordinates in the isoparametric space
    // (r,s,t>=-1  r+s+t<=-1) for the left element
    _isoParamQuadCoordLeft.resize(_nquad * _dim);

    // initialize shape functions
    this->BuildInterpolants(coordinates, indicesVerticesInterface);
}

/**
 * @brief Restart constructor from checkpoint data
 *
 * Reconstructs an ElementSetInterfaceTetP object from previously saved checkpoint data.
 * This constructor is used for simulation restart capabilities.
 *
 * @param[in] checkpoint Pointer to the checkpoint object containing saved data
 * @param[in] name Name of the group within the checkpoint containing this element set's data
 *
 * @pre checkpoint must be a valid, opened checkpoint object
 * @pre name must correspond to an existing group in the checkpoint
 */
summit::ElementSetInterfaceTetP::ElementSetInterfaceTetP(Checkpoint* checkpoint, const char* name)
  : ElementSetInterfaceOneSided(checkpoint, name)
{
    // instantiate a BasisFunctionsReferenceTetP object
    _basisFunctionsTetP = new BasisFunctionsReferenceTetP(_pOrder);
}

summit::ElementSetInterfaceTetP::~ElementSetInterfaceTetP() { delete _basisFunctionsTetP; }

size_t summit::ElementSetInterfaceTetP::nodes_simplex() const { return 8; }

size_t summit::ElementSetInterfaceTetP::nodes_1stOrder_left_element() const { return 4; }

size_t summit::ElementSetInterfaceTetP::nquad() const { return _nquad; }

summit::ElemType summit::ElementSetInterfaceTetP::type() const {
    if(_bip==0){
        return DG_BOUNDARY; //here bip is a bulk element set key. This is required because the set iterators at the weakform level are poorly designed and force all non-bulk regions to be DG regions. Here we have a boundary condition region, but we pretend it is a bulk region until we fix our code design. It would be greatly desirable to use less complicated structures and control flow in a future rewrite. The unnecessary complixity of sorting things by BIP, but iterating through the sorted set until we reach a given type is inflexible and error prone.
    }return HALF_BUTTERFLY;
}

/**
 * @brief Lump mass matrix for tetrahedral interface elements
 *
 * This method computes the lumped mass matrix for tetrahedral interface elements
 * by distributing the total mass among the elements. Currently not implemented
 * for interface elements.
 *
 * @param[in] total_mass Total mass to be distributed among elements
 * @param[out] elem_mass Mass assigned to each element
 *
 * @note This method is not yet implemented for interface elements
 * @todo Implement mass lumping for interface elements or remove if not needed
 */
void summit::ElementSetInterfaceTetP::LumpMass(const real total_mass,
                                               std::vector<real>& elem_mass) const
{
    // say something useful...
    std::cout << "in ElementSetInterfaceTetP class : LumpMass method not yet implemented\n"
              << "=> Not sure that it will be implemented eventually" << std::endl;
    // ... and die!
    exit(1);

    // end of method
    return;
}

void summit::ElementSetInterfaceTetP::ElementShape()
{
    // say something useful...
    std::cout << "in ElementSetInterfaceTetP class : ElementShape method not yet implemented\n"
              << "=> Not sure that it will be implemented eventually" << std::endl;
    // ... and die!
    exit(1);

    // end of method
    return;
}

void summit::ElementSetInterfaceTetP::ElementDShapeAndJac(
  std::vector<real> const& xl, real* shape, real* dShape, real* jac, real* normal_L)
{
    // say something useful...
    std::cout
      << "in ElementSetInterfaceTetP class : ElementDShapeAndJac method not yet implemented\n"
      << "=> Not sure that it will be implemented eventually" << std::endl;
    // ... and die!
    exit(1);

    // end of method
    return;
}

/**
 * @brief Compute shape functions, derivatives, Jacobian, and normal vectors
 *
 * This method computes the shape functions, their spatial derivatives, the Jacobian
 * determinant, and the outward normal vectors for a tetrahedral interface element
 * at all quadrature points.
 *
 * @param[in] e Element index within the element set
 * @param[in] xl Local nodal coordinates of the element
 * @param[in] xv Vertices of the interface (unused for tetrahedral elements)
 * @param[out] shape Shape function values at quadrature points
 * @param[out] dShape Shape function derivatives at quadrature points
 * @param[out] jac Jacobian determinants at quadrature points
 * @param[out] normal_L Outward normal vectors at quadrature points
 *
 * @pre e must be a valid element index
 * @pre xl must contain valid coordinates for all element nodes
 * @pre Output arrays must be properly allocated
 */
void summit::ElementSetInterfaceTetP::ElementShapeDShapeJacAndNormal(
  const size_t e,
  std::vector<real> const& xl,
  std::vector<real> const& xv,  // e unused
  real* shape,
  real* dShape,
  real* jac,
  real* normal_L)
{
    // 1. Creation of physical coordinates for gauss points

    // container to store the area of the face
    real areaFace = this->simplicesInterfaceJacobian<3>(xv);

    // container to store the spatially sorted quadrature coordinates in the physical space for the
    // interface element
    typedef std::set<SpatialSort::point_t, SpatialSort> spatialSet_t;
    spatialSet_t quadSet;
    SpatialSort::coord_t quadCoord = { 0.0, 0.0, 0.0 };
    // calculation of the coordinates and weights of the gauss points (physical space)
    for (quad_t lquad(0); lquad < _nquad; ++lquad) {
        // loop over spatial dimensions
        for (size_t j = 0; j < _dim; j++) {
            // calculation of quadrature coordinates
            quadCoord[j] = _coeff_mapp_s1[lquad] * (xv[3 * 0 + j])     // vertex 1
                           + _coeff_mapp_s2[lquad] * (xv[3 * 1 + j])   // vertex 2
                           + _coeff_mapp_s3[lquad] * (xv[3 * 2 + j])   // vertex 3
                           + _coeff_mapp_s4[lquad] * (xv[3 * 3 + j]);  // vertex 4
        }
        quadSet.insert(SpatialSort::point_t(lquad, quadCoord));
    }

    int lq = 0;
    std::vector<real> sortedQuadCoord(_dim * _nquad);
    for (spatialSet_t::iterator it = quadSet.begin(); it != quadSet.end(); ++it, ++lq) {
        quadCoord = std::get<1>(*it);
        for (size_t j = 0; j < _dim; j++) {
            sortedQuadCoord[lq * _dim + j] = quadCoord[j];
        }
        int lquad = std::get<0>(*it);

        // weight multiplied by the Jacobian (area of the face)
        jac[lq] = _isoGaussPointsWeight[lquad] * areaFace;
        if (jac[lq] <= 0.0) {
            std::cout << "Error in ElementSetInterfaceTetP::ElementShapeDShapeAndJac : "
                      << "Negative jacobian" << std::endl;
            exit(1);
        }
    }


    // 2. Calculation of the isoparametric coordinates for gauss points
    this->fromPhysical2IsoparametricCoordinates<3>(_nquad, _pOrder, &(xl[0]), &(sortedQuadCoord[0]),
                                                   _isoParamQuadCoordLeft);


    // 3. Calculation of shape functions and derivatives for all the gauss points
    //    (isoparametric element)

    // containers to store the shape functions and its derivatives
    std::vector<real> Na_left, dNa_left;
    // calculation of the shape functions and its derivatives (left and right elements)
    _basisFunctionsTetP[0].ComputeBasisFunctions(_nquad, _isoParamQuadCoordLeft, Na_left, dNa_left);


    // 4. Store the shape functions for all the gauss points

    // loop over the quadrature points of the element in the reference configuration
    // the shape functions are stored
    for (quad_t lquad(0); lquad < _nquad; ++lquad) {
        // left element
        int countLeft = 0;
        for (lnode_t a = leftNodeStart(); a != leftNodeDone(); ++a) {
            shape[_nen * lquad + a] = Na_left[_nenLeft * lquad + countLeft];
            countLeft++;
        }
    }


    // 5. Transform the shape function derivatives to the physical space and store them
    this->shapeDerivativeFromUVW2XYZ<3>(_nquad, leftNodeStart(), leftNodeDone(), dNa_left.data(),
                                        xl.data(), dShape);


    // 6. Calculate the normal to the left element for each quadrature point and store them
    //    The normals are the same for all the quadrature points because we are working with
    //    simplices

    // calculation of the normal (ONLY valid for simplices)
    real normal[_dim];
    this->simplicesNormal<3>(xv, normal);
    if (_side == RIGHT)  // If we are right we invert the normal
    {
        alphax<3>(-1., normal);
    }

    // the normals are stored for each quadrature point
    for (quad_t lquad(0); lquad < _nquad; ++lquad) {
        normal_L[lquad * _dim + 0] = normal[0];
        normal_L[lquad * _dim + 1] = normal[1];
        normal_L[lquad * _dim + 2] = normal[2];
    }

    // end of method
    return;
}

void summit::ElementSetInterfaceTetP::ElementaryExtrapolate(
  const elem_t e,
  const ElementQuadratureField<real>& quad_field,
  std::vector<real>& local_nodal_field,
  std::vector<real>& local_weights) const
{
    const int ndim = quad_field.dim();

    std::fill(local_nodal_field.begin(), local_nodal_field.end(), 0.);
    std::fill(local_weights.begin(), local_weights.end(), 0.);

    // calculate area of the interface for weighting
    real wg = 0;
    for (quad_t lquad(0); lquad < _nquad; ++lquad) {
        wg += _jac.local(e, lquad)[0];
    }

    // weight the area with respect to the shape functions
    // (this guarantees that the local weight of the vertices opposite to the interface is zero)
    for (quad_t lquad(0); lquad < _nquad; ++lquad) {
        for (lnode_t a(0); a < _nen; ++a) {
            local_weights[a] += wg * (shape(e, lquad, a) > 0) / _nquad;
        }
    }

    for (quad_t lquad(0); lquad < _nquad; ++lquad) {
        const real* local_quad_field = quad_field.local(e, lquad);

        for (lnode_t a(0); a < _nen; ++a)
            for (int k = 0; k < ndim; ++k)
                local_nodal_field[ndim * a + k] += local_weights[a] * local_quad_field[k] / _nquad;
    }

    return;
}

summit::real summit::ElementSetInterfaceTetP::InRadiusElement(const real* xl) const
{
    return InRadiusTetrahedronElement(_pOrder, xl);
}

void summit::ElementSetInterfaceTetP::WriteForRestart(Checkpoint* checkpoint,
                                                      const char* name,
                                                      const char* tag) const
{
    // Call write of super class (this creates the group for this element set)
    if (tag == nullptr) {
        ElementSetInterfaceOneSided::WriteForRestart(checkpoint, name, "ElementSetInterfaceTetP");
    }
    else {
        ElementSetInterfaceOneSided::WriteForRestart(checkpoint, name, tag);
    }

    // all done
    return;
}

void summit::ElementSetInterfaceTetP::FillSubConnectivities()
{
    // container to store the centroidal coordinates of the sub-elements generated by the
    // subdivision of the simplex
    //      the coordinates are only stored for the element of reference (the calculation for each
    //      element of the mesh is not needed)
    // instantiate a ReferencePElement object
    ReferencePElement reference_element(_dim, _pOrder);
    reference_element.Init();
    // number of connectivities stemming from the subdivision of a d-simplex in non-overlapped
    // d-simplices
    const int nSubConn = reference_element.nSubdivisionConnectivities();
    // centroidal coordinates for each subelement stemming from the subdivision of a d-simplex in
    // p^d non-overlapped d-simplices
    const std::vector<real>& subdivisionCoordinates = reference_element.subdivisionCoordinates();
    // connectivities stemming from the subdivision of a d-simplex in p^d non-overlapped d-simplices
    const std::vector<int>& subdivisionConnectivities =
      reference_element.subdivisionConnectivities();
    // number of sub-elements
    _nSubConn = nSubConn;
    // copy the centroidal coordinates of the sub-elements
    _SubdivisionCoord.resize(_dim * _nSubConn);
    std::copy(subdivisionCoordinates.begin(), subdivisionCoordinates.end(),
              _SubdivisionCoord.begin());
    // copy the connectivities of the sub-elements
    _subdivisionConnectivity.resize((_dim + 1) * _nSubConn);
    std::copy(subdivisionConnectivities.begin(), subdivisionConnectivities.end(),
              _subdivisionConnectivity.begin());

    // all done
    return;
}

// Register ElementSetInterfaceTetP as option for class to instantiate
REGISTER(ElementSetBody, ElementSetInterfaceTetP);

// end of file
