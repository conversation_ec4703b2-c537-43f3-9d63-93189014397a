#include <cstdlib>
#include <cstdio>
#include <cmath>
#include <iostream>

#include "element_quadrature_field.h"
#include "../fem/nodal_field.h"
#include "../fem/reference_p_element.h"

#include "../utils/spatial_sort.h"

#include "../mathlib/quadrature_library.h"
#include "../utils/math-util.h"

#include "element_set_interface_triP.h"

#include "../restart/checkpoint.h"

summit::ElementSetInterfaceTriP::ElementSetInterfaceTriP(
  size_t topo,
  size_t nElements,
  size_t nNodes_elem,
  size_t pOrder,
  int matLabel,
  int matLabelLeft,
  std::vector<int> const& connectivity,
  NodalField<real> const& coordinates,
  std::vector<int> const& indicesVerticesInterface,
  std::vector<int> const& globalElementId,
  SIDE side,
  short int bip)
  : ElementSetInterfaceOneSided(/*dim*/ 2,
                                topo,
                                nElements,
                                /*nen*/ nNodes_elem,
                                /*nenLeft*/ nNodes_elem,
                                pOrder,
                                matLabel,
                                matLabelLeft,
                                globalElementId,
                                bip,
                                side)
{
    // populate the DoFMap:
    _dof_map->AddElementSet(connectivity, nNodes_elem, nElements);

    // creation of the gauss coordinates and corresponding weights for the 1D element of reference
    // (-1 <= r <= 1)
    _nquad = this->fillInterfaceQuadratureRule<2>(_pOrder, _isoGaussPointsWeight, _coeff_mapp_s1,
                                                  _coeff_mapp_s2, _coeff_mapp_s3);

    // instantiate a BasisFunctionsReferenceTriP object
    _basisFunctionsTriP = new BasisFunctionsReferenceTriP(_pOrder);

    // container to store the quadrature coordinates in the isoparametric space
    // (r,s>=-1 and r+s<=0) for the left element
    _isoParamQuadCoordLeft.resize(_nquad * _dim);

    // initialize shape functions
    this->BuildInterpolants(coordinates, indicesVerticesInterface);
}

summit::ElementSetInterfaceTriP::ElementSetInterfaceTriP(Checkpoint* checkpoint, const char* name)
  : ElementSetInterfaceOneSided(checkpoint, name)
{
    // instantiate a BasisFunctionsReferenceTriP object
    _basisFunctionsTriP = new BasisFunctionsReferenceTriP(_pOrder);
}

summit::ElementSetInterfaceTriP::~ElementSetInterfaceTriP() { delete _basisFunctionsTriP; }

size_t summit::ElementSetInterfaceTriP::nquad() const { return _nquad; }

size_t summit::ElementSetInterfaceTriP::nodes_simplex() const { return 6; }

size_t summit::ElementSetInterfaceTriP::nodes_1stOrder_left_element() const { return 3; }

summit::ElemType summit::ElementSetInterfaceTriP::type() const { return HALF_BUTTERFLY; }


void summit::ElementSetInterfaceTriP::LumpMass(const real total_mass,
                                               std::vector<real>& elem_mass) const
{
    // say something useful...
    std::cout << "in ElementSetInterfaceTriP class : LumpMass method not yet implemented\n"
              << "=> Not sure that it will be implemented eventually" << std::endl;
    // ... and die!
    exit(1);

    // end of method
    return;
}


void summit::ElementSetInterfaceTriP::ElementShape()
{
    // say something useful...
    std::cout << "in ElementSetInterfaceTriP class : ElementShape method not yet implemented\n"
              << "=> Not sure that it will be implemented eventually" << std::endl;
    // ... and die!
    exit(1);

    // end of method
    return;
}


void summit::ElementSetInterfaceTriP::ElementDShapeAndJac(
  std::vector<real> const& xl, real* shape, real* dShape, real* jac, real* normal_L)
{
    // say something useful...
    std::cout
      << "in ElementSetInterfaceTriP class : ElementDShapeAndJac method not yet implemented\n"
      << "=> Not sure that it will be implemented eventually" << std::endl;
    // ... and die!
    exit(1);

    // end of method
    return;
}

void summit::ElementSetInterfaceTriP::ElementShapeDShapeJacAndNormal(const size_t e,
                                                                     std::vector<real> const& xl,
                                                                     std::vector<real> const& xv,
                                                                     real* shape,
                                                                     real* dShape,
                                                                     real* jac,
                                                                     real* normal_L)
{
    // 1. Creation of physical coordinates for gauss points

    // container to store the length of the edge
    real lengthEdge = this->simplicesInterfaceJacobian<2>(xv);

    // container to store the spatially sorted quadrature coordinates in the physical space for the
    // interface element
    typedef std::set<SpatialSort::point_t, SpatialSort> spatialSet_t;
    spatialSet_t quadSet;
    SpatialSort::coord_t quadCoord = { 0.0, 0.0, 0.0 };

    // calculation of the coordinates and weights of the gauss points (physical space)
    for (quad_t lquad(0); lquad < _nquad; ++lquad) {
        // loop over spatial dimensions
        for (size_t j = 0; j < _dim; j++) {
            // calculation of quadrature coordinates
            quadCoord[j] = _coeff_mapp_s1[lquad] * (xv[2 * 0 + j])     // vertex 1
                           + _coeff_mapp_s2[lquad] * (xv[2 * 1 + j])   // vertex 2
                           + _coeff_mapp_s3[lquad] * (xv[2 * 2 + j]);  // vertex 3
        }
        quadSet.insert(SpatialSort::point_t(lquad, quadCoord));
    }

    int lq = 0;
    std::vector<real> sortedQuadCoord(_dim * _nquad);
    for (spatialSet_t::iterator it = quadSet.begin(); it != quadSet.end(); ++it, ++lq) {
        quadCoord = std::get<1>(*it);
        for (size_t j = 0; j < _dim; j++) {
            sortedQuadCoord[lq * _dim + j] = quadCoord[j];
        }
        int lquad = std::get<0>(*it);
        // weight multiplied by the Jacobian (length of the edge)
        jac[lq] = _isoGaussPointsWeight[lquad] * lengthEdge;
        if (jac[lq] <= 0.0) {
            std::cout
              << "Error in ElementSetInterfaceTriP::ElementShapeDShapeAndJac : Negative jacobian"
              << std::endl;
            exit(1);
        }
    }


    // 2. Calculation of the isoparametric coordinates for gauss points
    this->fromPhysical2IsoparametricCoordinates<2>(_nquad, _pOrder, xl.data(),
                                                   sortedQuadCoord.data(), _isoParamQuadCoordLeft);


    // 3. Calculation of shape functions and derivatives for all the gauss points (isoparametric
    // element)

    // containers to store the shape functions and its derivatives
    std::vector<real> Na_left, dNa_left;
    // calculation of the shape functions and its derivatives (left elements)
    _basisFunctionsTriP[0].ComputeBasisFunctions(_nquad, _isoParamQuadCoordLeft, Na_left, dNa_left);


    // 4. Store the shape functions for all the gauss points

    // loop over the quadrature points of the element in the reference configuration
    // the shape functions are stored
    for (quad_t lquad(0); lquad < _nquad; ++lquad) {
        // left element
        int countLeft = 0;
        for (lnode_t a = leftNodeStart(); a != leftNodeDone(); ++a) {
            shape[_nen * lquad + a] = Na_left[_nenLeft * lquad + countLeft];
            countLeft++;
        }
    }


    // 5. Transform the shape function derivatives to the physical space and store them
    this->shapeDerivativeFromUVW2XYZ<2>(_nquad, leftNodeStart(), leftNodeDone(), dNa_left.data(),
                                        xl.data(), dShape);


    // 6. Calculate the normal to the left element for each quadrature point and store them
    //    The normals are the same for all the quadrature points because we are working with
    //    simplices

    // calculation of the normal (ONLY valid for simplices)
    real normal[_dim];
    this->simplicesNormal<2>(xv, normal);
    if (_side == RIGHT)  // If we are right we invert the normal
    {
        alphax<2>(-1., normal);
    }

    // the normals are stored for each quadrature point
    for (quad_t lquad(0); lquad < _nquad; ++lquad) {
        normal_L[lquad * _dim + 0] = normal[0];
        normal_L[lquad * _dim + 1] = normal[1];
    }

    // end of method
    return;
}

void summit::ElementSetInterfaceTriP::ElementaryExtrapolate(
  const elem_t e,
  const ElementQuadratureField<real>& quad_field,
  std::vector<real>& local_nodal_field,
  std::vector<real>& local_weights) const
{
    const int ndim = quad_field.dim();

    std::fill(local_nodal_field.begin(), local_nodal_field.end(), 0.);
    std::fill(local_weights.begin(), local_weights.end(), 0.);

    // calculate area of the interface for weighting
    real wg = 0;
    for (quad_t lquad(0); lquad < _nquad; ++lquad) {
        wg += _jac.local(e, lquad)[0];
    }

    // weight the area with respect to the shape functions
    // (this guarantees that the local weight of the vertices opposite to the interface is zero)
    for (quad_t lquad(0); lquad < _nquad; ++lquad) {
        for (lnode_t a(0); a < _nen; ++a) {
            local_weights[a] += wg * (shape(e, lquad, a) > 0) / _nquad;
        }
    }

    for (quad_t lquad(0); lquad < _nquad; ++lquad) {
        const real* local_quad_field = quad_field.local(e, lquad);

        for (lnode_t a(0); a < _nen; ++a)
            for (int k = 0; k < ndim; ++k)
                local_nodal_field[ndim * a + k] += local_weights[a] * local_quad_field[k] / _nquad;
    }

    return;
}

summit::real summit::ElementSetInterfaceTriP::InRadiusElement(const real* xl) const
{
    return InRadiusTriangleElement(_pOrder, xl);
}

void summit::ElementSetInterfaceTriP::WriteForRestart(Checkpoint* checkpoint,
                                                      const char* name,
                                                      const char* tag) const
{
    // Call write of super class (this creates the group for this element set)
    if (tag == nullptr) {
        ElementSetInterfaceOneSided::WriteForRestart(checkpoint, name, "ElementSetInterfaceTriP");
    }
    else {
        ElementSetInterfaceOneSided::WriteForRestart(checkpoint, name, tag);
    }

    // all done
    return;
}

void summit::ElementSetInterfaceTriP::FillSubConnectivities()
{
    // container to store the centroidal coordinates of the sub-elements generated by the
    // subdivision of the simplex
    //      the coordinates are only stored for the element of reference (the calculation for each
    //      element of the mesh is not needed)
    // instantiate a ReferencePElement object
    ReferencePElement reference_element(_dim, _pOrder);
    reference_element.Init();
    // number of connectivities stemming from the subdivision of a d-simplex in non-overlapped
    // d-simplices
    const int nSubConn = reference_element.nSubdivisionConnectivities();
    // centroidal coordinates for each subelement stemming from the subdivision of a d-simplex in
    // p^d non-overlapped d-simplices
    const std::vector<real>& subdivisionCoordinates = reference_element.subdivisionCoordinates();
    // connectivities stemming from the subdivision of a d-simplex in p^d non-overlapped d-simplices
    const std::vector<int>& subdivisionConnectivities =
      reference_element.subdivisionConnectivities();
    // number of sub-elements
    _nSubConn = nSubConn;
    // copy the centroidal coordinates of the sub-elements
    _SubdivisionCoord.resize(_dim * _nSubConn);
    std::copy(subdivisionCoordinates.begin(), subdivisionCoordinates.end(),
              _SubdivisionCoord.begin());
    // copy the connectivities of the sub-elements
    _subdivisionConnectivity.resize((_dim + 1) * _nSubConn);
    std::copy(subdivisionConnectivities.begin(), subdivisionConnectivities.end(),
              _subdivisionConnectivity.begin());

    // all done
    return;
}

// Register ElementSetInterfaceTriP as option for class to instantiate
REGISTER(ElementSetBody, ElementSetInterfaceTriP);

// end of file
