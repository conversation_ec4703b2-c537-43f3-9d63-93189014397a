/**
 * @file element_set_interface_one_sided.h
 * @brief One-sided interface element set for discontinuous Galerkin methods
 * <AUTHOR> Development Team
 * @date 2011-2012
 *
 * This file contains the ElementSetInterfaceOneSided class which implements
 * one-sided interface elements for discontinuous Galerkin (DG) formulations.
 * These elements are used to handle interfaces between different materials
 * or boundary conditions in finite element simulations.
 */

#ifndef SUMMIT_ELEMENT_SET_INTERFACE_ONE_SIDED_H
#define SUMMIT_ELEMENT_SET_INTERFACE_ONE_SIDED_H

// -*- C++ -*-
//
// rrgroup
// summit development team
// massachusetts institute of technology
// (c) 2011-2012 all rights reserved
//

#include "element_set_body.h"
#include <tuple>
#include "../materials/material_library.h"
namespace summit {

class Checkpoint;

/**
 * @brief One-sided interface element set for discontinuous Galerkin methods
 *
 * The ElementSetInterfaceOneSided class represents a collection of interface elements
 * that are used in discontinuous Galerkin (DG) formulations. These elements handle
 * the coupling between different materials or enforce boundary conditions at interfaces.
 *
 * Key features:
 * - Supports one-sided interface formulations
 * - Handles material property discontinuities
 * - Integrates with the Summit material library
 * - Supports various element topologies and interpolation orders
 *
 * @note This class inherits from ElementSetBody and extends it with interface-specific functionality.
 */
class ElementSetInterfaceOneSided : public ElementSetBody {
  private:
    /**
     * Friendship with class ElementSetMonolithic
     */
    friend class ElementSetMonolithic;

  public:
    /**
     * @brief Primary constructor for one-sided interface element set
     *
     * Creates a new ElementSetInterfaceOneSided with the specified geometric and material properties.
     * This constructor initializes all necessary data structures for interface element computations.
     *
     * @param[in] dim Spatial dimension of the problem (2D or 3D)
     * @param[in] topo Topology identifier used to build the element set
     * @param[in] nElements Total number of interface elements in the set
     * @param[in] nen Total number of nodes per interface element
     * @param[in] nenLeft Number of nodes per element on the left side of the interface
     * @param[in] pOrder Polynomial interpolation order for shape functions
     * @param[in] matLabel Material label for the interface material (1-based index)
     * @param[in] matLabelLeft Material label for the left bulk material (1-based index)
     * @param[in] globalElementId Vector of global element indices for interface elements
     * @param[in] bip Interface type parameter: 1=interface, 2=parallel
     * @param[in] side Side specification for the interface (LEFT, RIGHT, or COMPLETE)
     *
     * @pre globalElementId.size() must equal nElements
     * @pre All material labels must be valid indices in the material library
     *
     * @throws std::exception if globalElementId.size() != nElements
     */
    ElementSetInterfaceOneSided(size_t dim,
                                size_t topo,
                                size_t nElements,
                                size_t nen,
                                size_t nenLeft,
                                size_t pOrder,
                                int matLabel,
                                int matLabelLeft,
                                std::vector<int> const& globalElementId,
                                short int bip,
                                SIDE side = summit::LEFT);

    /**
     * @brief Restart constructor from checkpoint data
     *
     * Reconstructs an ElementSetInterfaceOneSided object from previously saved checkpoint data.
     * This constructor is used for simulation restart capabilities.
     *
     * @param[in] checkpoint Pointer to the checkpoint object containing saved data
     * @param[in] name Name of the group within the checkpoint containing this element set's data
     *
     * @pre checkpoint must be a valid, opened checkpoint object
     * @pre name must correspond to an existing group in the checkpoint
     */
    ElementSetInterfaceOneSided(Checkpoint* checkpoint, const char* name);

    /**
     * @brief Copy constructor
     *
     * Creates a deep copy of an existing ElementSetInterfaceOneSided object.
     * All internal data structures and element connectivity are duplicated.
     *
     * @param[in] elementSetInterfaceOneSided Source object to copy from
     */
    ElementSetInterfaceOneSided(const ElementSetInterfaceOneSided& elementSetInterfaceOneSided);

    /**
     * @brief Virtual destructor
     *
     * Properly cleans up all dynamically allocated memory and resources
     * associated with the interface element set.
     */
    virtual ~ElementSetInterfaceOneSided();

  private:
    /**
     * Default constructor
     */
    ElementSetInterfaceOneSided();

    /**
     * Assignment operator
     * @param[in] a const reference to an ElementSetInterfaceOneSided object
     * @return a reference to an ElementSetInterfaceOneSided object
     */
    ElementSetInterfaceOneSided& operator=(ElementSetInterfaceOneSided const&);

  public:
    /**
     * @brief Compute the gradient of field u on the left side of the interface
     *
     * This method computes the spatial gradient of a field u at a specific quadrature point
     * on the left side of the interface element. The gradient is computed using the shape
     * function derivatives and the nodal values of the field.
     *
     * @param[in] e Element index within the element set
     * @param[in] q Quadrature point index within the element
     * @param[in] u Pointer to the nodal values of the field (size: nen * u_dim)
     * @param[in] u_dim Number of components per node (e.g., 1 for scalar, 3 for vector)
     * @param[out] Du Computed gradient tensor: \f$Du^L[i,j]=du^L_i/dX_j\f$, stored in ROW MAJOR format
     *
     * @pre e must be a valid element index (0 <= e < nelements)
     * @pre q must be a valid quadrature point index (0 <= q < nquad)
     * @pre u must point to valid memory of size nen * u_dim
     * @pre Du must point to valid memory of size dim * u_dim
     */
    void Gradient_L(elem_t e, quad_t q, real const* u, int u_dim, real* Du) const;

    /**
     * @brief Interpolate nodal field values to quadrature points
     *
     * This method interpolates values from nodes to quadrature points using the element
     * shape functions. Currently not implemented for interface elements as interpolation
     * is typically handled by the Region class.
     *
     * @param[in] nodal_field Field values defined at the nodes
     * @param[out] quad_field Field values interpolated to quadrature points
     *
     * @note This method is not yet implemented and may be moved to the Region class
     * @todo Implement interpolation functionality or remove if not needed
     */
    virtual void Interpolate(const NodalField<real>& nodal_field,
                             ElementQuadratureField<real>& quad_field) const;

    /**
     * Method in charge of averaging a field over a given set of elements
     * identified by a label in the mesh file. By default all the elements
     * in the entire mesh are considered.
     */
    /*virtual void
      AverageField(QuadratureField<real>& quad_field, std::vector<real>& sum, double&
      outputVolume);*/

    /**
     * Accesor to globalElementId
     */
    inline const std::vector<int>& globalElementId() const;

    /**
     * Accessor to the interpolation order
     */
    virtual size_t pOrder() const;

    /**
     * Accessor
     * @return the element type
     */
    virtual ElemType type() const;

    virtual int vtkType() const;

    /**
     * Accessor to the number of nodes of the simplex per element
     */
    virtual size_t nodes_simplex() const = 0;

    /**
     * Accessor
     * @return the number of quadrature points per element
     */
    virtual size_t nquad() const = 0;

    /**
     * Accessor
     * @return the number of nodes of the first order elements (ie 3 triangular, 4 tetrahedron)
     */
    virtual size_t nodes_1stOrder_left_element() const = 0;

    /**
     * Create the region that will be assiciated to the element_set
     * The "owner" is the WeakForm
     */
    virtual Region* newRegion(const REGION region,
                              MaterialLibrary const& material_lib,
                              const NodalField<real>& coordinates,
                              const Functor<real>* sourceTerm = 0) const;

    /**
     * Accessor
     * @return the label of the material of the interface material
     */
    int materialLabelLeft() const;

    /**
     * Get nodal shape function value at a quad point of a specific element
     * @param[in] e the element index
     * @param[in] q the quadrature point index
     * @param[in] a the shape function index (a.k.a. the nodal index)
     * @return the value of the shape function
     */
    inline real shape(elem_t e, quad_t q, lnode_t a) const;

    /**
     * Get nodal shape function value at a quad point of a specific element
     * @param[in] e the element index
     * @param[in] q the quadrature point index
     * @return the value of the shape function
     */
    inline const real* shape(elem_t e, quad_t q = quad_t(0)) const { return _shape.local(e, q); }

    /**
     * pseudo iterator
     * @return the index of the first left node
     */
    inline lnode_t leftNodeStart() const;

    /**
     * pseudo iterator
     * @return the index of the last left node
     */
    inline lnode_t leftNodeDone() const;

    inline size_t nenLeft() const;

    /**
     * Compute displacement jump
     */
    virtual void Jump(elem_t e,
                      quad_t q,
                      std::vector<real> const& ul,
                      std::vector<real>& jumpU,
                      const size_t* _dof_node = NULL) const;

    /**
     * Method that return the side (left or right)
     * @return the side
     */
    inline SIDE side() const { return (_side == RIGHT) ? _side : LEFT; }

    /**
     * Compute effective length scale of element, this is taken as the minimum
     * inscribed circle/sphere in the left and right elements.
     * @param xl local coordinates of the "butterfly" element
     */
    virtual real InRadiusElement(const real* xl) const;

    virtual real InRadiusElementLeft(const real* xl) const;

    /**
     * Method to compute the barycenter of an interface and not of the element
     * so compute the barycenter of the triangle (3D) or the edge (2D)
     * @param[in] e the local index of the interface
     * @param[in] xl the spatial coordinates of the nodes
     * @param[in,out] barycenter a vector that will contains the barycenter of the interface
     */
    virtual void barycenter(const size_t e, const real* xl, std::vector<real>& barycenter) const;

    /**
     * Method to compute the physical positions (X,Y,Z) of the Gauss points of a given element
     * @param[in] e the local index of the interface
     * @param[in] coordinates coordinates of all the nodes
     * @param[in,out] physCoord (X,Y,Z) coordinates of the Gauss points
     */
    void computePositionOfIntegrationPoints(const elem_t e,
                                            NodalField<real> const& coordinates,
                                            std::vector<real>& physCoord) const;

    /**
     * Method to compute the sparsity of a stiffness matrix over the halfbutterfly element set
     * @param[in] eqMap map local degree of freedom to a global equation number
     * @param[in] n_dof_node number of degrees of freedom associated with each node
     * @param[in] halfButterflyMissingWingEQN "missing wing" equation number
     * @param[out] sparsity sparsity of the stiffness matrix for this local processor
     */
    void ComputeSparsityPattern(const int * eqMap, int n_dof_node,
        ElementQuadratureField<int>& halfButterflyMissingWingEQN, 
        std::map<int, std::set<int> >& sparsity) const;

    virtual void WriteForRestart(Checkpoint* checkpoint,
                                 const char* name,
                                 const char* tag = nullptr) const;

    /*This guy is used for all other interface elements*/
    void BuildInterpolants(NodalField<real> const& coordinates,
                           std::vector<int> const& indicesVerticesInterface);

  protected:
    /**
     * Method to compute the physical positions (X,Y,Z) of the Gauss points of a given element
     * @param[in] xl local coordinates of all the nodes
     * @param[in,out] physCoord (X,Y,Z) coordinates of the Gauss points
     */
    void computePositionOfIntegrationPoints(const elem_t e,
                                            const real* xl,
                                            std::vector<real>& physCoord) const;

    /**
     * Method to build the arrays of shape functions and their spatial derivatives, as well as,
     * the jacobian; these quantities are evaluated at quadrature points and tabulated
     * @param[in] coordinates coordinates of all the nodes
     * @param[in] indicesVerticesInterface indices of the coordinates of the vertices of the
     *            interface for all the interface elements
     */
    /*This guy is used for linear interface elements*/
    void BuildInterpolants(NodalField<real> const& coordinates);

#if 0
        /**
         * Method to add element to the set. This method increases also _nelements
         * @param[in] idElementsNeighborInterface global ID of the coboundary elements (triangles)
         *             for each interface
         */
        void
            _addElements(std::vector<int> const & idElementsNeighborInterface);
#endif

    /**
     * Method to compute _RegionKey
     * @param[in] bip stands for b: bulk = 0, i: interface = 1, p: parallel = 2
     * @param[in] coordinates self explanatory
     */
    void computeRegionKey(const short int bip,
                          NodalField<real> const& coordinates,
                          std::vector<int> const& indicesVerticesInterface);

    /**
     * Method to compute the isoparametric coordinates of Gauss points from their physical
     * coordinates
     * @param[in]  number of Gauss points to compute. Trick for Simpson point in TriPTetQ
     *             (where nquad != _nquad)
     * @param[in]  polynomial order of the element
     * @param[in]  nodal coordinates local to the element
     * @param[in]  physical coordinates of the Gauss points
     * @param[out] isoparametric coordinates of the Gauss points
     */
    template <int tdim>
    void fromPhysical2IsoparametricCoordinates(const int nquad,
                                               const int pOrder,
                                               const double* xl,
                                               const double* physicalQuadCoordInterface,
                                               // inline definition as template
                                               std::vector<double>& isoParamQuadCoord) const;

    /**
     * Method to compute the isoparametric coordinates of Gauss points from their physical
     * coordinates for shell elements
     * i.e. triangular in 3D space (should give the same result as the previous function if the
     *  triangulr is in the plane XY --> dim template)
     * @param[in]  number of Gauss points to compute. Trick for Simpson point in TriPTetQ
     *             (where nquad != _nquad)
     * @param[in]  polynomial order of the element
     * @param[in]  nodal coordinates local to the element
     * @param[in]  physical coordinates of the Gauss points
     * @param[out] isoparametric coordinates of the Gauss points
     */
    // inline definition as template
    template <int tdim>
    void fromPhysical2IsoparametricCoordinates3DTriangular(
      const int nquad,
      const int pOrder,
      const double* xl,
      const double* physicalQuadCoordInterface,
      std::vector<double>& isoParamQuadCoord) const;


    /**
     * Method to compute the interface quadrature rule
     * @param[in]  polynomial order of the element
     * @param[in,out] weight of Gauss point
     * @param[in,out]  s1,s2,s3,s4 are the map coefficient (s4 only for 3D)
     * @param[in,out] the of the Gauss points
     */
    template <int Tdim>
    int fillInterfaceQuadratureRule(const int pOrder,
                                    std::vector<real>& weight,
                                    std::vector<real>& s1,
                                    std::vector<real>& s2,
                                    std::vector<real>& s3,
                                    std::vector<real>* s4 = NULL) const;


    /**
     * Method to compute the normal to the left elements in case of simplices
     * @param[in] xv vertices of the interface
     * @param[in,out] normal
     */
    template <int Tdim>
    void simplicesNormal(std::vector<real> const& xv, real normal[Tdim]) const;

    /**
     * Method to compute the Jacobian value on interface
     * Simplices: 2D interface --> area of the interface 1D interface --> length of the edge
     * @param[in] xv vertices of the interface
     */
    // Idim == Interface dim; Sdim == space dim
    // (They are different if triangular in 3D space for this case Idim=2, Sdim=3)
    template <int Idim, int Sdim>
    real simplicesInterfaceJacobian(std::vector<real> const& xv) const;
    // Default accessor when Idim==Sdim
    template <int Idim>
    real simplicesInterfaceJacobian(std::vector<real> const& xv) const;

    /**
     * Method to extrapolate from quadrature points to nodes on one element, mostly for
     * visualization
     * @param e the element index in the element set
     * @param quad_field, a ElementQuadratureField of real
     * @param local_nodal_field, will be the nodal values that have to be assemblied by the
     *        asseble method
     * @param local_weights, the weight for the assembly
     */
    virtual void ElementaryExtrapolate(const elem_t e,
                                       const ElementQuadratureField<real>& quad_field,
                                       std::vector<real>& local_nodal_field,
                                       std::vector<real>& local_weights) const;


    /**
     * Method to evaluate the shape functions at the quadrature point locations in the reference
     * configuration
     */
    virtual void ElementShape() = 0;

    /**
     * Method to compute the shape functions derivatives and the normals to the left element
     *     for each quadrature point of the interface
     * @param[in] xl nodal coordinates local to the element
     * @param[in,out] shape pointer to the first value of the first quadrature point of the element
     *                in the global shape function container
     * @param[in,out] dShape pointer to the first value of the first quadrature point of the element
     *                in the global shape function derivative container
     * @param[in,out] jac pointer to the first value of the first quadrature point of the element
     *                in the global jacobian container
     * @param[in,out] normal_L pointer to the first value of the first quadrature point of the
     *                element in the global outer left element normal container
     */
    virtual void ElementDShapeAndJac(
      std::vector<real> const& xl, real* shape, real* dShape, real* jac, real* normal_L) = 0;

    /**
     * Method to compute the shape functions and its derivatives and the normals to the left element
     *    for each quadrature point of the interface
     * @param[in] e element number needed to compute element data for 3D2Shell interface
     * @param[in] xl nodal coordinates local to the element
     * @param[in] xv vertices of the interface: needed to calculate the quadrature coordinates
     *            local to the element (physical space)
     * @param[in,out] shape pointer to the first value of the first quadrature point of the element
     *                in the global shape function container
     * @param[in,out] dShape pointer to the first value of the first quadrature point of the element
     *                in the global shape function derivative container
     * @param[in,out] jac pointer to the first value of the first quadrature point of the element
     *                in the global jacobian container
     * @param[in,out] normal_L pointer to the first value of the first quadrature point of the
     *                element in the global outer left element normal container
     */
    virtual void ElementShapeDShapeJacAndNormal(const size_t e,
                                                std::vector<real> const& xl,
                                                std::vector<real> const& xv,
                                                real* shape,
                                                real* dShape,
                                                real* jac,
                                                real* normal_L) = 0;

#if 0
        /**
         * Global ID of the coboundary (left and right) elements for each interface
         */
        std::vector<int> _idElementsNeighborInterface;
#endif

    /**
     * Interpolation polynomial order
     */
    size_t _pOrder;

    /**
     * Number of quadrature points per interface
     */
    size_t _nquad;

    /**
     * Number of nodes per element for the left element
     */
    size_t _nenLeft;

    /**
     * Material label of the left element
     */
    int _matLabelLeft;

    /**
     * global element id
     */
    std::vector<int> _globalElementId;

    /**
     * bip... technically more an ip
     * bip = 1: interface
     * bip = 2: parallel
     */
    short int _bip;

    /**
     * Side of the elements wrt the interface
     */
    SIDE _side;
};
}  // namespace summit

// get the inline definitions
#define summit_elements_element_set_interface_one_sided_icc
#include "element_set_interface_one_sided.icc"
#undef summit_elements_element_set_interface_one_sided_icc

#endif  // SUMMIT_ELEMENT_SET_INTERFACE_ONE_SIDED_H

// end of file
