feature: Extend Source method interface to support gradient-dependent source terms

This commit introduces a major enhancement to the reaction-diffusion material
system by extending the Source method interface to include gradient dependencies.
The new signature enables more sophisticated numerical formulations required for treatment
of advective cooling or strain gradient effects.

## Interface Changes

### Base Class (ReactionDiffusionMaterial)
- Removed old 7-parameter Source method signature
- Updated to new 11-parameter signature with gradient dependencies:
- Fixed tests of the Source terms
- Propagated the changes to the weak formulation class