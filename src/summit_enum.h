// -*- C++ -*-
//
// rrgroup
// summit development team
// massachusetts institute of technology
// (c) 2011-2012 all rights reserved
//

#ifndef SUMMIT_ENUM_H_
#define SUMMIT_ENUM_H_

namespace summit {

/**
 *  all possible formats that we can use to write checkpoints
 */
enum SUMMIT_CHECKPOINT_TYPE { HDF5_CHECKPOINT };

/**
 *  all possible sparse matrix formats that we can use
 */
enum SparseMatrixType {
    SPARSE_MATRIX_CRS,   // serial sparse CRS
    PSPARSE_MATRIX_CRS,  // parallel sparse CRS
    SPARSE_MATRIX_SKYLINE
};


/**
 * All possible types of body forces
 */
enum BodyForceType {
    BODY_FORCE_SPECIFIC,
    BODY_FORCE_SPECIFIC_PARAMETRIC,
    BODY_FORCE_VOLUMETRIC
};

/**
 * all possible solver types that we can use
 * note: not every solver works with every matrix, and
 * some solvers require external libraries
 */
enum SolverType {
    SPARSE_SOLVER_ADLIB,
    SPARSE_SOLVER_WSMP,
    SPARSE_SOLVER_INTELDSS,
    SPARSE_SOLVER_INTELPARDISO,
    SOL<PERSON>R_PETSC,     // can be not sparse ...
    SOLVER_PETSC_NCG  // no matrix for this one ...
};

enum SchemeType {
    SCHEME_MECHANICAL_STATIC,
    SCHEME_MECHANICAL_DYNAMIC,
    SCHEME_MECHANICAL_PETSC_NCG  // not really a scheme but difficult to do this in other way
};

/**
 * Formulation type enum.
 * It describes the possible choices of formulations supported by a
 * FunctionSpace.
 */
enum FormulationType {
    FORMULATION_CG = 0,     //< Continuous Galerkin formulation
    FORMULATION_DG = 1,     //< Discontinuous Galerkin formulation
    FORMULATION_PERIDG = 2  //< Peridynamic DG formulation
};

/**
 * Element type enum.
 * It describes the possible choices of element types supported by an
 * ElementSet.
 */
enum ElemType {
    ELEM_TET = 0,        //< Tetrahedron
    ELEM_TRI = 1,        //< Triangle
    ELEM_SHL = 2,        //< Triangular shell
    ELEM_LIN = 3,        //< Line (for BCs)
    INTERFACE = 4,       //< Interface
    HALF_BUTTERFLY = 5,  //< Half butterfly elements
    ELEM_SHL_LUB = 6,    //< Shell element to solve lubrication equation (will maybe disappear...)
    ELEM_QUAD = 7,       //< Quadrilateral
    ELEM_HEX = 8,        //< Hexahedral
    ELEM_TRI_AXI = 9,    //< Triangular element for axisymmetric analysis
    ELEM_MANIFOLD_TRIP = 10,  //< Triangular element to solve a manifold in a 3D surface
    ELEM_MANIFOLD_SEGP = 11,  //< Segment line element to solve a manifold in a 2D surface
    ELEM_TET_GMSH = 12,
    ELEM_TRI_GMSH = 13,
    ELEM_SHL_GMSH = 14,  //< Shell (tri or quad)
    ELEM_3D_GMSH = 15,   //< 3D (Tetra or Hexa)
    ELEM_SHL_LUB_GMSH = 16,
    ELEM_PLATE_RM = 17,                       //< Reissner-Mindlin plate. Uses gmsh.
    ELEM_SHL_RM = 103,                        // Reissner-Mindlin linear shell. requires gmsh.
    ELEM_SHL_RM_NONLINEAR = 104,              // Reissner-Mindlin nonlinear shell. requires gmsh.
    ELEM_MONO_INTERFACE = 18,                 //< Monolithic interface
    ELEM_MONO_INTERFACE_HALF_BUTTERFLY = 19,  //< Monolithic interface
    ELEM_BTF = 20,                            //< Torsion-free Kirchhoff-Love beam element
    ELEM_BKL = 21,                            //< Kirchhoff-Love beam element
    ELEM_BSR = 22                             //< Simo-Reissner beam element
};


/**
 * Element type enum.
 * It describes the possible choices of element types supported by a
 * ElementSet.
 */
enum ElemName {
    ELEM_SET_TETP = 0,  //< Tetrahedron
    ELEM_SET_TRIP = 1,  //< Triangle
    ELEM_SET_SHELLP = 2,
    ELEM_SET_TETP_TETP = 3,
    ELEM_SET_TRIP_TRIP = 4,
    ELEM_SET_INTERFACE_TETP = 5,
    ELEM_SET_INTERFACE_TRIP = 6,
    ELEM_SET_SHELLP_SHELLP = 7
};

/**
 * Element type enum.
 * It describes the possible choices to access to left, right and interface values of an
 * ElementSetShell
 */
enum SIDE { LEFT = 0, RIGHT = 1, INTER = 2, BULK = 3 };

template <SIDE side>
inline double signSide();

template <>
inline double signSide<LEFT>()
{
    return -1.0;
}

template <>
inline double signSide<RIGHT>()
{
    return 1.0;
}

template <SIDE side1, SIDE side2>
inline double signSide();

template <>
inline double signSide<LEFT, LEFT>()
{
    return 1.0;
}

template <>
inline double signSide<RIGHT, RIGHT>()
{
    return 1.0;
}

template <>
inline double signSide<LEFT, RIGHT>()
{
    return -1.0;
}

template <>
inline double signSide<RIGHT, LEFT>()
{
    return -1.0;
}

enum REGION {
    MECHANICS = 0,
    SEVF = 1,
    PERIDYNAMICS = 2,
    PERIDYNAMICS_GHOST = 3,
    LUBRICATION = 4,
    POISSONMANIFOLD = 5,
    EXTENDED_POISSONMANIFOLD = 6,
    EXTENDED_POISSONMANIFOLD_ARTIFICIAL_COMPRESSIBILITY = 7,
    BLACK_OIL_MODEL = 8,
    SINGLEPHASE_FLOW = 9,
    SINGLEPHASE_GAS_FLOW = 10,
    THERMAL_MODEL = 11,
    TRANSPORT = 12,
    HYDROMECHANICS = 13,
    EXTENDED_HYDROMECHANICS = 14,
    EXTENDED_HYDROMECHANICS_IMPLICIT_OPENING_DERIVATIVE = 15,
    FLUID = 16,
    CHANNEL_ADVECTION = 17,
    CHANNEL_HEAT = 18,
    REACTION_DIFFUSION = 19
};

enum PHASE { OIL = 0, WATER = 1, GAS = 2 };

enum BC_TYPE { BC_NEUMANN = 0, BC_DIRICHLET = 1, BC_PARADG = 2, BC_PERIODIC = 3 };

enum ACTIVATION_STATUS { ACTIVE = 0, NON_ACTIVE = 1 };
enum IN_ACTIVATION { IN_ACTIVATION = 0, NON_IN_ACTIVATION = 1 };

enum KIND_DOF { INTERNAL = 0, EXTERNAL = 1, BOUNDARY = 2 };

enum NEUMANN_TYPE { NEUMANN_ON_EDGE = 0, NEUMANN_ON_FACE = 1, NEUMANN_ON_3D = 2 };

enum DIRECTION { X = 0, Y = 1, Z = 2 };

enum SCALAR_TYPE { MIN = 0, MAX = 1 };

enum ELEMFIELD_OP {
    EOP_NODE = 0,  // nodal output
    EOP_MEAN = 1,
    EOP_MAX = 2,
    EOP_MIN = 3
};

/**
 * types of DG mindlin elements
 */
enum MindlinElementKind { MINDLIN_NONE, MINDLIN_PLATE, MINDLIN_LINEAR, MINDLIN_NONLINEAR };

/**
 * Norms of the solution error
 */
namespace norms {
enum NormType {
    ENERGY,
    L2,
    L2_SHELL_DISPLACEMENT,
    L2_SHELL_ROTATION,
    SHELL_BROKEN_H1_DISPLACEMENT,
    SHELL_BROKEN_H1_ROTATION,
    SHELL_L2_SCALED_SHEAR,
    SHELL_PENALTY_ENERGY_DISPLACEMENT,
    SHELL_PENALTY_ENERGY_ROTATION
};
}

/** GB: Define templated structure to allow Template Meta-Programming
 * on the dimension space (2 or 3) ie some if statement needed for 3D
 * are replaced at compilation and so are not evaluated at computation
 * PUT THESE THINGS ELSEWHERE?
 */
template <int X, int Y>
struct SameDim {
    enum { result = 0 };
};

template <int X>
struct SameDim<X, X> {
    enum { result = 1 };
};
}  // namespace summit
#endif /* SUMMIT_ENUM_H_ */
