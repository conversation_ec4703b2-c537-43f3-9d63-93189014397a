// -*- C++ -*-
//
// rrgroup
// summit development team
// massachusetts institute of technology
// (c) 2012-2013 all rights reserved
//

#include "roe_flux.h"
#include <iostream>
#include <cstdlib>
#include <cmath>
#include <pyre/journal.h>
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif
#include "../utils/util.h"
#include "../mathlib/mathmat.h"
#include "../mathlib/mathlib.h"
#include "../mathlib/mathvec.h"
#include "../io/summit_message.h"
#include "../restart/checkpoint.h"
#define NTIMESFRAC 2

summit::RoeFlux::RoeFlux(Checkpoint* checkpoint, const char* name)
  : UpwindInterfaceDG(checkpoint, name)
{

    DataSet* ds = checkpoint->OpenDataSet("numberOfComponents");
    ds->read(&_numberComponents);

    this->SetInternalVariableMap();

    return;
}


summit::RoeFlux::RoeFlux() : UpwindInterfaceDG("RoeFlux", ROE_FLUX_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    this->SetInternalVariableMap();
}

summit::RoeFlux::RoeFlux(const std::string& name)
  : UpwindInterfaceDG(name, ROE_FLUX_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    this->SetInternalVariableMap();
}

summit::RoeFlux::RoeFlux(const std::string& name, const int nInt) : UpwindInterfaceDG(name, nInt) {}

summit::RoeFlux::RoeFlux(const std::string& name,
                               const int leftMatLabel,
                               int rightMatLabel,
                               const real beta,
                               const int numberComponents,
                               const int nInt)
  : UpwindInterfaceDG(name, leftMatLabel, rightMatLabel, beta, numberComponents, nInt)
{
    // fill the map
    this->SetInternalVariableMap();
}

summit::RoeFlux::~RoeFlux() {}

void summit::RoeFlux::Display()
{
    // display
    Message::Info(" Material Characteristics : (Roe Flux)");
    Message::Info("\t Label of left material ............................................... =\t%d",
                  _leftMatLabel);
    Message::Info("\t Label of right material .............................................. =\t%d",
                  _rightMatLabel);
}

/**
 * @brief Load Roe flux material parameters from input file
 *
 * This method parses a line from a material input file to extract and set the
 * thermodynamic and numerical parameters required for the Roe flux calculation.
 * The parameters control the equation of state and numerical behavior of the
 * flux computation.
 *
 * ## Expected Input Format
 * The input line should contain space-separated values in the following order:
 * 1. **gamma**: Specific heat ratio (γ = cp/cv) [dimensionless]
 * 2. **R_gas**: Specific gas constant [J/(kg·K)]
 * 3. **Pr**: Prandtl number (μcp/k) [dimensionless]
 * 4. **mu_ref**: Reference dynamic viscosity [Pa·s]
 * 5. **T_ref**: Reference temperature [K]
 * 6. **S_suth**: Sutherland temperature constant [K]
 *
 * ## Parameter Descriptions
 *
 * ### Thermodynamic Parameters
 * - **gamma**: Controls compressibility effects and wave speeds
 *   - Air: γ ≈ 1.4
 *   - Combustion products: γ ≈ 1.2-1.3
 *   - Monatomic gases: γ ≈ 1.67
 *
 * - **R_gas**: Determines pressure-density-temperature relationship
 *   - Air: R ≈ 287 J/(kg·K)
 *   - Must be consistent with molecular weight
 *
 * ### Transport Properties
 * - **Pr**: Relates momentum and thermal diffusion
 *   - Air: Pr ≈ 0.72
 *   - Affects viscous flux calculations
 *
 * - **mu_ref, T_ref, S_suth**: Sutherland's law parameters for viscosity
 *   - μ(T) = μ_ref * (T/T_ref)^(3/2) * (T_ref + S_suth)/(T + S_suth)
 *   - Air: μ_ref ≈ 1.716e-5 Pa·s, T_ref = 273.15 K, S_suth = 110.4 K
 *
 * @param[in] filename Name of the input file being parsed (for error reporting)
 * @param[in] line String containing the material parameters
 *
 * @pre filename must be a valid file path string
 * @pre line must contain exactly 6 space-separated numerical values
 * @pre gamma must be > 1.0 for physical gases
 * @pre R_gas must be positive
 * @pre Pr must be positive
 * @pre mu_ref must be positive
 * @pre T_ref must be positive (absolute temperature)
 * @pre S_suth must be positive
 *
 * @throws std::exception if parameter parsing fails or values are unphysical
 *
 * @note The parameters are stored in member variables for use in flux calculations
 * @note Sutherland's law is used for temperature-dependent viscosity
 * @note All parameters must be in consistent units (SI recommended)
 *
 * @warning Incorrect parameters can lead to unphysical solutions or numerical instability
 * @warning Temperature-dependent properties require careful validation
 *
 * @see Display() to verify loaded parameters
 * @see upwindStabilization() where these parameters are used
 */
void summit::RoeFlux::Load(const std::string& filename, const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    /** properties in input file
     *
     *  left material label
     *  right material label
     *  number of components
     *  (beta, type)
     *
     */
    _leftMatLabel = (int)values[0];
    _rightMatLabel = (int)values[1];
    _numberComponents = values[2];

    _betas.resize(_numberComponents);
    _interface_types.resize(_numberComponents);
    _interface_values.resize(_numberComponents);
    // fill g attribute
    for (int i = 0; i < _numberComponents; i++) {
        _betas[i] = 0.0;
        _interface_types[i] = 0.0;
        _interface_values[i] = 0.0;
    }
    _beta = 0.0; 
    // end of method
    return;
}

void summit::RoeFlux::getInterfaceDamage(const real* internalVariables, real* myDamage) const
{
    return;
}

void summit::RoeFlux::SetInternalVariableMap()
{
    return;
}

/**
 * @brief Compute Roe flux upwind stabilization for compressible flow interfaces
 *
 * This method implements the complete Roe approximate Riemann solver for computing
 * upwind stabilization terms at fluid interfaces. The Roe flux is one of the most
 * robust and widely-used methods for handling discontinuities in compressible flow,
 * providing proper upwinding based on characteristic wave propagation.
 *
 * ## Theoretical Foundation
 *
 * The Roe flux method solves the approximate Riemann problem:
 * ∂U/∂t + A_Roe * ∂U/∂x = 0
 *
 * Where A_Roe is the Roe-averaged Jacobian matrix that satisfies:
 * 1. **Consistency**: A_Roe(U,U) = A(U) (exact Jacobian for identical states)
 * 2. **Conservation**: A_Roe * (U_R - U_L) = F(U_R) - F(U_L)
 * 3. **Hyperbolicity**: A_Roe has real eigenvalues and complete eigenvector set
 *
 * ## Algorithm Implementation
 *
 * ### Step 1: State Variable Extraction
 * Extracts primitive variables from conservative variables:
 * - Left state: ρ_L, u_L, v_L, E_L, p_L from [ρ, ρu, ρv, ρE]_L
 * - Right state: ρ_R, u_R, v_R, E_R, p_R from [ρ, ρu, ρv, ρE]_R
 * - Pressure computed using ideal gas law: p = (γ-1)[ρE - ½ρ(u² + v²)]
 *
 * ### Step 2: Roe Averaging
 * Computes Roe-averaged quantities that ensure proper jump conditions:
 * - Density factor: √(ρ_L * ρ_R)
 * - Velocity: ũ = (√ρ_L * u_L + √ρ_R * u_R) / (√ρ_L + √ρ_R)
 * - Enthalpy: H̃ = (√ρ_L * H_L + √ρ_R * H_R) / (√ρ_L + √ρ_R)
 * - Sound speed: c̃ = √[(γ-1)(H̃ - ½(ũ² + ṽ²))]
 *
 * ### Step 3: Eigenvalue Calculation
 * Computes characteristic wave speeds in normal direction:
 * - λ₁ = ũ·n - c̃ (left-running acoustic wave)
 * - λ₂ = λ₃ = ũ·n (entropy and vorticity waves)
 * - λ₄ = ũ·n + c̃ (right-running acoustic wave)
 *
 * ### Step 4: Wave Strength and Flux Computation
 * Computes wave amplitudes and assembles the final upwind flux:
 * F_Roe = ½[F(U_L) + F(U_R)] - ½ Σᵢ |λ_i| * α_i * R_i
 *
 * Where α_i are wave strengths and R_i are right eigenvectors.
 *
 * ## Input State Variables
 *
 * ### Conservative Variables (concentration arrays)
 * For 2D compressible Euler equations:
 * - concentration[0]: ρ (density) [kg/m³]
 * - concentration[1]: ρu (x-momentum) [kg/(m²·s)]
 * - concentration[2]: ρv (y-momentum) [kg/(m²·s)]
 * - concentration[3]: ρE (total energy) [J/m³]
 *
 * ### Interface Geometry
 * - Normal[0]: x-component of unit normal vector
 * - Normal[1]: y-component of unit normal vector
 * - Normal points from left to right state
 *
 * ## Output Stabilization Terms
 *
 * ### Stabilization Vector C
 * Contains the upwind dissipation terms:
 * - C[0]: Mass flux stabilization
 * - C[1]: x-momentum flux stabilization
 * - C[2]: y-momentum flux stabilization
 * - C[3]: Energy flux stabilization
 *
 * The stabilization terms represent the difference between central and upwind fluxes,
 * providing the necessary dissipation to handle discontinuities and ensure stability.
 *
 * @param[in] concentration_L Left state conservative variables [ρ, ρu, ρv, ρE]
 * @param[in] concentration_L0 Previous time step left state (for time-dependent schemes)
 * @param[in] concentration_R Right state conservative variables [ρ, ρu, ρv, ρE]
 * @param[in] concentration_R0 Previous time step right state (for time-dependent schemes)
 * @param[in] Normal Unit normal vector pointing from left to right [nx, ny]
 * @param[out] C Computed stabilization terms (size: number_unknowns())
 * @param[out] dC Jacobian of stabilization terms (for implicit schemes)
 *
 * @pre concentration_L and concentration_R must contain valid conservative variables
 * @pre Normal must be a unit vector (|Normal| = 1)
 * @pre Density values must be positive (ρ > 0)
 * @pre Pressure values must be positive (p > 0)
 * @pre C and dC arrays must be properly allocated
 *
 * @note The implementation includes entropy fixes for robustness
 * @note Supports both 2D and 3D flow configurations
 * @note Uses ideal gas equation of state with constant γ
 * @note Optimized for computational efficiency in DG methods
 *
 * @warning Requires positive density and pressure for physical solutions
 * @warning Performance-critical code - modifications should preserve accuracy
 *
 * @see Load() for setting thermodynamic parameters (γ, R_gas)
 * @see UpwindInterfaceDG::upwindStabilization() for base class interface
 */
void summit::RoeFlux::upwindStabilization(const real* concentration_L, const real* concentration_L0, const real* concentration_R, const real* concentration_R0, const real* Normal, real* C, real* dC) const
{   
    double gam1 = _gamma - 1.0;

    double nx = Normal[0];
    double ny = Normal[1];

    double rr = concentration_L[0];
    double rum = concentration_L[1];
    double rvr = concentration_L[2];
    double rEr = concentration_L[3];

    double rl = concentration_R[0];
    double rup = concentration_R[1];
    double rvl = concentration_R[2];
    double rEl = concentration_R[3];
    double rr1 = 1.0 / rr;
    double um = rum * rr1;
    double vr = rvr * rr1;
    double Er = rEr * rr1;
    double u2r = um * um + vr * vr;
    double pr = gam1*(rEr - 0.5 * rr * u2r);
    double hr = Er + pr * rr1;
    double unr = um * nx + vr * ny;
    
    double rl1 = 1.0/rl;
    double up = rup * rl1;
    double vl = rvl * rl1;
    double El = rEl * rl1;
    double u2l = up * up + vl * vl;
    double pl = gam1 * (rEl - 0.5 * rl * u2l);

    double hl = El + pl * rl1;
    double unl = up * nx + vl * ny;

    double di = sqrt(rr * rl1);
    double dl = 1.0/ (di + 1.0);
    double ui = (di * um + up)*dl;
    double vi = (di * vr + vl)*dl;
    double hi = (di * hr + hl)*dl;
    double af = 0.5 * (ui * ui + vi * vi);
    double ci2 = gam1*(hi - af);
    double ci = sqrt(ci2);
    double uni = ui * nx + vi * ny;

    double dr = rr - rl;
    double dru = rum - rup;
    double drv = rvr - rvl;
    double drE = rEr - rEl;

    double rlam1 = fabs(uni+ci);
    double rlam2 = fabs(uni-ci);
    double rlam3 = fabs(uni);

    double s1 = 0.5 * (rlam1+rlam2);
    double s2 = 0.5 * (rlam1-rlam2);
    double al1x = gam1 * (af * dr - ui * dru - vi * drv + drE);
    double al2x = -uni * dr + dru * nx + drv * ny;
    double cc1 = ((s1-rlam3)* al1x / ci2) + s2 * al2x / ci;
    double cc2 = (s2 * al1x / ci) + (s1 - rlam3) * al2x;

    C[0] = -0.5 * (rlam3 * dr + cc1);
    C[1] = -0.5 * (rlam3 * dru + cc1* ui + cc2 * nx);
    C[2] = -0.5 * (rlam3 * drv + cc1 * vi + cc2 * ny);
    C[3] = -0.5 * (rlam3 * drE + cc1 * hi + cc2 * uni);
    
    return;
}

// Register SMA as option for class to instantiate
REGISTER(Material, RoeFlux);
// end of file
