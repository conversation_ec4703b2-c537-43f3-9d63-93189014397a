#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion.h"
#include "../../../mathlib/mathlib.h"
#include "../../../io/summit_message.h"
#include "../../exponential/log_exp_mappings.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion(const std::string& name)
  : ReactionDiffusionMaterial(name, PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::~PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion() {}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);
    _rho = values[0];
    _heatCapacity_virgin = values[1];
    _heatCapacity_reacted = values[2];
    _thermalConductivity_virgin = values[3];
    _thermalConductivity_reacted = values[4];
    _OxygenDiffusivity_virgin = values[5];//these are not relevant
    _OxygenDiffusivity_reacted = values[6];//these are not relevant
    _RateConstant = values[7];
    _ActivationEnergy = values[8];
    _chemistry_modulus = values[9];
    _omega = values[10];
    _R_gas_constant = values[11];
    _R_max_reaction = values[12];
    _alpha_virgin = 3.0 * values[13];
    _alpha_reacted = 3.0 * values[14];
    _E_virgin = values[15];
    _E_reacted = values[16];
    _nu_virgin = values[17];
    _nu_reacted = values[18];
    _eta_0 = values[19];//these are not relevant
    _sigma_c = values[20];//these are not relevant
    _theta_ref = values[21];
    _c_ref = values[22];
    _mu_bar = values[23];
    _molar_density = values[24];// reciprocal of the activity!
    real dum1, dum2, dum3;
    this->_getMuFromC(_c_ref, _theta_ref,
                      _mu_ref, dum1, dum2, dum3);
    // for (int i=0;i<values.size();i++){
    //     std::cout << "Values of ["<< i <<"] is:" << values[i]<< std::endl;
    // }
    // all done


   

    real D;
    this->_getDFromTheta((0+_theta_ref), D);
    //real muDiffFactor = D * (q[0]+_c_ref) / (_R_gas_constant * (concentration0[0]+_theta_ref));
    real muDiffFactor_Est = D * (0+_c_ref) / (_R_gas_constant * (0+_theta_ref));
    real K_Est = 0.5*(_thermalConductivity_virgin+_thermalConductivity_reacted);
    rescale_factor = K_Est / muDiffFactor_Est;
    return;
}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::_getMuFromC(const real C,
                      const real theta,
                      real &Mu,
                      real &dMudC,
                      real &ddMudC2,
                      real & dMudtheta) const
{   
    Mu = _mu_bar + _R_gas_constant * theta * log(C/_molar_density);
    dMudC = _R_gas_constant * theta  / C;
    ddMudC2 =  -_R_gas_constant * theta / (C*C);
    dMudtheta = _R_gas_constant * log(C/_molar_density);
    return ;
}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::_getCFromMu(const real Mu,
                      const real theta,
                      real &C,
                      real &dC,
                      real &ddC) const
{   
    C = _molar_density * exp((Mu-_mu_bar)/(theta * _R_gas_constant));
    dC = C /(theta * _R_gas_constant);
    ddC = dC /(theta * _R_gas_constant);
    return ;
}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::_getDFromTheta(const real theta,
                      real &D) const
{   
    D = std::exp(-(3.9136/.3)*(1000.0/theta - 0.65) - 26.02);
    return ;
}


void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::Display()
{
    // Check material properties
    Message::Info("Thermo-Chemical Model of PillingBedworthSwellingHeatConductingEyringCreep:");
    Message::Info("\tDensity....... = %e", _rho);
    Message::Info("\tHeat Capacity Virgin....... = %e", _heatCapacity_virgin);
    Message::Info("\tHeat Capacity Reacted....... = %e", _heatCapacity_reacted);
    Message::Info("\tThermal Conductivity Virgin....... = %e", _thermalConductivity_virgin);
    Message::Info("\tThermal Conductivity Reacted....... = %e", _thermalConductivity_reacted);
    Message::Info("\tOxygenDiffusivity Virgin....... = %e", _OxygenDiffusivity_virgin);
    Message::Info("\tOxygenDiffusivity Reacted....... = %e", _OxygenDiffusivity_reacted);
    Message::Info("\tRate Constant....... = %e", _RateConstant);
    Message::Info("\tActivation Energy....... = %e", _ActivationEnergy);
    Message::Info("\tChemistry Modulus....... = %e", _chemistry_modulus);
    Message::Info("\tOmega....... = %e", _omega);
    Message::Info("\tUniversal Gas Constant....... = %e", _R_gas_constant);
    Message::Info("\tReaction Consumption....... = %e", _R_max_reaction);
    Message::Info("\tAlpha Virgin....... = %e", _alpha_virgin);
    Message::Info("\tAlpha Reacted....... = %e", _alpha_reacted);
    Message::Info("\tE Virgin....... = %e", _E_virgin);
    Message::Info("\tE Reacted....... = %e", _E_reacted);
    Message::Info("\tnu Virgin....... = %e", _nu_virgin);
    Message::Info("\tnu Reacted....... = %e", _nu_reacted);
    Message::Info("\tEta....... = %e", _eta_0);
    Message::Info("\tSigma c....... = %e", _sigma_c);
    Message::Info("\tTheta ref....... = %e", _theta_ref);
    Message::Info("\tC ref....... = %e", _c_ref);
    Message::Info("\tMu Bar....... = %e", _mu_bar);
    Message::Info("\tMu ref....... = %e", _mu_ref);
    Message::Info("\tMolar density....... = %e", _molar_density);

    real PartialPressureO2 = std::exp(  ( _mu_bar + _R_gas_constant * _theta_ref * log(_c_ref/_molar_density) ) /  (_R_gas_constant * _theta_ref) );
    Message::Info("\tOxygen Partial Pressure....... = %e", PartialPressureO2);
    // end of method
    return;
}

summit::real summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    return _thermalConductivity_virgin / (_rho * _heatCapacity_virgin);
}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::Constitutive(const real* concentration0,
                                                           const real* concentration,
                                                           const real* Dconcentration0,
                                                           const real* Dconcentration,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    int strain_dim = ndm * ndf;
    real CurrentVolumeFraction = q[2];
    real CurrentE = _E_virgin * (1.0-CurrentVolumeFraction) + _E_reacted * (CurrentVolumeFraction);
    real CurrentNu = _nu_virgin * (1.0-CurrentVolumeFraction) + _nu_reacted * (CurrentVolumeFraction);
    real CurrentLambda = CurrentE * CurrentNu / ((1. + CurrentNu) * (1. - 2. * CurrentNu));
    real CurrentMu = CurrentE / (2. * (1. + CurrentNu));
    real CurrentBulk = CurrentLambda + 2. / 3. * CurrentMu;
    real CurrentAlpha = _alpha_virgin * (1.0-CurrentVolumeFraction) + _alpha_reacted * (CurrentVolumeFraction);
    real CurrentHeatCapacity = _heatCapacity_virgin * (1.0-CurrentVolumeFraction) + _heatCapacity_reacted * (CurrentVolumeFraction);
    real CurrentThermalConductivity = _thermalConductivity_virgin * (1.0-CurrentVolumeFraction) + _thermalConductivity_reacted * (CurrentVolumeFraction);
    real CurrentOxygenDiffusivity = _OxygenDiffusivity_virgin * (1.0-CurrentVolumeFraction) + _OxygenDiffusivity_reacted * (CurrentVolumeFraction);
    real dCurrentE = _E_reacted - _E_virgin;
    real dCurrentNu = _nu_reacted - _nu_virgin;
    real dCurrentBulk = dCurrentE / (3.0*(1.0-2.0*CurrentNu)) + 2.0/3.0 * CurrentE * dCurrentNu / ((1.0*2.0*CurrentNu)*(1.0*2.0*CurrentNu));
    real dCurrentMu = dCurrentE / (2.0*(1.0+CurrentNu)) + CurrentE/(2.0*(1.0+CurrentNu)*(1.0+CurrentNu)) * dCurrentNu;
    real dCurrentAlpha = _alpha_reacted - _alpha_virgin;
    real dCurrentHeatCapacity = _heatCapacity_reacted - _heatCapacity_virgin;
    real dCurrentThermalConductivity = _thermalConductivity_reacted - _thermalConductivity_virgin;
    real dCurrentOxygenDiffusivity = _OxygenDiffusivity_reacted - _OxygenDiffusivity_virgin;
    real CurrentDeltaTheta = concentration[0];
    real CurrentTheta = CurrentDeltaTheta+_theta_ref;
    real CurrentPotential = _mu_ref + concentration[1];

    real CurrentConcentration, dCurrentConcentration, ddCurrentConcentration;
    this->_getCFromMu(CurrentPotential, CurrentTheta, CurrentConcentration, dCurrentConcentration, ddCurrentConcentration);
    real ConcentrationInc = CurrentConcentration - (q[0]+ _c_ref);
    real D;
    this->_getDFromTheta((concentration0[0]+_theta_ref), D);
    //real muDiffFactor = D * (q[0]+_c_ref) / (_R_gas_constant * (concentration0[0]+_theta_ref));
    real muDiffFactor = D * (q[0]+_c_ref) / (_R_gas_constant * (concentration0[0]+_theta_ref));
    real dDdMu = dCurrentConcentration * D / (_R_gas_constant * (concentration0[0]+_theta_ref));
    muDiffFactor *= rescale_factor;
    dDdMu *= rescale_factor;
    int c = 0;
    for (int dimension = 0; dimension < ndm; dimension++){
        //standard heat flux
        P[dimension + c * ndm] = CurrentThermalConductivity * Dconcentration[dimension + c * ndm];
        if(compute_tangents){
            tangent[(dimension + c * ndm) * strain_dim + dimension + c * ndm] = CurrentThermalConductivity;
        }
    }
    c = 1;
    for (int dimension = 0; dimension < ndm; dimension++){
        //standard heat flux
        P[dimension + c * ndm] = muDiffFactor * Dconcentration[dimension + 1 * ndm] ;        
        if(compute_tangents){
            tangent[(dimension + c * ndm) * strain_dim + dimension + c * ndm] = muDiffFactor;
        }
    }
    return;
}


summit::real summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::capacity(real const* internal, const int component) const
{   
    return 0.0;
}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::_setInternalVariableMap()
{
    SetLocationInInternalTable("Oxygen Content", 0, 1);
    SetLocationInInternalTable("Temperature", 1, 1);
    SetLocationInInternalTable("Volume Fraction", 2, 1);
    SetLocationInInternalTable("Diffusion Heating", 3, 1);
    SetLocationInInternalTable("Fe", 4, 9);
    SetLocationInInternalTable("FeOld", 13, 9);
    SetLocationInInternalTable("Thermoelastic Forcing", 22, 1);
    SetLocationInInternalTable("Reaction Heating", 23, 1);
    // all done
    return;
}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{
    int strain_dim = ndf * ndf;
    real CurrentVolumeFraction = q[2];
    real CurrentE = _E_virgin * (1.0-CurrentVolumeFraction) + _E_reacted * (CurrentVolumeFraction);
    real CurrentNu = _nu_virgin * (1.0-CurrentVolumeFraction) + _nu_reacted * (CurrentVolumeFraction);
    real CurrentLambda = CurrentE * CurrentNu / ((1. + CurrentNu) * (1. - 2. * CurrentNu));
    real CurrentMu = CurrentE / (2. * (1. + CurrentNu));
    real CurrentBulk = CurrentLambda + 2. / 3. * CurrentMu;
    real CurrentAlpha = _alpha_virgin * (1.0-CurrentVolumeFraction) + _alpha_reacted * (CurrentVolumeFraction);
    real CurrentHeatCapacity = _heatCapacity_virgin * (1.0-CurrentVolumeFraction) + _heatCapacity_reacted * (CurrentVolumeFraction);
    real CurrentThermalConductivity = _thermalConductivity_virgin * (1.0-CurrentVolumeFraction) + _thermalConductivity_reacted * (CurrentVolumeFraction);
    real CurrentOxygenDiffusivity = _OxygenDiffusivity_virgin * (1.0-CurrentVolumeFraction) + _OxygenDiffusivity_reacted * (CurrentVolumeFraction);
    real dCurrentE = _E_reacted - _E_virgin;
    real dCurrentNu = _nu_reacted - _nu_virgin;
    real dCurrentBulk = dCurrentE / (3.0*(1.0-2.0*CurrentNu)) + 2.0/3.0 * CurrentE * dCurrentNu / ((1.0*2.0*CurrentNu)*(1.0*2.0*CurrentNu));
    real dCurrentMu = dCurrentE / (2.0*(1.0+CurrentNu)) + CurrentE/(2.0*(1.0+CurrentNu)*(1.0+CurrentNu)) * dCurrentNu;
    real dCurrentAlpha = _alpha_reacted - _alpha_virgin;
    real dCurrentHeatCapacity = _heatCapacity_reacted - _heatCapacity_virgin;
    real dCurrentThermalConductivity = _thermalConductivity_reacted - _thermalConductivity_virgin;
    real dCurrentOxygenDiffusivity = _OxygenDiffusivity_reacted - _OxygenDiffusivity_virgin;
    real CurrentDeltaTheta = concentration[0];
    real OldDeltaTheta = concentration0[0];
    real OldTheta = OldDeltaTheta+_theta_ref;
    real CurrentTheta = CurrentDeltaTheta+_theta_ref;
    real CurrentPotential = _mu_ref + concentration[1];

    real CurrentConcentration, dCurrentConcentration, ddCurrentConcentration;
    this->_getCFromMu(CurrentPotential, CurrentTheta, CurrentConcentration, dCurrentConcentration, ddCurrentConcentration);
    real ConcentrationInc = CurrentConcentration - (q[0]+ _c_ref);
    real D;
    this->_getDFromTheta((concentration0[0]+_theta_ref), D);
    real muDiffFactor = D * (q[0]+_c_ref) / (_R_gas_constant * (concentration0[0]+_theta_ref));

    real Mu, dMudC,ddMudC2,dMudtheta;
    real Cest = (q[0]+ _c_ref);
    // if(Cest<_c_ref){
    //     Cest = _c_ref;
    // }
    this->_getMuFromC(Cest, (concentration0[0]+_theta_ref), Mu, dMudC,ddMudC2,dMudtheta);//old quantities

    // transient terms always required
    df[0] = - (_rho * CurrentHeatCapacity + CurrentTheta * CurrentAlpha * CurrentAlpha * CurrentBulk) / dt[0];
    f[0] = df[0] * (concentration[0] - concentration0[0]);
    //undo the rescaling
    df[1 * 2 + 1] = - 1.0 / dt[0] * (1.0/dMudC) * rescale_factor;
    f[1] = df[1 * 2 + 1] * (concentration[1]-concentration0[1]) ;
    f[1] += dMudtheta/dMudC * (concentration[0] - concentration0[0])/dt[0] * rescale_factor;// this is the temperature potential modulus
    df[0 * 2 + 1] += dMudtheta/(dMudC*dt[0]);//this is the consistent linearization, I hope!


    real x = CurrentConcentration /_c_ref - 100.0;
    real dXdC = 1/_c_ref;

    real sigmoid = 0;
    real dSigmoid = 0;
    if(x>-30){
        sigmoid = 1.0/(1.0+exp(-x));
        dSigmoid = sigmoid*sigmoid*exp(-x);
    }
    if(x>30){
        sigmoid = 1.0;
        dSigmoid = 0.0;
    }
    dSigmoid *= dXdC;


    //std::cout << "sigmoid: "<< sigmoid << std::endl;
    //explicit integration of the source term
    real S = _RateConstant * sigmoid * CurrentConcentration;
    real dS = _RateConstant * (dSigmoid * CurrentConcentration + sigmoid) * dCurrentConcentration;

    if(S<0){
        std::cout << "S: " << S <<std::endl;
        std::cout << "(q[0]+_c_ref): " << (q[0]+_c_ref) <<std::endl;
    }
    q[2] += dt[0]*(S/_R_max_reaction);
    if(q[2]>1){
        S -= (q[2]-1.0)/dt[0]*_R_max_reaction;
        q[2] = 1.0;
    }
    f[1] -= S * rescale_factor;// got to remember to rescale
    df[1 * 2 + 1] -= dS * rescale_factor;

    // todo: try something like this, no source terms if c<cref
    // normal source terms if c>10*cref
    // smooth interpolation between these two (logistic curve)
    // we cant have big source terms with no C, big issue

    //update state variables
    q[0] = CurrentConcentration - _c_ref;
    q[1] = CurrentDeltaTheta;
    return;
}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    return;
}

int summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::number_unknowns() const{
    return 2;
}

real summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion::bulkModulus(real const* internal) const
{
    return _thermalConductivity_virgin;
}
// end of file
