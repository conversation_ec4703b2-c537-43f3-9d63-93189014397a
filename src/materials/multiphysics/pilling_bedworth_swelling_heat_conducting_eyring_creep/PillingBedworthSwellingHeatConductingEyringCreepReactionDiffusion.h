#ifndef SUMMIT_PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion_H
#define SUMMIT_PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion_H

#include "../../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion
                                         // 0 homogeneous reactions
#define PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion_NUMBER_INTERNAL_VARIABLES 25

namespace summit {

/**
 * Class for swelling, pilling bedworth, heat conducting, elastically deforming, inelastically creaping, chemically reacting, material behavior with temperature dependent diffusivity and viscosity and volume fraction dependent elastic moduli and swelling and diffusivity. Widely adopted for the analysis of silicon carbides, zirconium carbides as well as zirconium diborides, among other UHTCs
 */
class PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion : public ReactionDiffusionMaterial {
  public:
    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion(const std::string& name);

    /**
     * Destructor
     */
    virtual ~PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion(const PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion& operator=(const PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const;

    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;
  
      /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 

    real getMuRef() const {return _mu_ref;}
  protected: 

   /**
     * Method to get Mu
     */
    void _getMuFromC(const real C,
                      const real theta,
                      real &Mu,
                      real &dMudC,
                      real &ddMudC2,
                      real & dMudtheta) const;
    /**
     * Method to get D from theta
     */
    void _getDFromTheta(const real theta,
                      real &D) const;

    /**
     * Method to get C from Mu
     */
    void _getCFromMu(const real Mu,
                      const real theta,
                      real &C,
                      real &dC,
                      real &ddC) const;

    real _rho;
    real _heatCapacity_virgin;
    real _heatCapacity_reacted;
    real _thermalConductivity_virgin;
    real _thermalConductivity_reacted;
    real _OxygenDiffusivity_virgin;
    real _OxygenDiffusivity_reacted;
    real _RateConstant;
    real _ActivationEnergy;
    real _chemistry_modulus;
    real _omega;
    real _R_gas_constant;
    real _R_max_reaction;
    real _alpha_virgin;
    real _alpha_reacted;
    real _E_virgin;
    real _E_reacted;
    real _nu_virgin;
    real _nu_reacted;
    real _eta_0;
    real _sigma_c;
    real _theta_ref;
    real _c_ref;
    real _mu_bar;
    real _molar_density;
    real _mu_ref;
    real rescale_factor;

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
