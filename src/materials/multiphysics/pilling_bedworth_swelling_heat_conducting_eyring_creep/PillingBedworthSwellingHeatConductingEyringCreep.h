/**
 * @file PillingBedworthSwellingHeatConductingEyringCreep.h
 * @brief Complex multiphysics material model for ultra-high temperature ceramics
 * <AUTHOR> Development Team
 * @date 2011-2023
 *
 * This file contains the PillingBedworthSwellingHeatConductingEyringCreep class which
 * implements a comprehensive multiphysics material model for ultra-high temperature
 * ceramics (UHTCs) including silicon carbides, zirconium carbides, and zirconium diborides.
 */

#ifndef SUMMIT_PILLING_BEDWORTH_SWELLING_HEAT_CONDUCTING_EYRING_CREEP_H
#define SUMMIT_PILLING_BEDWORTH_SWELLING_HEAT_CONDUCTING_EYRING_CREEP_H

#include "../../mechanical_material.h"

namespace summit {

class Checkpoint;

/**
 * @brief Complex multiphysics material model for ultra-high temperature ceramics
 *
 * The PillingBedworthSwellingHeatConductingEyringCreep class implements a comprehensive
 * multiphysics material model that combines multiple physical phenomena relevant to
 * ultra-high temperature ceramics (UHTCs). This model is widely adopted for the analysis
 * of silicon carbides, zirconium carbides, and zirconium diborides.
 *
 * Key physical phenomena included:
 * - **Pilling-Bedworth swelling**: Volume changes due to oxidation reactions
 * - **Heat conduction**: Temperature-dependent thermal diffusivity
 * - **Elastic deformation**: Volume fraction dependent elastic moduli
 * - **Eyring creep**: Inelastic deformation with temperature-dependent viscosity
 * - **Chemical reactions**: Coupled chemical-mechanical behavior
 * - **Temperature dependence**: Material properties vary with temperature
 * - **Volume fraction effects**: Properties depend on constituent volume fractions
 *
 * @note This is a highly specialized material model for extreme temperature applications
 * @note Requires careful parameter calibration for specific UHTC materials
 */
class PillingBedworthSwellingHeatConductingEyringCreep : public MechanicalMaterial {
  public:
    /**
     * Constructor (default)
     */
    PillingBedworthSwellingHeatConductingEyringCreep();

    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    PillingBedworthSwellingHeatConductingEyringCreep(const std::string& name);

    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     * @param[in] number of internal variables
     */
    PillingBedworthSwellingHeatConductingEyringCreep(const std::string& name, int numIntVars);

    /**
     * @brief Constructor with material properties
     *
     * Creates a PillingBedworthSwellingHeatConductingEyringCreep material with specified
     * basic mechanical properties. Additional multiphysics parameters must be set
     * separately through parameter loading methods.
     *
     * @param[in] rho Material density [kg/m³]
     * @param[in] E Young's modulus [Pa]
     * @param[in] nu Poisson's ratio [-]
     * @param[in] numIntVars Number of internal variables (default: 22 for full multiphysics)
     * @param[in] mode Analysis mode: 1=plane stress, 2=plane strain, 3=3D (default: 2)
     *
     * @pre rho must be positive
     * @pre E must be positive
     * @pre nu must be in range (-1, 0.5) for physical materials
     * @pre numIntVars must be sufficient for the multiphysics phenomena (minimum 22)
     *
     * @note The default 22 internal variables accommodate all multiphysics phenomena
     */
    PillingBedworthSwellingHeatConductingEyringCreep(const real rho, const real E, const real nu, int numIntVars = 22, const int mode = 2);

    PillingBedworthSwellingHeatConductingEyringCreep(Checkpoint* checkpoint, const char* name);
    /**
     * Destructor
     */
    virtual ~PillingBedworthSwellingHeatConductingEyringCreep();

    /**
     * Method to display the material parameters to the output channel
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    PillingBedworthSwellingHeatConductingEyringCreep(const PillingBedworthSwellingHeatConductingEyringCreep&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    PillingBedworthSwellingHeatConductingEyringCreep& operator=(const PillingBedworthSwellingHeatConductingEyringCreep&);

  public:
    /**
     * Method to return density stored in internal variables array
     * @param[in] pointer to internal variables array
     */
    virtual real density(real const* internal) const;
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Get the mass damping factor
     * @param[in] q quadrature point
     */
    virtual real massDampingFactor(real const* q) const;

    /**
     * Method to compute the critical wavespeed
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual double Celerity(const real* Fn, const real* q, const int ndm) const;

    /**
     * Constitutive law, all tensors are passed in ROW MAJOR
     * @param u0 the interpolation of field u0 at quadrature point
     * @param u1 the interpolation of field u1 at quadrature point
     * @param Du0 the previous deformation gradient
     * @param Du1 the current deformation gradient
     * @param P the first Piola Kirchhoff stress
     * @param q the internal variables
     * @param tangent the tangent dP/dF
     * @param dt the timestep
     * @param ndf the degree of freedom per node
     * @param ndm the dimension of the computational domain
     * @param compute_tangents the flag for computing the tangent
     * @param artVisc_interface_activate the flag for artificial viscosity
     */
    virtual void Constitutive(const real* u0,
                              const real* u1,
                              const real* Du0,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real dt,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const;

    virtual MaterialType GetMaterialType() const { return MATERIAL_PillingBedworthSwellingHeatConductingEyringCreep; }

    // Write for Restart with Checkpoint
    virtual void WriteForRestart(Checkpoint* checkpoint,
                                 const char* name,
                                 const char* tag = nullptr) const;

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void SetInternalVariableMap();

    static Register<Material, PillingBedworthSwellingHeatConductingEyringCreep> reg;

  protected:
    /**
     * Computes the the viscosity coefficient at a given temperature
     */
    void _getEtaFromTheta(const real theta,
                      real &D) const;

    /**
     * Computes the strength relation for viscoelastic flow
     */
    void _creepStrengthRelation(const real f,
                      const real &ratio,
                      const real &constants,
                      real &error,
                      real &Derror) const;
    /**
     * Initializes the Newton Raphson solver
     */
    void _InitGuess(const real &ratio,
                      const real &constants,
                      real &f) const;
    
    /**
     * Local newton raphson solver for the flow
     */
    void _LocalNewtonSolver(const real &ratio,
                      const real &constants,
                      real &F) const;
    /**
     * Mode: 0 = plane stress, 1 = plain strain, 2 = general 3D
     */
    int _mode;
    real _rho;
    real _heatCapacity_virgin;
    real _heatCapacity_reacted;
    real _thermalConductivity_virgin;
    real _thermalConductivity_reacted;
    real _OxygenDiffusivity_virgin;
    real _OxygenDiffusivity_reacted;
    real _RateConstant;
    real _ActivationEnergy;
    real _chemistry_modulus;
    real _Pilling_Bedworth;
    real _R_gas_constant;
    real _R_max_reaction;
    real _alpha_virgin;
    real _alpha_reacted;
    real _E_virgin;
    real _E_reacted;
    real _nu_virgin;
    real _nu_reacted;
    real _sigma_c;
    real _theta_ref;
    real _eta_act;
    real _eta_const;
    real SolverTolerance = 1e-12;

  public:
    /**
     * @brief Public accessor for the viscosity calculation
     * 
     * @details Provides external access to the temperature-dependent viscosity calculation
     * while maintaining encapsulation of the implementation details.
     * 
     * @param[in] theta Current temperature (K)
     * @param[out] eta Computed viscosity coefficient (Pa·s)
     */
    void getEtaFromTheta(const real theta, real &eta) const {
        this->_getEtaFromTheta(theta, eta);
    }

    /**
     * @brief Public accessor for the viscosity calculation
     * 
     * @details Provides external access to the temperature-dependent viscosity calculation
     * while maintaining encapsulation of the implementation details.
     * 
     * @param[in] theta Current temperature (K)
     * @param[out] eta Computed viscosity coefficient (Pa·s)
     */
    void creepStrengthRelation(const real f,
                      const real &ratio,
                      const real &constants,
                      real &error,
                      real &Derror) const {
        this->_creepStrengthRelation(f, ratio, constants, error, Derror);
    }
};
}  // namespace summit

#endif
