#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic.h"
#include "../../../mathlib/mathlib.h"
#include "../../../io/summit_message.h"
#include "../../exponential/log_exp_mappings.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic(const std::string& name)
  : ReactionDiffusionMaterial(name, PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic::~PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic() {}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);
    _rho = values[0];
    _heatCapacity_virgin = values[1];
    _heatCapacity_reacted = values[2];
    _thermalConductivity_virgin = values[3];
    _thermalConductivity_reacted = values[4];
    _DoesItReact = values[5];
    _chemistry_modulus = values[6];
    _Pilling_Bedworth = values[7];
    _R_gas_constant = values[8];
    _R_max_reaction = values[9];
    _alpha_virgin = 3.0 * values[10];
    _alpha_reacted = 3.0 * values[11];
    _E_virgin = values[12];
    _E_reacted = values[13];
    _nu_virgin = values[14];
    _nu_reacted = values[15];
    _D_act = values[16];
    _D_const = values[17];
    _theta_ref = values[18];
    _c_ref = values[19];
    if(values.size()>20){
        _IncludeThermalAnalysis = values[20];
    }
    _scale_factor =1e9;
    // all done
    return;
}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic::Display()
{
    // Check material properties
    Message::Info("Macroscopic Thermo-Chemical Model of Pilling-Bedworth Swelling, Heat Conducting, and Eyring Creep Material:");
    Message::Info("\tDensity....... = %e", _rho);
    Message::Info("\tHeat Capacity Virgin....... = %e", _heatCapacity_virgin);
    Message::Info("\tHeat Capacity Reacted....... = %e", _heatCapacity_reacted);
    Message::Info("\tThermal Conductivity Virgin....... = %e", _thermalConductivity_virgin);
    Message::Info("\tThermal Conductivity Reacted....... = %e", _thermalConductivity_reacted);
    Message::Info("\tRate Constant....... = %e", _RateConstant);
    Message::Info("\tIsItReact....... = %e", _DoesItReact);
    Message::Info("\tChemistry Modulus....... = %e", _chemistry_modulus);
    Message::Info("\tJacobian Pilling Bedworth....... = %e", _Pilling_Bedworth);
    Message::Info("\tUniversal Gas Constant....... = %e", _R_gas_constant);
    Message::Info("\tReaction Consumption....... = %e", _R_max_reaction);
    Message::Info("\tAlpha Virgin....... = %e", _alpha_virgin);
    Message::Info("\tAlpha Reacted....... = %e", _alpha_reacted);
    Message::Info("\tE Virgin....... = %e", _E_virgin);
    Message::Info("\tE Reacted....... = %e", _E_reacted);
    Message::Info("\tnu Virgin....... = %e", _nu_virgin);
    Message::Info("\tnu Reacted....... = %e", _nu_reacted);
    Message::Info("\tDiffusion Activation Energy....... = %e", _D_act);//9.0830e+04
    Message::Info("\tDiffusion Constant....... = %e", _D_const);//1.6673e-11
    Message::Info("\tTheta ref....... = %e", _theta_ref);
    Message::Info("\tC ref....... = %e", _c_ref);
    std::cout << "_IncludeThermalAnalysis: " << _IncludeThermalAnalysis << std::endl;
    // Message::Info("\tAre We Running Thermal Mode....... = %s", _IncludeThermalAnalysis);
    
    // end of method
    return;
}

summit::real summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    return _thermalConductivity_virgin / (_rho * _heatCapacity_virgin);
}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic::Constitutive(const real* concentration0,
                                                           const real* concentration,
                                                           const real* Dconcentration0,
                                                           const real* Dconcentration,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    int strain_dim = ndm * ndf;
    real CurrentVolumeFraction = q[2];
    real CurrentE = _E_virgin * (1.0-CurrentVolumeFraction) + _E_reacted * (CurrentVolumeFraction);
    real CurrentNu = _nu_virgin * (1.0-CurrentVolumeFraction) + _nu_reacted * (CurrentVolumeFraction);
    real CurrentLambda = CurrentE * CurrentNu / ((1. + CurrentNu) * (1. - 2. * CurrentNu));
    real CurrentMu = CurrentE / (2. * (1. + CurrentNu));
    real CurrentBulk = CurrentLambda + 2. / 3. * CurrentMu;
    real CurrentAlpha = _alpha_virgin * (1.0-CurrentVolumeFraction) + _alpha_reacted * (CurrentVolumeFraction);
    real CurrentHeatCapacity = _heatCapacity_virgin * (1.0-CurrentVolumeFraction) + _heatCapacity_reacted * (CurrentVolumeFraction);
    real CurrentThermalConductivity = _thermalConductivity_virgin * (1.0-CurrentVolumeFraction) + _thermalConductivity_reacted * (CurrentVolumeFraction);
    real dCurrentE = _E_reacted - _E_virgin;
    real dCurrentNu = _nu_reacted - _nu_virgin;
    real dCurrentBulk = dCurrentE / (3.0*(1.0-2.0*CurrentNu)) + 2.0/3.0 * CurrentE * dCurrentNu / ((1.0*2.0*CurrentNu)*(1.0*2.0*CurrentNu));
    real dCurrentMu = dCurrentE / (2.0*(1.0+CurrentNu)) + CurrentE/(2.0*(1.0+CurrentNu)*(1.0+CurrentNu)) * dCurrentNu;
    real dCurrentAlpha = _alpha_reacted - _alpha_virgin;
    real dCurrentHeatCapacity = _heatCapacity_reacted - _heatCapacity_virgin;
    real dCurrentThermalConductivity = _thermalConductivity_reacted - _thermalConductivity_virgin;
    real CurrentDeltaTheta, CurrentConcentration, ConcentrationInc;
    if(_IncludeThermalAnalysis){
        CurrentDeltaTheta = concentration[0];// component 0 is temperature in this mode
        CurrentConcentration = concentration[1] + _c_ref;
        ConcentrationInc = (concentration[1] - concentration0[1]);
    }else{
        CurrentDeltaTheta = 0; // set to the default _theta_ref
        CurrentConcentration = concentration[0] + _c_ref;// if no heat transfer, indexes are all 0!!
        ConcentrationInc = (concentration[0] - concentration0[0]);
    }
    real CurrentTheta = CurrentDeltaTheta+_theta_ref;
    

    real CurrentOxygenDiffusivity;
    this->_getDFromTheta(CurrentTheta, CurrentOxygenDiffusivity);
    int c = 0; // which component of the problem are we dealing with
    if(_IncludeThermalAnalysis){
        for (int dimension = 0; dimension < ndm; dimension++){
            //standard heat flux
            P[dimension + c * ndm] = CurrentThermalConductivity * Dconcentration[dimension + c * ndm];
            //derivative of above with respect to Dconcentration
            if(compute_tangents){
                tangent[(dimension + c * ndm) * strain_dim + dimension + c * ndm] = CurrentThermalConductivity;
                // dPdu[(dimension + c * ndm) * ndf + c] += _dkdt * Dconcentration[dimension + c * ndm];
            }
        }
        c = 1; // increment component of the problem, so that chemistry goes into second slot
    }
    
    // rescaling was suggested as a means to make these systems well-conditioned
    // here we scale the diffusivity by exactly CurrentThermalConductivity / CurrentOxygenDiffusivity
    // then in the transient term, we have to un-rescale this
    // in the future we should just nondimensionalize properly or use
    // PETSc's preconditioners that automatically rescale
    // this became way too complicated and we should never do this ever again
    real RescaledCurrentOxygenDiffusivity = CurrentThermalConductivity;
    q[3] = 0.0;
    for (int dimension = 0; dimension < ndm; dimension++){
        //standard heat flux
        P[dimension + c * ndm] = RescaledCurrentOxygenDiffusivity * Dconcentration[dimension + c * ndm] ;        
        if(compute_tangents){
            tangent[(dimension + c * ndm) * strain_dim + dimension + c * ndm] = RescaledCurrentOxygenDiffusivity;
        }
        q[3]+=(Dconcentration0[dimension + 1 * ndm]) * (Dconcentration0[dimension + c * ndm]);
    }

    //protect against division by zero here
    real TOL = 1e-12;
    if(q[3]>TOL && CurrentConcentration>TOL){
        q[3] *= CurrentOxygenDiffusivity * _R_gas_constant * _theta_ref / CurrentConcentration;
    }else{
        q[3] = 0.0;
    }
    return;
}


summit::real summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic::capacity(real const* internal, const int component) const
{   
    // not needed because we integrate with the implicit integrator
    return 0.0;
}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic::_setInternalVariableMap()
{
    SetLocationInInternalTable("Oxygen Content", 0, 1); // for transfer to mechanics
    SetLocationInInternalTable("Temperature", 1, 1); // for transfer to mechanics
    SetLocationInInternalTable("Volume Fraction", 2, 1); // for transfer to mechanics
    SetLocationInInternalTable("Diffusion Heating", 3, 1); // typically very small, useful for plotting
    SetLocationInInternalTable("Fe", 4, 9); // we get this from mechanics
    SetLocationInInternalTable("FeOld", 13, 9); // we get this from mechanics
    SetLocationInInternalTable("Thermoelastic Forcing", 22, 1); // for exchanges with mechanics
    SetLocationInInternalTable("Reaction Heating", 23, 1); // for transfer to mechanics
    // all done
    return;
}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic::Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{
    real CurrentVolumeFraction = q[2];
    real CurrentE = _E_virgin * (1.0-CurrentVolumeFraction) + _E_reacted * (CurrentVolumeFraction);
    real CurrentNu = _nu_virgin * (1.0-CurrentVolumeFraction) + _nu_reacted * (CurrentVolumeFraction);
    real CurrentLambda = CurrentE * CurrentNu / ((1. + CurrentNu) * (1. - 2. * CurrentNu));
    real CurrentMu = CurrentE / (2. * (1. + CurrentNu));
    real CurrentBulk = CurrentLambda + 2. / 3. * CurrentMu;
    real CurrentAlpha = _alpha_virgin * (1.0-CurrentVolumeFraction) + _alpha_reacted * (CurrentVolumeFraction);
    real CurrentHeatCapacity = _heatCapacity_virgin * (1.0-CurrentVolumeFraction) + _heatCapacity_reacted * (CurrentVolumeFraction);
    real CurrentThermalConductivity = _thermalConductivity_virgin * (1.0-CurrentVolumeFraction) + _thermalConductivity_reacted * (CurrentVolumeFraction);
    real dCurrentE = _E_reacted - _E_virgin;
    real dCurrentNu = _nu_reacted - _nu_virgin;
    real dCurrentBulk = dCurrentE / (3.0*(1.0-2.0*CurrentNu)) + 2.0/3.0 * CurrentE * dCurrentNu / ((1.0*2.0*CurrentNu)*(1.0*2.0*CurrentNu));
    real dCurrentMu = dCurrentE / (2.0*(1.0+CurrentNu)) + CurrentE/(2.0*(1.0+CurrentNu)*(1.0+CurrentNu)) * dCurrentNu;
    real dCurrentAlpha = _alpha_reacted - _alpha_virgin;
    real dCurrentHeatCapacity = _heatCapacity_reacted - _heatCapacity_virgin;
    real dCurrentThermalConductivity = _thermalConductivity_reacted - _thermalConductivity_virgin;

    real CurrentDeltaTheta, CurrentConcentration, ConcentrationInc, OldDeltaTheta;
    if(_IncludeThermalAnalysis){
        CurrentDeltaTheta = concentration[0];// component 0 is temperature in this mode
        CurrentConcentration = concentration[1] + _c_ref;
        ConcentrationInc = (concentration[1] - concentration0[1]);

        // just needed for the source terms
        OldDeltaTheta = concentration0[0];
    }else{
        CurrentDeltaTheta = 0; // set to the default _theta_ref
        CurrentConcentration = concentration[0] + _c_ref;// if no heat transfer, indexes are all 0!!
        ConcentrationInc = (concentration[0] - concentration0[0]);
        OldDeltaTheta = 0;
    }
    real CurrentTheta = CurrentDeltaTheta+_theta_ref;
    real OldTheta = OldDeltaTheta+_theta_ref;
    real CurrentOxygenDiffusivity;
    this->_getDFromTheta(CurrentTheta, CurrentOxygenDiffusivity);

    
    real Fe[] = { 1., 0., 0., 0., 1., 0., 0., 0., 1. };
    real FeInv[] = { 0., 0., 0., 0., 0., 0., 0., 0., 0. };
    real Ce[] = { 0., 0., 0., 0., 0., 0., 0., 0., 0. };
    real LogCe[] = { 0., 0., 0., 0., 0., 0., 0., 0., 0. };
    real FeDotdt[] = { 0., 0., 0., 0., 0., 0., 0., 0., 0. };
    
    for (int a=0;a<9;a++){
        Fe[a] += q[4+a];
        FeDotdt[a] = q[4+a] - q[13+a];
        q[13+a]=q[4+a];
    }

    real J = MathMat3Inv(Fe, FeInv);
    MathMat3Mults(Fe, Fe, Ce);
    int fl0 = 1, fl1 = 0, fl2 = 0;
    logarithmic_(Ce, LogCe, NULL, NULL,&fl0,&fl1,&fl2);
    real L2LogCeSquared=0.0;
    real ThermalElasticEffect = 0.0;
    for (int a=0;a<3;a++){
        for (int b=0;b<3;b++){
            L2LogCeSquared += LogCe[a*3+b]*LogCe[a*3+b];
            ThermalElasticEffect += FeInv[a*3+b] * FeDotdt[b*3+a];
        }
    }
    q[22] = -ThermalElasticEffect/dt[0] * CurrentTheta * CurrentBulk * CurrentAlpha*J*3.0; 
    
    int c = 0; // which component of the problem are we dealing with
    if(_IncludeThermalAnalysis){
        // transient terms always required
        df[c] = - (_rho * CurrentHeatCapacity + 9*CurrentTheta * CurrentAlpha * CurrentAlpha * CurrentBulk) / dt[0];
        f[c] = df[c] * (CurrentDeltaTheta - OldDeltaTheta);
        c = 1; // increment c to do the chemistry problem next
    }
    //undo the rescaling. this is crazy that the transport properties show up in the transient term here do to rescaling
    df[c * ndf + c] = - (CurrentThermalConductivity / (CurrentOxygenDiffusivity))  * 1.0 / dt[0];
    f[c] = df[c * ndf + c] * ConcentrationInc ;

    real oldVolumeChange = (J - CurrentAlpha * (OldDeltaTheta) - log(_Pilling_Bedworth) * CurrentVolumeFraction);
    real A_Term =  -0.5 * dCurrentMu * L2LogCeSquared - 0.5 * dCurrentBulk * oldVolumeChange * oldVolumeChange
    +( log(_Pilling_Bedworth) + 3*dCurrentAlpha * OldDeltaTheta) * CurrentBulk * oldVolumeChange - dCurrentHeatCapacity * OldTheta * (1.0 - log(OldTheta / _theta_ref)) + _chemistry_modulus ;

    real dA_Term = dCurrentHeatCapacity * log(OldTheta / _theta_ref)+   3*(CurrentAlpha * dCurrentBulk + dCurrentAlpha * CurrentBulk) * oldVolumeChange - 3*( log(_Pilling_Bedworth) + 3*dCurrentAlpha * OldDeltaTheta) * CurrentAlpha * CurrentBulk;

    //check that thermodynamic restrictions satisfied, and sufficient reactants are available
    real ConsumptionRate = 0.0;
    if( (_DoesItReact>0)   && (q[2]<=1) && (q[2]>=0) && (CurrentConcentration >_R_max_reaction * CurrentVolumeFraction) && (A_Term>dA_Term * OldTheta)){
        //real ConsumptionRate = _RateConstant * exp(-_ActivationEnergy / (OldTheta*_R_gas_constant)) * (1 - CurrentVolumeFraction);
        
        //enforce species conservation
        real bound = std::min(1.0, CurrentConcentration / _R_max_reaction);
        if(bound>q[2]){
            ConsumptionRate = (bound -q[2])/dt[0];
            q[2] = bound;
        }

        q[23] = ConsumptionRate * (A_Term - OldTheta * dA_Term  );
        // Add any heating due to reaction. this could be uncommented out if we ever needed them.
        // These terms were used in thesis of D. Pickard. They typically are not important
        //f[0] += q[23]; // Commented out source does not matter in No Heat Transfer Mode, it only works when _IncludeThermalAnalysis is true
    }
    // Add any chemical dissipation. this could be uncommented out if we ever needed them.
    // These terms were used in thesis of D. Pickard. They typically are not important
    //f[0] += q[3] ; // Commented out source does not matter in No Heat Transfer Mode, it only works when _IncludeThermalAnalysis is true
    
    // Add any thermo-elastic coupling. this could be uncommented out if we ever needed them.
    // These terms were used in thesis of D. Pickard. They typically are not important
    //f[0] += q[22] ; // Commented out source does not matter in No Heat Transfer Mode, it only works when _IncludeThermalAnalysis is true

    q[0] = CurrentConcentration;
    q[1] = CurrentDeltaTheta;
    return;
    // end done

}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    return;
}

int summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic::number_unknowns() const{
    if(_IncludeThermalAnalysis){
        return 2;
    }
    return 1; // case with no heat transfer
}

void summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic::_getDFromTheta(const real theta,
                      real &D) const
{   
    if(_D_act>0){
        real B0 = _D_const * 1/(4 * 0.564189583547756 * 0.564189583547756 * _Pilling_Bedworth * _Pilling_Bedworth);// fudge factor we need to calibrate the model!
        D = B0 * std::exp(-_D_act/ (_R_gas_constant * theta));
    }else{
        D = _D_const;
    }
    return ;
}


real summit::PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic::bulkModulus(real const* internal) const
{
    return _thermalConductivity_virgin;
}
// end of file
