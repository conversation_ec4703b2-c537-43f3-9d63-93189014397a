#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "linear-thermo-poro-elasticity.h"
#include "../../../mathlib/mathlib.h"
#include "../../../io/summit_message.h"
#include "../../exponential/log_exp_mappings.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::LinearThermoPoroElasticity::LinearThermoPoroElasticity(const std::string& name)
  : ReactionDiffusionMaterial(name, LINEAR_THERMO_PORO_ELASTICITY_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::LinearThermoPoroElasticity::~LinearThermoPoroElasticity() {}

void summit::LinearThermoPoroElasticity::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);
    _rho = values[0];
    _heatCapacity = values[1];
    _permeability = values[2];
    _viscosity = values[3];
    _conductivity = values[4];
    _shearModulus = values[5];
    _poissonRatio = values[6];
    _bulkModulus = values[7];
    _solidBulkModulus = values[8];
    _secondSolidBulkModulus = values[9];
    _cubicalCTE = values[10];
    _secondCubicalCTE = values[11];
    _fluidBulkModulus = values[12];
    _fluidCTE = values[13];
    _theta_ref = values[14];
    _porosity_ref= values[15];
    _porePressureCoef = 1.0/(1.0+_porosity_ref*(_bulkModulus/_fluidBulkModulus)*(1-_fluidBulkModulus/_secondSolidBulkModulus)/(1.0-_bulkModulus/_solidBulkModulus)) ;// Eq. 7 of McTigue 1986
    _undrainedPoissonRatio = (3*_poissonRatio+_porePressureCoef*(1.0-2.0*_poissonRatio)*(1.0-_bulkModulus/_solidBulkModulus))/(3.0-_porePressureCoef*(1.0-2.0*_poissonRatio)*(1-_bulkModulus/_solidBulkModulus)) ;// Eq. 8 of McTigue 1986
    _c = _permeability/_viscosity*2*_shearModulus*(1-_poissonRatio)/(9.0*(1.0-_undrainedPoissonRatio)*(_undrainedPoissonRatio-_poissonRatio)) * (1.0+_undrainedPoissonRatio) * (1.0+_undrainedPoissonRatio) * _porePressureCoef * _porePressureCoef;// Eq. 18 of McTigue 1986

    real rightTerm =     _porePressureCoef*(1.0-_poissonRatio)*(1.0+_undrainedPoissonRatio)/(2.0*(_undrainedPoissonRatio-_poissonRatio)) *_porosity_ref*(_fluidCTE-_secondCubicalCTE);



    _b = 4.0/3.0*_shearModulus * (1.0+_poissonRatio)/ (1.0-_poissonRatio) * (_c/(_conductivity/(_rho*_heatCapacity))*_cubicalCTE+rightTerm);// Eq. 20 of McTigue 1986
    
    // real factorFront = 4.0/9.0*_shearModulus * _porePressureCoef * (1.0+_undrainedPoissonRatio)/ (1.0-_undrainedPoissonRatio);
    // real pPrime =  factorFront * (_cubicalCTE+rightTerm);// Eq. 27 of McTigue 1986
    

    // std::cout << "R is: " <<  sqrt( _c/(_conductivity/(_rho*_heatCapacity)))<<std::endl;
    // std::cout << "_b is: " <<  _b<<std::endl;
    // std::cout << "pPrime is: " <<  pPrime<<std::endl;
    // std::cout << "rightTerm is: " <<  rightTerm<<std::endl;
    // std::cout << "factorFront is: " <<  factorFront<<std::endl;
    // std::cout << "(_cubicalCTE+rightTerm) is: " <<  (_cubicalCTE+rightTerm)<<std::endl;
    return;
}

void summit::LinearThermoPoroElasticity::Display()
{
    // Check material properties
    Message::Info("Linear Thermo-Poro-Elasticity Transport Model:");
    Message::Info("\tDensity....... = %e", _rho);
    Message::Info("\tHeat Capacity....... = %e", _heatCapacity);
    Message::Info("\tFluid Permeability....... = %e", _permeability);
    Message::Info("\tThermal Conductivity...... = %e", _conductivity);
    Message::Info("\tShear Modulus....... = %e", _shearModulus);
    Message::Info("\tPoisson's Ratio....... = %e", _poissonRatio);
    Message::Info("\tBulk Modulus....... = %e", _bulkModulus);
    Message::Info("\tSolid Bulk Modulus....... = %e", _solidBulkModulus);
    Message::Info("\tSecond Solid Bulk Modulus....... = %e", _secondSolidBulkModulus);
    Message::Info("\tFluid Bulk Modulus....... = %e", _fluidBulkModulus);
    Message::Info("\tFluid Thermal Expansion Coefficient....... = %e", _fluidCTE);
    Message::Info("\tLinear Thermal Expansion Coefficient....... = %e", _cubicalCTE);
    Message::Info("\tSecond Linear Thermal Expansion Coefficient....... = %e", _secondCubicalCTE);
    Message::Info("\tReference Temperature....... = %e", _theta_ref);
    Message::Info("\tReference Porosity....... = %e", _porosity_ref);
    Message::Info("\tUndrained Poisson's Ratio....... = %e", _undrainedPoissonRatio);
    Message::Info("\tB....... = %e", _porePressureCoef);
    Message::Info("\tFluid Diffusivity Parameter....... = %e", _c);
    Message::Info("\tThermal Transient Source....... = %e", _b);

    // end of method
    return;
}

summit::real summit::LinearThermoPoroElasticity::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    return _conductivity / (_rho * _heatCapacity);
}

void summit::LinearThermoPoroElasticity::Constitutive(const real* concentration0,
                                                           const real* concentration,
                                                           const real* Dconcentration0,
                                                           const real* Dconcentration,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    int strain_dim = ndm * ndf;
    int c = 0;
    for (int dimension = 0; dimension < ndm; dimension++){
        //standard heat flux
        P[dimension + c * ndm] = _conductivity * Dconcentration[dimension + c * ndm];
        //derivative of above with respect to Dconcentration
        if(compute_tangents){
            tangent[(dimension + c * ndm) * strain_dim + dimension + c * ndm] = _conductivity;
        }
    }
    c = 1;
    for (int dimension = 0; dimension < ndm; dimension++){
        //standard heat flux
        P[dimension + c * ndm] = _c * Dconcentration[dimension + c * ndm];
        //derivative of above with respect to Dconcentration
        if(compute_tangents){
            tangent[(dimension + c * ndm) * strain_dim + dimension + c * ndm] = _c;
        }
    }
    c = 2;
    for (int dimension = 0; dimension < ndm; dimension++){
        //standard heat flux
        P[dimension + c * ndm] = _permeability/_viscosity * Dconcentration[dimension + c * ndm];
        //derivative of above with respect to Dconcentration
        if(compute_tangents){
            tangent[(dimension + c * ndm) * strain_dim + dimension + c * ndm] = _permeability/_viscosity;
        }
    }
    
    real currentTemperature = concentration[0];
    real currentPressure = concentration[2];
    //pressure we want to add to the solid response
    q[0] = - _bulkModulus*_cubicalCTE*(currentTemperature-_theta_ref)-(1-_bulkModulus/_solidBulkModulus)*currentPressure;

    // these lines are so this model can be used with the linear thermo-elastic solid model
    // it is key that this model has CTE=1, and uses the same elastic constants
    // if that is the case the following lines are required so that the solid model can read a fictitious temperature
    // that will contribute a stress of the form calculated above
    // real lambda = _bulkModulus - 2.0/3.0 * _shearModulus;
    // if(ndm==2){
    //     q[0] = -q[0] /(2*lambda+2*_shearModulus);
    // }
    // if(ndm==3){
    //     q[0] = -q[0] /(3*lambda+2*_shearModulus);
    // }


    return;
}


summit::real summit::LinearThermoPoroElasticity::capacity(real const* internal, const int component) const
{   
    return 0.0;
}

void summit::LinearThermoPoroElasticity::_setInternalVariableMap()
{
    SetLocationInInternalTable("ScalarToExchangeWithMechanics", 0, 1);
    // all done
    return;
}

void summit::LinearThermoPoroElasticity::Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{
    if(dt[0]<1e-19){ // this catches floating point errors
        return;
    }

    // thermal transients
    df[0] = - (_rho * _heatCapacity ) / dt[0];
    f[0] = df[0] * (concentration[0] - concentration0[0]);
    // auxiliary parabolic transients
    df[1 * 3 + 1] = - 1.0 / dt[0];
    f[1] = df[1 * 3 + 1] * (concentration[1] - concentration0[1]) ;
    // thermal source term in auxiliary parabolic problem
    f[1] += _b * (concentration[0] - concentration0[0])/ dt[0];
    df[0 * 3 + 1] = _b / dt[0];
    // thermal source term in auxiliary elliptic problem
    real thermalCoeff = _porosity_ref*(_fluidCTE-_secondCubicalCTE);
    f[2] = thermalCoeff * (concentration[0] - concentration0[0])/ dt[0];
    df[0 * 3 + 2] = thermalCoeff / dt[0];
    real auxDiffCoeff = -3*(_undrainedPoissonRatio-_poissonRatio)/(2*_shearModulus*_porePressureCoef*(1+_poissonRatio)*(1+_undrainedPoissonRatio));
    f[2] += auxDiffCoeff * (concentration[1] - concentration0[1])/ dt[0];
    df[1 * 3 + 2] = auxDiffCoeff/ dt[0];
    return;
    // end done

}

void summit::LinearThermoPoroElasticity::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    return;
}

int summit::LinearThermoPoroElasticity::number_unknowns() const{
    return 3;
}

real summit::LinearThermoPoroElasticity::bulkModulus(real const* internal) const
{
    return _conductivity;
}
// end of file
