#ifndef SUMMIT_LINEAR_THERMO_PORO_ELASTICITY_H
#define SUMMIT_LINEAR_THERMO_PORO_ELASTICITY_H

#include "../../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material LinearThermoPoroElasticity
                                         // 0 homogeneous reactions
#define LINEAR_THERMO_PORO_ELASTICITY_NUMBER_INTERNAL_VARIABLES 1

namespace summit {

/**
 *
 */
class LinearThermoPoroElasticity : public ReactionDiffusionMaterial {
  public:
    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    LinearThermoPoroElasticity(const std::string& name);

    /**
     * Destructor
     */
    virtual ~LinearThermoPoroElasticity();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    LinearThermoPoroElasticity(const LinearThermoPoroElasticity&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    LinearThermoPoroElasticity& operator=(const LinearThermoPoroElasticity&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * LinearThermoPoroElasticity constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const;

    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;
  
      /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 
  protected: 

    real _rho;
    real _heatCapacity;
    real _permeability;
    real _viscosity;
    real _conductivity;
    real _shearModulus;
    real _poissonRatio;
    real _bulkModulus;
    real _solidBulkModulus;
    real _cubicalCTE;
    real _theta_ref;
    real _secondCubicalCTE;
    real _secondSolidBulkModulus;
    real _fluidBulkModulus;
    real _fluidCTE;
    real _porosity_ref;
    real _porePressureCoef;
    real _undrainedPoissonRatio;
    real _c;
    real _b;


  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
