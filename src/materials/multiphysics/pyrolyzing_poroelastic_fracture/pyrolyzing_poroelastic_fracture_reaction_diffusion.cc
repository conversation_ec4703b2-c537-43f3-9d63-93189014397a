#include <pyre/journal.h>
#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "pyrolyzing_poroelastic_fracture_reaction_diffusion.h"
#include "../../../mathlib/mathlib.h"
#include "../../../io/summit_message.h"

#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::PyrolyzingPoroElasticFractureReactionDiffusion::PyrolyzingPoroElasticFractureReactionDiffusion(const std::string& name)
  : ReactionDiffusionMaterial(name, LINEARIZED_PYROLYSIS_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::PyrolyzingPoroElasticFractureReactionDiffusion::~PyrolyzingPoroElasticFractureReactionDiffusion() {}

void summit::PyrolyzingPoroElasticFractureReactionDiffusion::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    _rhov.resize(3,0);
    _rhoc.resize(3,0);
    _B.resize(3,0);
    _E.resize(3,0);
    _psi.resize(3,0);
    _T_reac.resize(3,0);

    // Programmed in the specific model adopted by C. Quinn in his masters thesis.
    // This could be improved in future work or made more general.
    _rhov = {900.0, 300.0, 1600.0};
    _rhoc = {600.0, 0.0, 1600.0};
    _psi = {3.0, 3.0, 0.0};
    _E = {20444.0, 8556.0, 10.0e10};
    _B = {4.48e9, 1.20e4, 1.0e-11};
    _T_reac = {555.6, 333.3, 10.0e10};
    _gamma = {0.5, 0.5, 0.5};
    // _rho_C = 1600.0;
    // _Gamma = 0.5;

    _kChar = 2.0e-11;
    _kVirgin = 1.6e-11;

    _phiChar = 0.85;
    _phiVirgin = 0.8;

    // all done
    return;
}

#ifdef WITH_YAML_CPP
void summit::PyrolyzingPoroElasticFractureReactionDiffusion::Load(const YAML::Node &yamlNode) {

    pyre::journal::error_t error("summit.materials.reaction_diffusion.PyrolyzingPoroElasticFractureReactionDiffusion");
    
    // Read material parameters
    try {
        // these are debugging flags and inputs that control the mode of operation
        _explicit_lagged_properties = yamlNode["lag representitive quantities"].as<bool>();

        _p_r = yamlNode["representative pressure"].as<real>();
        _T_r = yamlNode["representative temperature"].as<real>();

        _chem_source_terms = yamlNode["include chemical source terms"].as<bool>();
        _finite_diff_rhos = yamlNode["finite difference decomposition rate"].as<bool>();
        _explicit_char_solve = yamlNode["explicit evolution of char"].as<bool>();
        _advective_energy_term = yamlNode["include advective energy balance term"].as<bool>();
        _DoC_permeability = yamlNode["degree-of-char-dependent permeability"].as<bool>();
        _Klinkenberg_permeability = yamlNode["Klinkenberg permeability"].as<bool>();

        // _Rho_Total_Char = yamlNode["char density"].as<real>();
        // _Rho_Total_Virgin = yamlNode["virgin density"].as<real>();
        _TEMPERATURE_OFFSET_VERY_IMPORTANT = yamlNode["offset temperature"].as<real>();
        _PRESSURE_OFFSET_VERY_IMPORTANT = yamlNode["offset pressure"].as<real>();
        // _Gamma = yamlNode["volume fractions"].as<real>();

        // These are the inputs for the pyrolysis. That means the source terms
        _rhov = yamlNode["component virgin density"].as<std::vector<real>>();
        _rhoc = yamlNode["component char density"].as<std::vector<real>>();
        _B = yamlNode["arhenius coefficient"].as<std::vector<real>>();
        _E = yamlNode["activation energy"].as<std::vector<real>>();
        _psi = yamlNode["reaction order"].as<std::vector<real>>();
        _T_reac = yamlNode["activation temperature"].as<std::vector<real>>();
        _gamma = yamlNode["volume fractions"].as<std::vector<real>>();

        _kChar = yamlNode["char permeability"].as<real>();
        _kVirgin = yamlNode["virgin permeability"].as<real>();

        _k0Char = yamlNode["Klinkenberg char permeability"].as<real>();
        _bChar = yamlNode["Klinkenberg char permeability slip parameter"].as<real>();
        _k0Virgin = yamlNode["Klinkenberg virgin permeability"].as<real>();
        _bVirgin = yamlNode["Klinkenberg virgin permeability slip parameter"].as<real>();

        _phiChar = yamlNode["char porosity"].as<real>();
        _phiVirgin = yamlNode["virgin porosity"].as<real>();

        // calculate the total density of the solid
        real phi = _phiVirgin;
        _Rho_Total_Char = 0.0;
        _Rho_Total_Virgin = 0.0;
        for (int i = 0; i < _gamma.size(); i++){
            _Rho_Total_Char += _gamma[i] * _rhoc[i];
            _Rho_Total_Virgin += _gamma[i] * _rhov[i];
        }
        _Rho_Total_Char *= (1.0 - phi);
        _Rho_Total_Virgin *= (1.0 - phi);

        // _rho_C = yamlNode["virgin fiber density"].as<real>();

        std::vector<real> Temperatures = yamlNode["lookup solid temperature"].as<std::vector<real>>();
        std::vector<real> CapacitiesVirgin = yamlNode["lookup virgin specific heat capacity"].as<std::vector<real>>();
        std::vector<real> CapacitiesChar = yamlNode["lookup char specific heat capacity"].as<std::vector<real>>();
        std::vector<real> ThermalConductivitiesVirgin = yamlNode["lookup virgin thermal conductivity"].as<std::vector<real>>();
        std::vector<real> ThermalConductivitiesChar = yamlNode["lookup char thermal conductivity"].as<std::vector<real>>();
        std::vector<real> EnthalpiesVirgin = yamlNode["lookup solid virgin enthalpies"].as<std::vector<real>>();
        std::vector<real> EnthalpiesChar = yamlNode["lookup solid char enthalpies"].as<std::vector<real>>();


        _KappaCharInterpolant.set_points(Temperatures, ThermalConductivitiesChar);
        _KappaVirgInterpolant.set_points(Temperatures, ThermalConductivitiesVirgin);

        _CpCharInterpolant.set_points(Temperatures, CapacitiesChar);
        _CpVirgInterpolant.set_points(Temperatures, CapacitiesVirgin);

        _EnthalpiesCharInterpolant.set_points(Temperatures, EnthalpiesChar);
        _EnthalpiesVirgInterpolant.set_points(Temperatures, EnthalpiesVirgin);

        std::vector<real> TemperaturesPyrolysisGas = yamlNode["lookup gas temperature"].as<std::vector<real>>();
        std::vector<real> hPyrolysisGas = yamlNode["lookup gas enthalpy"].as<std::vector<real>>();
        std::vector<real> MuPyrolysisGas = yamlNode["lookup gas viscosity"].as<std::vector<real>>();
        std::vector<real> MPyrolysisGas = yamlNode["lookup gas molar mass"].as<std::vector<real>>();
        std::vector<real> CpPyrolysisGas = yamlNode["lookup gas specific heat capacity"].as<std::vector<real>>();

        _GasEnthalpyInterpolant.set_points(TemperaturesPyrolysisGas, hPyrolysisGas);
        _GasViscosityInterpolant.set_points(TemperaturesPyrolysisGas, MuPyrolysisGas);
        _GasMolarWeightInterpolant.set_points(TemperaturesPyrolysisGas, MPyrolysisGas);
        _GasCpInterpolant.set_points(TemperaturesPyrolysisGas, CpPyrolysisGas);

    } catch (...) {
        error << "Error in PyrolyzingPoroElasticFractureReactionDiffusion Material : A required key could not be found in the YAML tree."
              << pyre::journal::endl(__HERE__);
    }

    return; // All done!
}
#endif

void summit::PyrolyzingPoroElasticFractureReactionDiffusion::Display()
{
    // Check material properties
    Message::Info("Linearized Pyrolysis Parameters:");
    Message::Info("\tLag representative quantities....... = ", _explicit_lagged_properties);
    Message::Info("\tRepresentative pressure....... = %e", _p_r);
    Message::Info("\tRepresentative temperature....... = %e", _T_r);
    Message::Info("\tInclude chemical source terms....... = ", _chem_source_terms);
    Message::Info("\tFinite difference decomposition rate....... = ", _finite_diff_rhos);
    Message::Info("\tExplicit evolution of char....... = ", _explicit_char_solve);
    Message::Info("\tInclude advective energy balance term....... = ", _advective_energy_term);
    Message::Info("\tDegree-of-char-dependent permeability....... = ", _DoC_permeability);
    Message::Info("\tKlinkenberg permeability....... = ", _Klinkenberg_permeability);

    // end of method
    return;
}

summit::real summit::PyrolyzingPoroElasticFractureReactionDiffusion::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    
    real rhos = 0.0;
    real kappa = 0.0;
    real Cp = 0.0;

    if (rhos == 0.0){
        rhos = 280.0;
    }

    if (kappa = 0.0){
        kappa = 0.5;
    }

    if (Cp == 0.0){
        Cp = 1400.0;
    }

    // diffusivity
    return kappa / (rhos * Cp);
}

void summit::PyrolyzingPoroElasticFractureReactionDiffusion::Constitutive(const real* primal0,
                                                           const real* primal, // primal[0] is temperature, // primal[1] is pyrolysis gas pressure
                                                           const real* Dprimal0,
                                                           const real* Dprimal,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dt,
                                                           const int ndf, // number of components in primal
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    //whenever the actual temperature is below absolute zero, we can exit here. This is unphysical behavior
    if ((primal[0]+_TEMPERATURE_OFFSET_VERY_IMPORTANT) <= 0.0){
        return;
    }

    // These parameters are very important in the DG mode of operation because the crack flank bulk quadrature points need to be initialized in DG mode
    // This can be done inside the driver, but here we adopt the very simple paradigm of shifting the primitive quantities of the problem in order to
    // achieve a non-zero initial condition, without writing data in at the driver. Also note that the DG stabilizer functions requires an evaluation of
    // this function in the constructor, which may make it desirable to have the constitutive model work robustly from the start.
    real T = (primal[0]+_TEMPERATURE_OFFSET_VERY_IMPORTANT);
    real T0 = (primal0[0]+_TEMPERATURE_OFFSET_VERY_IMPORTANT);

    real p = (primal[1]+_PRESSURE_OFFSET_VERY_IMPORTANT);
    real p0 = (primal0[1]+_PRESSURE_OFFSET_VERY_IMPORTANT);

    real T_eval;
    real p_eval;
    if ( _explicit_lagged_properties ) {
        T_eval = (primal0[0]+_TEMPERATURE_OFFSET_VERY_IMPORTANT);
        p_eval = (primal0[1]+_PRESSURE_OFFSET_VERY_IMPORTANT);
    } else {
        T_eval = _T_r;
        p_eval = _p_r;
    }
    
    real R = 8.3145;

    // Gas properties evaluated at representative temperature
    // This is done for simplicity here following the approach of S. Hoss
    // See the masters thesis, and also her AIAA paper with J. Krause
    real M_r = _GasMolarWeightInterpolant(T_eval)/1000.0;
    real mu_r = _GasViscosityInterpolant(T_eval)*0.0001;
    real rhog_r = p_eval * M_r / (R * T_eval);  // should make sure that this is not getting mixed up with rhog
    real hg_r = _GasEnthalpyInterpolant(T_eval)*1.0e3;

    real phi = _phiVirgin;

    real rhos = calculateTotalSolidDensity(phi,q);

    real rho = rhos + phi * rhog_r;

    real rhov = _Rho_Total_Virgin;
    real rhoc = _Rho_Total_Char;
    real beta = (rhov - rhos)/(rhov - rhoc);

    // Degree of char dependent permeability
    real k = _kVirgin;
    real k0Char = _k0Char;
    real k0Virgin = _k0Virgin;
    real bChar = _bChar;
    real bVirgin = _bVirgin;
    if ( _DoC_permeability && _Klinkenberg_permeability ) {
        real kVirgin = k0Virgin * ( 1 + bVirgin / p_eval );
        real kChar = k0Char * ( 1 + bChar / p_eval );
        k = (1 - beta) * kVirgin + beta * kChar;
    } else if ( _DoC_permeability ) {
        k = (1 - beta) * _kVirgin + beta * _kChar;
    } else if ( _Klinkenberg_permeability ) {
        k = k0Virgin * ( 1 + bVirgin / p_eval );
    }

    real yc = rhoc / rhos * beta;
    real yv = rhov / rhos * (1.0 - beta);
    real yg = phi * rhog_r / rho;
    real ys = 1.0 - yg;
    
    real KappaChar_r = _KappaCharInterpolant(T_eval);
    real KappaVirgin_r = _KappaVirgInterpolant(T_eval);

    real Kappa_r = KappaVirgin_r * yv + KappaChar_r * yc;
    
    for (int dimension = 0; dimension < ndm; dimension++){

        // Heat flux term
        int component = 0;
        if (_advective_energy_term) {
            P[dimension + component * ndm] = Kappa_r * Dprimal[dimension + component * ndm] + hg_r * rhog_r * k / mu_r * Dprimal0[dimension + ( component + 1 ) * ndm];
        } else {
            P[dimension + component * ndm] = Kappa_r * Dprimal[dimension + component * ndm];
        }
        if (compute_tangents) {
            tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = Kappa_r;
            dPdu[(dimension + ndm) * ndf + component] = 0.0;
        }

        // Mass flux term
        component = 1;
        for (int dimension = 0; dimension < ndm; dimension++){
            P[dimension + component * ndm] = rhog_r * (k / mu_r) * Dprimal[dimension + component * ndm];
            if(compute_tangents){
                tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = rhog_r * (k / mu_r);
                dPdu[(dimension + ndm) * ndf + component] = 0.0;
            }
        }
    }
    
    return;
}

summit::real summit::PyrolyzingPoroElasticFractureReactionDiffusion::capacity(real const* q, const int component) const
{   
    return 0.0;
}

void summit::PyrolyzingPoroElasticFractureReactionDiffusion::_setInternalVariableMap()
{

    SetLocationInInternalTable("ScalarToExchangeWithMechanics", _INT_TRANSFER, 1); // this quantity is not completely necessary because the scalar rate drives cracking in fracture mode
    SetLocationInInternalTable("Extent of Reaction", _INT_EXTENT_OF_REACTION, _NUM_OF_REACTIONS); // This is the state of the damage variables
    SetLocationInInternalTable("ScalarRateToExchangeWithMechanics", _INT_TRANSFER_RATE, 1);// this is the quantity leading to swelling in fracture mode, where we use subcycling extensively
    // all done
    return;
}

summit::real summit::PyrolyzingPoroElasticFractureReactionDiffusion::getComponentDensity(const real* q, const int index) const
{   
    return q[_INT_EXTENT_OF_REACTION + index] *(_rhoc[index] - _rhov[index]) + _rhov[index] ;
}

void summit::PyrolyzingPoroElasticFractureReactionDiffusion::setExtentOfReaction(const int index, const real rhoi, real* q) const
{   
    q[_INT_EXTENT_OF_REACTION + index] = (rhoi - _rhov[index]) / (_rhoc[index] - _rhov[index]);//catch this floating point exception. set to zero please
    return;
}

void summit::PyrolyzingPoroElasticFractureReactionDiffusion::Source(const real* primal0, const real* primal, const real* Dprimal0, const real* Dprimal, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{   
    if(dt[0]<1e-15){
        std::cout << "time step: " << dt[0] << "too small. returning" <<std::endl;
        return;
    }

    if ((primal[0]+_TEMPERATURE_OFFSET_VERY_IMPORTANT) <= 0.0){
        return;
    }

    // define the universal gas constant
    real R = 8.3145;

    real gamma = _gamma[0];

    // get primal variables
    real T = (primal[0]+_TEMPERATURE_OFFSET_VERY_IMPORTANT);
    real T0 = (primal0[0]+_TEMPERATURE_OFFSET_VERY_IMPORTANT);

    real p = (primal[1]+_PRESSURE_OFFSET_VERY_IMPORTANT);
    real p0 = (primal0[1]+_PRESSURE_OFFSET_VERY_IMPORTANT);

    // porosity
    real phi = _phiVirgin;

    // get permeability
    real k = _kVirgin;

    real rhos0 = calculateTotalSolidDensity(phi, q);

    real T_eval;
    real p_eval;
    if ( _explicit_lagged_properties ) {
        // T_eval = primal0[0];
        // p_eval = primal0[1];
        T_eval = T0; 
        p_eval = p0;
    } else {
        T_eval = _T_r;
        p_eval = _p_r;
    }

    // calculate the decomposition rate and corresponding solid density
    real T_char; // has to be different from T_eval
    real rhos = rhos0;
    real drhos_dt = 0.0;
    real Ddrhos_dtDT = 0.0;
    if ( _chem_source_terms ) {

        if ( _explicit_char_solve ) {
            T_char = T0;
        } else if ( !_explicit_char_solve ) { // then we need to use the current temperature
            T_char = T;
        } else {
            T_char = _T_r; // updated to be the previous temperature
        }

        //rhos_dt = calculateDecompositionRate(T_char, q);
        real old_Total_rho = this->calculateTotalSolidDensity(phi, q);

        real dEdT[_NUM_OF_REACTIONS] = {0.0};
        updateExtentOfReaction(T_char, q, dt, dEdT);
        real dRhoidEi[_NUM_OF_REACTIONS] = {0.0};
        real dRhoDT = 0.0;
        for(int i=0;i< _NUM_OF_REACTIONS;i++){
            dRhoidEi[i] = _gamma[i]*(_rhoc[i]-_rhov[i])*(1.0-phi);
            dRhoDT += dRhoidEi[i] * dEdT[i];
        }
        
        real new_Total_rho = this->calculateTotalSolidDensity(phi, q);

        drhos_dt = (new_Total_rho - old_Total_rho) / dt[0];
        Ddrhos_dtDT = dRhoDT / dt[0];
        

        //rhos = calculateTotalSolidDensity(phi, q);
        // if ( _finite_diff_rhos ) {
        //     drhos_dt = ((rhos - rhos0) / dt[0]);
        // }
    }

    // Gas properties evaluated at representative temperature
    real M_r = _GasMolarWeightInterpolant(T_eval)/1000.0;
    real mu_r = _GasViscosityInterpolant(T_eval)*0.0001;
    real rhog_r = p_eval * M_r / (R * T_eval);

    // Calculate finite differences
    real dp_dt = (p - p0) / dt[0];
    real dT_dt = (T - T0) / dt[0];

    // calculate the degree of char
    real rhov = _Rho_Total_Virgin;
    real rhoc = _Rho_Total_Char;
    real beta = (rhov - rhos)/(rhov - rhoc);

    // calculate the bulk density
    real rho = rhos + phi * rhog_r;

    // calculate the extent of reaction variables
    real yc = rhoc / rhos * beta;
    real yv = rhov / rhos * (1.0 - beta);
    real yg = phi * rhog_r / rho;
    real ys = 1.0 - yg;

    // Calculate the partial heat of charring
    real h_bar_r = 0.0;
    if ( _chem_source_terms && _explicit_char_solve ) {
        h_bar_r = calculateConstPartialHeatOfCharring(T_char); // T_char is lagged in this case
    } else if ( _chem_source_terms && !_explicit_char_solve ) {
        h_bar_r = calculateConstPartialHeatOfCharring(T_eval); // needs to be T_eval rather than T_char so it doesn't contribute to tangents
    }

    // get the char and virgin sepcific heat capacity at the representative temperature
    real CpChar_r = _CpCharInterpolant(T_eval);
    real CpVirgin_r = _CpVirgInterpolant(T_eval);

    // calculate the solid material specific heat capacity
    real Cps_r = CpVirgin_r * yv + CpChar_r * yc;

    // calculate the gas specific heat capacity
    real Cpg_r = _GasCpInterpolant(T_eval)*1000.0;//magic number of 1000 is not good

    // calculate the bulk material specific heat capacity and corresponding derivative
    real Cp_r = ys * Cps_r + yg * Cpg_r;    // maybe make this constant

    // Initializing sources terms and their derivatives to avoid garbage numbers
    f[0] = 0.0;
    f[1] = 0.0;
    df[0] = 0.0;
    df[1] = 0.0;
    df[2] = 0.0;
    df[3] = 0.0;

    // Chemical source term and its derivatives for the energy balance equation - seems to cause problems for consistency
    if ( _chem_source_terms ) {
        f[0] += - h_bar_r * drhos_dt;
        df[0] += - h_bar_r * Ddrhos_dtDT;
        df[1] += 0.0;
    }

    // Chemical source term and its derivatives for the mass balance equation - seems to cause problems for consistency
    if ( _chem_source_terms ) {
        f[1] += - drhos_dt;
        df[2] += - Ddrhos_dtDT;
        df[3] += 0.0;
    }

    // Transient term and its derivatives for the energy balance equation
    f[0] += - rho * Cp_r * dT_dt;
    df[0] += - rho * Cp_r / dt[0];
    df[1] += 0.0;
    
    // Gas mass transient term and its derivatives for the mass balance equation, completely de-coupled. IE not Eq. 3.76 in C Quinn's thesis.
    // We have intentionally dropped a number of transient terms
    f[1] += - phi * ( M_r / ( R * T_eval ) ) * dp_dt;
    df[2] += 0.0;
    df[3] += - phi * ( M_r / ( R * T_eval * dt[0] ) );

    // Updating the internal table

    // scalar to exchange with mechanics
    // these numbers are placeholders
    // they could be read from file, and potentially YAMLized
    // Or from the Ablation Workshop Test Cases, if they have them for TACOT
    real myStressTempModulus = -10.0;
    real myBiotFactor = -5.0;
    real HydroNew = primal[0] * myStressTempModulus + primal[1] * myBiotFactor;
    
    q[_INT_TRANSFER_RATE] = (HydroNew - q[_INT_TRANSFER]) / dt[0];
    q[_INT_TRANSFER] = HydroNew;
    // here we use the relative temperature: T-_TEMPERATURE_OFFSET_VERY_IMPORTANT
    // same thing for the pressure!!

    return;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateTotalSolidDensity(real phi, real * q) const 
{
    real rhos = 0.0;
    for (int i = 0; i < _NUM_OF_REACTIONS; ++i){
        rhos += _gamma[i] * this->getComponentDensity(q, i);
    }
    rhos *= (1.0 - phi);

    return rhos;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateDecompositionRate(real T_eval, real * q) const
{
    real T = T_eval;
    real phi = _phiVirgin;

    real drhos_dt = 0.0;

    for (int i = 0; i < _NUM_OF_REACTIONS; ++i){
        real Bi = _B[i];
        real Ei = _E[i];
        real rho_vi = _rhov[i];
        real rho_ci = _rhoc[i];
        real psi = _psi[i];
        real rhoi = this->getComponentDensity(q, i);
        real drhoi_dt = 0.0;
        real w1 = 0.0;
        // real w2 = 0.0;
        if (T > _T_reac[i]){
            w1 = (rhoi-rho_ci)/rho_vi;
            // w2 = std::pow(std::pow(w1,1.0-psi) - Bi * (1.0 - psi) * std::exp(-Ei/T)*dt[0],1.0/(1.0-psi));
            // Above is an analaytic integration of the state variables. This approach is widely adopted in pyrolysis, by workers such as Joey Schulz at NASA, and C. Quinn in RRgroup
            // It is not strictly necessary, and has been removed below for convenience.
            drhoi_dt = -Bi*std::exp(-Ei/T)*rho_vi*std::pow(w1, psi); // this is the naive, very simple implementation often used in chemomechanics by D. Pickard
        } 
        drhos_dt += _gamma[i] * drhoi_dt;
    }

    drhos_dt *= (1.0 - phi);

    return drhos_dt;
}

void summit::PyrolyzingPoroElasticFractureReactionDiffusion::updateExtentOfReaction(real T_eval, real * q, real * dt, real * dEdT) const
{
    real T = T_eval;
    for (int i = 0; i < _NUM_OF_REACTIONS; ++i){
        if (T > _T_reac[i]){
            real Bi = _B[i];
            real Ei = _E[i];
            real rho_vi = _rhov[i];
            real rho_ci = _rhoc[i];
            real psi = _psi[i];
            real term1  = dt[0]*Bi*(psi-1.0)*std::exp(-Ei/T)*std::pow(rho_vi/(rho_vi-rho_ci), 1.0-psi);
            real dTerm1 = term1 * Ei/(T*T);
            real term2 = std::pow(1.0-q[_INT_EXTENT_OF_REACTION + i], 1.0-psi);
            q[_INT_EXTENT_OF_REACTION + i] = 1.0 - std::pow(term1+term2, 1.0/(1.0-psi)); 
            dEdT[i] = -(1.0/(1.0-psi)) * std::pow(term1+term2, 1.0/(1.0-psi) - 1.0) * dTerm1;
            //checks and enforces constraints.
            if(q[_INT_EXTENT_OF_REACTION + i]>1.0){
                q[_INT_EXTENT_OF_REACTION + i] = 1.0;
                dEdT[i] = 0.0;
            }
            if(q[_INT_EXTENT_OF_REACTION + i]<0.0){
                q[_INT_EXTENT_OF_REACTION + i] = 0.0;
                dEdT[i] = 0.0;
            }
        }
    }
    return;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateConstPartialHeatOfCharring(real T_eval) const
{
    real rho_c = _Rho_Total_Char;
    real rho_v = _Rho_Total_Virgin;

    real hc = _EnthalpiesCharInterpolant(T_eval);
    real hv = _EnthalpiesVirgInterpolant(T_eval);

    real h_bar = (rho_v*hv-rho_c*hc)/(rho_v - rho_c);

    return h_bar;
}


void summit::PyrolyzingPoroElasticFractureReactionDiffusion::ConvectiveFlux(const real* primal0, const real* primal, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{    
    return;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateGasEnthalpy(const real T) const{
    real hg = _GasEnthalpyInterpolant(T);
    return hg;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateSolidCharEnthalpy(const real T) const{
    real hc = _EnthalpiesCharInterpolant(T);
    return hc;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateSolidVirginEnthalpy(const real T) const{
    real hv = _EnthalpiesVirgInterpolant(T);
    return hv;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateCharThermalConductivity(const real T) const{
    real k = _KappaCharInterpolant(T);
    return k;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateVirginThermalConductivity(const real T) const{
    real k = _KappaVirgInterpolant(T);
    return k;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateGasViscosity(const real T) const{
    real mu = _GasViscosityInterpolant(T);
    return mu;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateGasMolarWeight(const real T) const{
    real M = _GasMolarWeightInterpolant(T);
    return M;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateGasCp(const real T) const{
    real cp = _GasCpInterpolant(T);
    return cp;
}


real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateDegreeOfChar(const real density) const{

    return (_Rho_Total_Virgin - density) / (_Rho_Total_Virgin - _Rho_Total_Char);
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateEmissivity(const real density) const{

    real beta = calculateDegreeOfChar(density);

    // calculate the extent of reaction variables
    real yc = _Rho_Total_Char / density * beta;
    real yv = 1.0 - yc;

    // calculate the emissivity
    real emissivity = 0.8*yv + 0.9*yc;

    return emissivity;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateHeatFlux(const real T, const real density, const real dT_dx) const{
    // Used for the Ablation Workshop Test Cases ONLY

    real beta = calculateDegreeOfChar(density);

    // calculate the extent of reaction variables
    real yc = _Rho_Total_Char / density * beta;
    real yv = 1.0 - yc;

    real KappaChar = _KappaCharInterpolant(T);
    real KappaVirgin = _KappaVirgInterpolant(T);

    real Kappa = KappaVirgin * yv + KappaChar * yc;

    return - Kappa * dT_dx;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::calculateMassFlux(const real T, const real p, const real dp_dx) const{
    // Used for the Ablation Workshop Test Cases ONLY

    real R = 8.3145;
    real M = _GasMolarWeightInterpolant(T)/1000.0;
    real mu = _GasViscosityInterpolant(T)*0.0001;
    real phi = _phiVirgin;
    real k = _kVirgin;

    real rhog = p * M / (R * T);
    real gas_velocity = - k / mu * dp_dx;

    return rhog * gas_velocity;
}

int summit::PyrolyzingPoroElasticFractureReactionDiffusion::number_unknowns() const{
    return 2;
}

real summit::PyrolyzingPoroElasticFractureReactionDiffusion::bulkModulus(real const* q) const
{
    real kappa = 0.0;
    if (kappa == 0.0){
        kappa = 3.5;
    }
    return kappa;
}

void summit::PyrolyzingPoroElasticFractureReactionDiffusion::laxFriedrichStabilization(const real* primal0, const real* primal, real* C, real* dC) const
{   
    C[0] = 0.3;
    return;
}

void summit::PyrolyzingPoroElasticFractureReactionDiffusion::initializeDefaultInternalVariables(real* q, real T, real p) const
{
    // we also do not need this thing
    q[_INT_TRANSFER] = 0.0;

    // the default behavior (ie. ALL ZERO qTable) is the pure virgin state. We do not need a default initializer
    // q[_INT_EXTENT_OF_REACTION] = 900.0;
    // q[_INT_EXTENT_OF_REACTION + 1] = 300.0;
    // q[_INT_EXTENT_OF_REACTION + 2] = 1600.0;

    return;
}

// end of file