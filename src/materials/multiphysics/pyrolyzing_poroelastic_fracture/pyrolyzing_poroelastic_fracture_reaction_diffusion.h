#ifndef SUMMIT_PYROLYZING_POROELASTIC_FRACTURE_REACTION_DIFFUSION_H
#define SUMMIT_PYROLYZING_POROELASTIC_FRACTURE_REACTION_DIFFUSION_H

#include "../../reaction_diffusion_material.h"
#include "../../../mathlib/spline.h"
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

// Number of internal variables:
// the value is used to size the memory allocation for the material PyrolyzingPoroElasticFractureReactionDiffusion
                                         // 0 homogeneous reactions
#define LINEARIZED_PYROLYSIS_NUMBER_INTERNAL_VARIABLES 5

namespace summit {

/**
 * Constitutive model for thermo-poro-elastic fracture developed by <PERSON><PERSON> for the FASTER technical interchange meeting (TIM) and PSAAP-IV site visit.
 * The approach adopts the pyrolysis models of S. Hoss and <PERSON><PERSON> Quinn, but neglects the pesky advective heat transfer term.
 * For many applications, this term is a second-order effect.
 * We combine the gas pressure and thermal expansion furnished by this simple model with linear thermo poroelasticity models implemented in
 * the class PyrolyzingPoroElasticFractureMechanics
 * For a discussion of the multiphysics theory see the dissertation of D. Pickard, and the papers referenced therein (McTigue, 1985).
 * Examples of sumMIT simulations with this theory are also presented in this thesis and in Pickard, 2024, JMPS.
 * Also see the work of J. Rice on this topic.
 * The flagship demonstration of this capability can be found at:
 * dv/sumMITApps/hypersonics/development/PressureSquared/two_dimensional_demo_dg/LinearizedPyrolysis.cc
 */
class PyrolyzingPoroElasticFractureReactionDiffusion : public ReactionDiffusionMaterial {
  public:
    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    PyrolyzingPoroElasticFractureReactionDiffusion(const std::string& name);

    /**
     * Destructor
     */
    virtual ~PyrolyzingPoroElasticFractureReactionDiffusion();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    PyrolyzingPoroElasticFractureReactionDiffusion(const PyrolyzingPoroElasticFractureReactionDiffusion&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    PyrolyzingPoroElasticFractureReactionDiffusion& operator=(const PyrolyzingPoroElasticFractureReactionDiffusion&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    #ifdef WITH_YAML_CPP
    /**
     * Method to load properties from a YAML node
     * @param[in] yaml a YAML node containing properties for this material
     */
    virtual void Load(const YAML::Node& yamlNode) override;
    #endif

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * PyrolyzingPoroElasticFractureReactionDiffusion constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* primal,
                              const real* u1,
                              const real* Dprimal,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* primal0, const real* primal, const real* Dprimal0, const real* Dprimal, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const;

    /**
     * set the source term
     */
    void ConvectiveFlux(const real* primal0, const real* primal, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;
  
      /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 

    /**
     * Compute the stabilization for the upwinding
     */
    void laxFriedrichStabilization(real const* primal0, real const* primal, real* C, real* dC) const;//this should be made virtual and reimplemented in future

    /**
     * Helper function to obtain derived quantity from the damage variables
     */
    real calculateTotalSolidDensity(real phi, real * q) const;
    
    /**
     * Key function to calculates the evolution of the pyrolysis gases and degradation of the char
     */
    real calculateDecompositionRate(real T_eval, real * q) const;
    
    /**
     * Updates the state, and also ensures the state remains admissible
     */
    void updateExtentOfReaction(real T_eval, real * q, real * dt, real * dEdT) const;

    /**
     * Enthalpy of the reaction model. Very important in physical applications to large structures, but for many problems on small length scales this is not important
     */
    real calculateConstPartialHeatOfCharring(real T_eval) const;

    /**
     * Thermodynamic model of the gas phase
     */
    real calculateGasEnthalpy(const real T) const;
    
    /**
     * Function inherited from models of S. Hoss, not used in this implementation
     */
    real calculateEmissivity(const real density) const;

    /**
     * enthalpy of the solid char
     */
    real calculateSolidCharEnthalpy(const real T) const;

    /**
     * Degree of char is a derived quantity needed by the chemistry-dependent thermophysical models
     */
    real calculateDegreeOfChar(const real density) const;

    /**
     * enthalpy of the solid virgin mateiral
     */
    real calculateSolidVirginEnthalpy(const real T) const;

    real calculateVirginThermalConductivity(const real T) const;

    real calculateCharThermalConductivity(const real T) const;

    real calculateHeatFlux(const real T, const real beta, const real dT_dx) const;

    real calculateMassFlux(const real T, const real p, const real dp_dx) const;

    real calculateGasViscosity(const real T) const;

    real calculateGasMolarWeight(const real T) const;

    real calculateGasCp(const real T) const;

    void initializeDefaultInternalVariables(real* q, real T, real p) const;

    real getComponentDensity(const real* q, const int index) const;

    void setExtentOfReaction(const int index, const real rhoi, real* q) const;
  protected: 
    bool _explicit_lagged_properties;

    // Representative temperature and pressure for linear model
    real _p_r;
    real _T_r;
    real _PRESSURE_OFFSET_VERY_IMPORTANT;
    real _TEMPERATURE_OFFSET_VERY_IMPORTANT;

    bool _chem_source_terms;
    bool _finite_diff_rhos;
    bool _explicit_char_solve;
    bool _advective_energy_term;
    bool _DoC_permeability;
    bool _Klinkenberg_permeability;

    real _kChar;
    real _kVirgin;

    real _k0Char;
    real _k0Virgin;
    real _bChar;
    real _bVirgin;

    real _phiChar;
    real _phiVirgin;

    std::vector<real> _rhov;
    std::vector<real> _rhoc;
    std::vector<real> _B;
    std::vector<real> _E;
    std::vector<real> _psi;
    std::vector<real> _T_reac;
    std::vector<real> _gamma;

    real _Rho_Total_Char;
    real _Rho_Total_Virgin;

    summit::spline _KappaCharInterpolant;
    summit::spline _KappaVirgInterpolant;

    summit::spline _CpCharInterpolant;
    summit::spline _CpVirgInterpolant;

    summit::spline _EnthalpiesCharInterpolant;
    summit::spline _EnthalpiesVirgInterpolant;
    
    summit::spline _GasEnthalpyInterpolant;
    summit::spline _GasViscosityInterpolant;
    summit::spline _GasMolarWeightInterpolant;
    summit::spline _GasCpInterpolant;

    int _NUM_OF_REACTIONS = 3;

    // Quantity that must be transferred to the solid mechanics
    int _INT_TRANSFER = 0;
    // Thermodynamic state variables, see Pickard & Radovitzky 2024, $\mathcal{E}$, as well as the reviewer response (soon to be submitted to JMPS)
    int _INT_EXTENT_OF_REACTION = _INT_TRANSFER + 1;
    int _INT_TRANSFER_RATE = _INT_EXTENT_OF_REACTION + _NUM_OF_REACTIONS;

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
