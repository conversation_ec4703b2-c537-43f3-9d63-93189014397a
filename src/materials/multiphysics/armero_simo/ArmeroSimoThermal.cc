#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>
#include <pyre/journal.h>

#include "ArmeroSimoThermal.h"
#include "../../../mathlib/mathlib.h"
#include "../../../io/summit_message.h"

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    4
// 0: Entropy
// 1: Temperature Estimate
// 2: Jacobian
// 3: devStrainSquared
// ****************************************************

/**
 * @brief Constructor with name
 * 
 * Initializes an ArmeroSimoThermal material with the given name and
 * sets up the internal variable mapping.
 * 
 * @param name Name of the material
 */
summit::ArmeroSimoThermal::ArmeroSimoThermal(const std::string& name)
  : ReactionDiffusionMaterial(name, ARMERO_SIMO_THERMAL_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

/**
 * @brief Destructor
 */
summit::ArmeroSimoThermal::~ArmeroSimoThermal() {}

void summit::ArmeroSimoThermal::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);
    /* properties in input file
     *
     *  0    1    2     3
     *  rho, E,   nu,   mode
     *
     *  mode 0: plane stress
     *       1: plane strain
     *       2: General 3D case
     *
     */
    
    // expect 20 parameters
    if (values.size() != 20) {
        pyre::journal::error_t error("summit.materials.ArmeroSimo");
        // throw message and die
        error << "you are using artificial viscosity and I expect you to give me 20"
                    "input parameters in the material description file, received "
                << values.size() << pyre::journal::endl(__HERE__);
    }

    // E material parameters
    _E_at_0 = values[0];
    _Et = values[1];

    // G material parameters
    _G_at_0 = values[2];
    _Gt = values[3];

    // alpha material parameters
    _alpha_a = values[4];
    _alpha_b = values[5];
    _alpha_c = values[6];
    _alpha_d = values[7];

    // heat capacity material parameters
    _Cp_exp_param = values[8];
    _Cp_constant = values[9];
    _Cp_linear = values[10];
    _Cp_expconstant = values[11];

    // thermal conductivity material parameters
    _kappa_constant = values[12];
    _kappa_exp_factor = values[13];
    _kappa_exp_param = values[14];
    _kappa_exp_rational_denom = values[15];

    // numerics and initial conditions
    _T_center_taylor_series = values[16];
    _physical_density = values[17];
    _temp_ref = values[18];
    _mode = values[19];

    // initialize the reference state for passing to thermal
    real D_eta_check,dK_check,K_check;//these are junk variables
    this->_entropy_and_variation(_temp_ref, _jacobian_ref,0,_eta_ref,D_eta_check);
    this->_approximate_kappa(_temp_ref, dK_check, K_check, _phi_ref);

    return;
}

void summit::ArmeroSimoThermal::Display()
{
    // display
    Message::Info("Kirchhoff-Transformed Heat Transfer for Adiabatic Mechanics:");
    Message::Info("Elasticity:");
    Message::Info("\tE Constant Parameter....... = %e", _E_at_0);
    Message::Info("\tE Derivative with Temperature....... = %e", _Et);
    Message::Info("\tG Constant Parameter....... = %e", _G_at_0);
    Message::Info("\tG Derivative with Temperature....... = %e", _Gt);
    Message::Info("Thermal Expansion:");
    Message::Info("\tAlpha Constant Parameter....... = %e", _alpha_a);
    Message::Info("\tAlpha Linear Parameter....... = %e", _alpha_b);
    Message::Info("\tAlpha Exponential Factor....... = %e", _alpha_c);
    Message::Info("\tAlpha Exponential Parameter....... = %e", _alpha_d);
    Message::Info("Heat Capacity:");
    Message::Info("\tCp Exponential Parameter....... = %e", _Cp_exp_param);
    Message::Info("\tCp Constant Parameter....... = %e", _Cp_constant);
    Message::Info("\tCp Linear Parameter....... = %e", _Cp_linear);
    Message::Info("\tCp Exponential Constant Parameter....... = %e", _Cp_expconstant);
    Message::Info("Thermal Conductivity:");
    Message::Info("\tKappa Constant Parameter....... = %e", _kappa_constant);
    Message::Info("\tKappa Exponential Constant Parameter....... = %e", _kappa_exp_factor);
    Message::Info("\tKappa Exponential Parameter....... = %e", _kappa_exp_param);
    Message::Info("\tKappa Fraction Denominator Parameter....... = %e", _kappa_exp_rational_denom);
    Message::Info("Initial Conditions and Numerical Parameters:");
    Message::Info("\tCenter of the Taylor Series....... = %e", _T_center_taylor_series);
    Message::Info("\tReference Density....... = %e", _physical_density);
    Message::Info("\tReference Temperature....... = %e", _temp_ref);

    // commented out because not physically meaningful to a user
    // Message::Info("Fields derived from the initial condition:");
    // Message::Info("\tReference Phi....... = %e", _phi_ref);
    // Message::Info("\tReference Entropy....... = %e", _eta_ref);

    return;
}

summit::real summit::ArmeroSimoThermal::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // these numbers are estimates of sup(kappa)/inf(Cp)
    // where the inf and sup are taken over the temperature range of the alumina parameters
    // please do not use this model in explicit mode anyways as it is designed entirely
    // to have nice properties for implicit integrators!
    return 115.0636 / (2.5e6);
}

summit::real summit::ArmeroSimoThermal::_T_from_phi(const real phi0, const real T0) const
{

    real TOL = 1e-12;
    
    //check the phi field is reasonable
    real phiMin = 2.516592714234719e5;
    if(phi0<phiMin){// this is absolute zero
        std::cout << "thermal phi value of: " << phi0<< " is too small" << std::endl;
        return 1000;
    }
    real T=T0;
    real kappaD,kappa,phi;
    this->_approximate_kappa(T, kappaD, kappa, phi);
    real error = phi -phi0;
    int iter = 0;
    while(std::abs(error)>std::abs(phi0)*TOL){
        T =T - error /kappa;
        this->_approximate_kappa(T, kappaD, kappa, phi);

        if(phi<phiMin){
            //reset to absolute zero
            phi = phiMin;
            kappa = 1.150636111363023e2;
            kappaD = -0.668089500030624;
        }
        error = phi -phi0;
        iter = iter+1;
    }

    return T;
}

void summit::ArmeroSimoThermal::Constitutive(const real* concentration0,
                                                           const real* concentration,
                                                           const real* Dconcentration0,
                                                           const real* Dconcentration,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    
    for (int component = 0; component < this->number_unknowns(); component++){
        for (int dimension = 0; dimension < ndm; dimension++){
            P[dimension + component * ndm] = Dconcentration[dimension + component * ndm];
            if(compute_tangents){
                tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = 1.0;
            }
        }
    }
    
    return;
}


summit::real summit::ArmeroSimoThermal::capacity(real const* internal, const int component) const
{
    return 0.0;//_heatCapacity * _rho;    
}

void summit::ArmeroSimoThermal::_setInternalVariableMap()
{   
    // all done
    SetLocationInInternalTable("Entropy", 0, 1);
    SetLocationInInternalTable("Temperature Estimate", 1, 1);
    SetLocationInInternalTable("Jacobian", 2, 1);
    SetLocationInInternalTable("devStrainSquared", 3, 1);

    return;
}

void summit::ArmeroSimoThermal::Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const
{   
    if(dt[0]<1e-19){
        return;
    }
    real oldEntropy, phi, T, phiCheck, kappaD, kappa, eta, D_eta, Jacobian, temp_est, devSt2;
    oldEntropy = q[0]+_eta_ref;
    Jacobian = q[2] + _jacobian_ref;
    temp_est = q[1] + _temp_ref;
    devSt2 = q[3];
    phi = concentration[0]+ _phi_ref;
    
    T = this->_T_from_phi(phi,temp_est);
    
    
    this->_approximate_kappa(T, kappaD, kappa, phiCheck);
    
    this->_entropy_and_variation(T, Jacobian, devSt2, eta, D_eta);
    q[1] = T - _temp_ref;

    T += 273.15;// conversion to Kelvin from Celsius!
    f[0] = T * (eta-oldEntropy)/dt[0];
    df[0] = (eta-oldEntropy)/(kappa*dt[0]) + T*D_eta/(kappa*dt[0]);
    q[0] = eta - _eta_ref;
    


    // this line is key for making sure the transient terms have the correct sign
    // conventionally they appear on the other side of the equation from sources,
    // so we require a minus sign to bake them into the source function
    f[0] *= -1;
    df[0] *= -1;
    
    return ;
}

void summit::ArmeroSimoThermal::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    return;
}

int summit::ArmeroSimoThermal::number_unknowns() const
{
    return 1;
}

real summit::ArmeroSimoThermal::bulkModulus(real const* internal) const
{
    return 1.0;
}


void summit::ArmeroSimoThermal::_get_alpha(const real T,
                      real &alpha,
                      real &alphat,
                      real &alphatt,
                      real &alphattt) const
{
    //CTE material parameters one could modify

    alpha = _alpha_a+_alpha_b*T-_alpha_c*exp(-_alpha_d*T);
    alphat = _alpha_b + _alpha_d * _alpha_c*exp(-_alpha_d*T);
    alphatt = -_alpha_d * _alpha_d * _alpha_c * exp(-_alpha_d*T);
    alphattt = _alpha_d * _alpha_d * _alpha_d * _alpha_c*exp(-_alpha_d*T);

    return ;
}

void summit::ArmeroSimoThermal::_entropy_and_variation(const real T,
                                                       const real J,
                                                       const real devStrainSquared,
                                                       real &eta,
                                                       real &D_eta ) const
{
    real W,Wt,Wtt, Wvol,Wvolt,Wvoltt, Wdev,Wdevt,Wdevtt, K, Kt, Ktt, Kttt, G, Gt, Gtt, Gttt, alpha, alphat, alphatt, alphattt, Cp,Cpt,Cptt,Cpttt;

    //this code is familiar from the mechanics model
    this->_get_bulk_modulus(T, K, Kt, Ktt, Kttt);
    this->_get_shear_modulus(T, G, Gt, Gtt, Gttt);
    this->_get_alpha(T, alpha, alphat, alphatt, alphattt);
    this->_get_thermal_free_energy(T, Cp,Cpt,Cptt,Cpttt);
    Wvol = K/2 *((std::log(J) - 3* alpha*T)*(std::log(J) - 3* alpha*T));
    Wvolt = Kt/2 * ((std::log(J) - 3* alpha*T)*(std::log(J) - 3* alpha*T)) + -3 * K * (std::log(J) - 3* alpha*T) *(alphat * T + alpha) ;
    Wvoltt = Ktt/2 * ((std::log(J) - 3* alpha*T)*(std::log(J) - 3* alpha*T)) + -6 * Kt * (std::log(J) - 3* alpha*T) *(alphat * T + alpha)+  9 * K *((alphat * T + alpha)*(alphat * T + alpha))  -3*K*(std::log(J)-3*alpha*T)*(alphatt * T + 2*alphat);

    Wdev = G * devStrainSquared;
    Wdevt = Gt * devStrainSquared;
    Wdevtt = Gtt * devStrainSquared;

    W=Wvol+Wdev+ Cp;
    Wt = Wvolt+Wdevt+ Cpt ;
    Wtt = Wdevtt + Wvoltt+Cptt;

    //this is the entropy and its first variation
    eta = -Wt;
    D_eta = -Wtt;

    return;
}

int summit::ArmeroSimoThermal::_factorial(const int n) const
{
    int f = 1;
    for (int i=1; i<=n; ++i){
        f *= i;
    }
    return f;
}

int summit::ArmeroSimoThermal::_n_choose_k(const int n, const int k) const
{
    return this->_factorial(n) /(this->_factorial(k)*this->_factorial(n-k)) ;
}


/**
     * Method to get the thermal conductivity
     */
void summit::ArmeroSimoThermal::_approximate_kappa(const real T,
                      real &kappaD,
                      real &kappa,
                      real &kappaI) const
{
    real mySum = 0;
    real mySumD = 0;
    real mySumI = 0;
    //real T0 = 1000;//centering of the Taylor series
    real newSum, newSumD, newSumI, factor;
    for (int n=0;n<=10;n++){
        newSum = 0;//this will become (T - _T_center_taylor_series)^(n)
        newSumD = 0;
        newSumI = 0;
        for (int k=0;k<=n;k++){
            factor = pow((-_T_center_taylor_series),k)* this->_n_choose_k(n,k);

            newSumI+= factor * this->_hyper_factor(T,n-k,_kappa_exp_rational_denom);
            newSum+= factor * pow(T,(n-k))/(T+_kappa_exp_rational_denom);
            newSumD+= factor * ((n-k)*pow(T,(n-k-1))/(T+_kappa_exp_rational_denom)  - pow(T,(n-k))/((T+_kappa_exp_rational_denom)*(T+_kappa_exp_rational_denom)));
        }
        factor = pow(-_kappa_exp_param,n) / this->_factorial(n);

        mySumI += factor * newSumI ;
        mySum += factor * newSum ;
        mySumD += factor * newSumD ;
    }
    factor = exp(-_kappa_exp_param * _T_center_taylor_series);
    kappaD = _kappa_exp_factor * factor * mySumD; 
    kappa = _kappa_constant+_kappa_exp_factor * factor * mySum;
    kappaI = _kappa_constant*T+_kappa_exp_factor * factor * mySumI;
    
    return;
}

real summit::ArmeroSimoThermal::_hyper_factor(const real T,
                      const int n,
                      const real d) const
{
    //integrates T^n/(T+d);
    real res=0;
    if(n==0){
        res = log(d+T);
    }
    if(n==1){
        res = T-d*log(d+T);
    }
    if(n==2){
        res = -d*T+pow(T,2)/2.0+pow(d,2)*log(d+T);
    }
    if(n==3){
        res = pow(d,2)*T-d*pow(T,2)/2.0+pow(T,3)/3.0-pow(d,3)*log(d+T);
    }
    if(n==4){
        res = -pow(d,3)*T + (pow(d,2) *pow(T,2))/2.0 - (d * pow(T,3))/3.0 + pow(T,4)/4.0 + pow(d,4)* log(d + T);
    }
    if(n==5){
        res = pow(d,4)*T - (pow(d,3)*pow(T,2))/2.0 + (pow(d,2)*pow(T,3))/3.0 - (d*pow(T,4))/4.0 + pow(T,5)/5.0 - pow(d,5) *log(d + T);
    }
    if(n==6){
        res = -pow(d,5)*T + (pow(d,4)*pow(T,2))/2.0 - (pow(d,3)*pow(T,3))/3.0 + (pow(d,2)*pow(T,4))/4.0 - (d*pow(T,5))/5.0 + pow(T,6)/6.0 + pow(d,6) *log(d + T);
    }
    if(n==7){
        res = pow(d,6)*T-(pow(d,5)*pow(T,2))/2.0+(pow(d,4)*pow(T,3))/3.0-(pow(d,3)*pow(T,4))/4.0+(pow(d,2)*pow(T,5))/5.0-(d*pow(T,6))/6.0+pow(T,7)/7.0-pow(d,7)*log(d+T);
    }
    if(n==8){
        res = -pow(d,7)*T+(pow(d,6)*pow(T,2))/2.0-(pow(d,5)*pow(T,3))/3.0+(pow(d,4)*pow(T,4))/4.0-(pow(d,3)*pow(T,5))/5.0+(pow(d,2)*pow(T,6))/6.0-(d*pow(T,7))/7.0+pow(T,8)/8.0+pow(d,8)*log(d+T);
    }
    if(n==9){
        res = pow(d,8)*T-(pow(d,7)*pow(T,2))/2.0+(pow(d,6)*pow(T,3))/3.0-(pow(d,5)*pow(T,4))/4.0+(pow(d,4)*pow(T,5))/5.0-(pow(d,3)*pow(T,6))/6.0+(pow(d,2)*pow(T,7))/7.0-(d*pow(T,8))/8.0+pow(T,9)/9.0-pow(d,9)*log(d+T);
    }
    if(n==10){
        res = -pow(d,9)*T+(pow(d,8)*pow(T,2))/2.0-(pow(d,7)*pow(T,3))/3.0+(pow(d,6)*pow(T,4))/4.0-(pow(d,5)*pow(T,5))/5.0+(pow(d,4)*pow(T,6))/6.0-(pow(d,3)*pow(T,7))/7.0+(pow(d,2)*pow(T,8))/8.0-(d*pow(T,9))/9.0+pow(T,10)/10.0+pow(d,10)*log(d+T);
    }

    if(n>10){
        std::cout << "hyper factor function not yet implemented for n: " << n <<std::endl;
    }

    return res;
}

void summit::ArmeroSimoThermal::_get_thermal_free_energy(const real T,
                      real &W,
                      real &Wt,
                      real &Wtt,
                      real &Wttt) const
{
    //computation of the terms
    real mySumD = 0;
    real mySum = 0;
    real mySumInt = 0;
    real mySumIntInt = 0;
    real newSum, newSumD, newSumInt, newSumIntInt;
    real one_over_nFact, expParam_to_n, nFact;
    for (int n=0; n<=6; ++n){
        newSum=0;
        newSumD=0;
        for (int k=0; k<=n; ++k){
            newSum+= std::pow(T,(n-k-1))*std::pow((-_T_center_taylor_series),k)*this->_n_choose_k(n,k);
            newSumD+= std::pow(T,(n-k-2))*std::pow((-_T_center_taylor_series),k)*this->_n_choose_k(n,k) * (n-k-1);
        }
        newSumInt = std::log(T)*std::pow((-_T_center_taylor_series),n);
        newSumIntInt = (T*std::log(T)-T) *std::pow((-_T_center_taylor_series),n);
        for (int k=0; k<=n-1; ++k){
            newSumInt+= std::pow(T,(n-k)) /(n-k) *std::pow((-_T_center_taylor_series),k)*this->_n_choose_k(n,k);
            newSumIntInt+= std::pow(T,(n-k+1)) /((n-k+1)*(n-k)) *std::pow((-_T_center_taylor_series),k)*this->_n_choose_k(n,k);
        }
        nFact = this->_factorial(n);
        one_over_nFact = (1.0/nFact);
        expParam_to_n = std::pow((- _Cp_exp_param),n);
        mySumD += one_over_nFact * expParam_to_n * newSumD;
        mySum += one_over_nFact * expParam_to_n * newSum; 
        mySumInt +=  one_over_nFact * expParam_to_n * newSumInt;
        mySumIntInt +=  one_over_nFact * expParam_to_n * newSumIntInt;
    }

    Wttt = _Cp_constant/(T*T)+_Cp_expconstant * exp(-_Cp_exp_param * _T_center_taylor_series) * (mySumD);
    Wtt = -_Cp_constant/T-_Cp_linear+_Cp_expconstant * exp(-_Cp_exp_param * _T_center_taylor_series) * (mySum);
    Wt = -_Cp_constant*std::log(T)-_Cp_linear*T+_Cp_expconstant * exp(-_Cp_exp_param * _T_center_taylor_series) * (mySumInt);
    W = -_Cp_constant*(T*std::log(T)-T)-_Cp_linear*T*T/2+_Cp_expconstant * exp(-_Cp_exp_param * _T_center_taylor_series) * (mySumIntInt);
    
    
    W *= _physical_density;
    Wt *= _physical_density;
    Wtt *= _physical_density;
    Wttt *= _physical_density;

    return ;
}

void summit::ArmeroSimoThermal::_get_shear_modulus(const real T,
                      real &G,
                      real &Gt,
                      real &Gtt,
                      real &Gttt) const
{
    //shear modulus material parameters one could modify
    G = _G_at_0+_Gt*T;
    Gt = _Gt;
    Gtt = 0;
    Gttt = 0;
    return ;
}

void summit::ArmeroSimoThermal::_get_bulk_modulus(const real T,
                      real &B,
                      real &Bt,
                      real &Btt,
                      real &Bttt) const
{
    real E, Et, Ett, Ettt;
    this->_get_youngs_modulus(T, E, Et, Ett, Ettt);
    real nu, nut, nutt, nuttt;
    this->_get_poisson_ratio(T, nu, nut, nutt, nuttt);

    real one_minus_two_nu_2 = std::pow((1-2*nu),2);
    real one_minus_two_nu_3 = std::pow((1-2*nu),3);

    B = E / (3*(1-2*nu));
    Bt = Et/ (3*(1-2*nu)) +6 * nut * E / std::pow((3*(1-2*nu)),2);
    Btt =Ett/ (3-6*nu) +4*nut*Et/(3*std::pow((1-2*nu),2)) +  2*nutt*E/(3*one_minus_two_nu_2) +8*nut*nut*E/ (3*one_minus_two_nu_3);
    Bttt = Ettt/ (3-6*nu)+6*Ett*nut/ ((3-6*nu)*(3-6*nu)) + 4*nutt*Et/(3*one_minus_two_nu_2) + 4*nut*Ett/(3*one_minus_two_nu_2) + 16*nut*nut*Et/(3*one_minus_two_nu_3)+ 2*nuttt*E/(3*one_minus_two_nu_2)+2*nutt*Et/(3*one_minus_two_nu_2) + 8*nutt*E*nut/(3*one_minus_two_nu_3)+ 8*nut*nut*Et/ (3*one_minus_two_nu_3) + 16*nut*nutt*E/ (3*one_minus_two_nu_3)+16*nut*nut*nut*E/ (std::pow((1-2*nu),4));
    return ;
}

void summit::ArmeroSimoThermal::_get_poisson_ratio(const real T,
                      real &nu,
                      real &nut,
                      real &nutt,
                      real &nuttt) const
{
    real E, Et, Ett, Ettt;
    this->_get_youngs_modulus(T, E, Et, Ett, Ettt);
    real G, Gt, Gtt, Gttt;
    this->_get_shear_modulus(T, G, Gt, Gtt, Gttt);
    nu = E/(2*G)-1;
    nut = Et / (2*G) -E*Gt/(2*std::pow(G,2));
    nutt = Ett / (2*G) -Et*Gt/std::pow(G,2) - E*Gtt/ (2*std::pow(G,2)) + E*std::pow(Gt,2)/std::pow(G,3);
    nuttt = Ettt/(2*G)-Ett*Gt/(2*std::pow(G,2)) - Ett*Gt/std::pow(G,2)-Et*Gtt/std::pow(G,2)+2*Et*std::pow(Gt,2)/std::pow(G,3) - Et*Gtt/(2*std::pow(G,2))-E*Gttt/(2*std::pow(G,2))+E*Gtt*Gt/(std::pow(G,3)) + Et*std::pow(Gt,2)/std::pow(G,3)+2*E*Gt*Gtt/std::pow(G,3)-3*E*std::pow(Gt,3)/std::pow(G,4);
    return ;
}

void summit::ArmeroSimoThermal::_get_youngs_modulus(const real T,
                      real &E,
                      real &Et,
                      real &Ett,
                      real &Ettt) const
{
    //youngs modulus material parameters one could modify
    
    E = _E_at_0 +_Et*T;
    Et = _Et;
    Ett =0;
    Ettt=0;
    return ;
}


// end of file
