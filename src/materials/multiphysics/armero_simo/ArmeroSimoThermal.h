#ifndef SUMMIT_ARMERO_SIMO_THERMAL_H
#define SUMMIT_ARMERO_SIMO_THERMAL_H

#include "../../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material ArmeroSimoThermal
#define ARMERO_SIMO_THERMAL_NUMBER_INTERNAL_VARIABLES 4

namespace summit {

/**
 * @class ArmeroSimoThermal
 * @brief Implements a thermal model for the Armero-Simo thermo-mechanical coupling
 * 
 * This class implements the thermal part of the Armero-Simo thermo-mechanical model,
 * handling heat transfer with temperature-dependent thermal properties. It uses a
 * Kirchhoff transformation approach to handle the nonlinear heat conduction problem.
 * 
 * The model accounts for temperature-dependent thermal conductivity, heat capacity,
 * and thermal expansion, and is designed to work in conjunction with the ArmeroSimo
 * mechanical material model.
 */
class ArmeroSimoThermal : public ReactionDiffusionMaterial {
  public:
    /**
     * @brief Constructor with name
     * 
     * @param[in] name A string that defines the name of the material
     */
    ArmeroSimoThermal(const std::string& name);
    
    /**
     * @brief Destructor
     */
    virtual ~ArmeroSimoThermal();

    /**
     * @brief Displays the material parameters to the output channel
     * 
     * Prints all material parameters including thermal conductivity, heat capacity,
     * and thermal expansion parameters.
     */
    virtual void Display();

    /**
     * @brief Returns the number of unknown fields in the model
     * 
     * @return Number of unknown fields (1 for temperature)
     */
    virtual int number_unknowns() const;

    /**
     * @brief Loads material parameters from a file
     * 
     * @param[in] filename Path to the material parameter file
     * @param[in] line Line from the file containing parameters for this material
     * 
     * Expected parameters in the input file:
     * - E_at_0, Et: Young's modulus parameters
     * - G_at_0, Gt: Shear modulus parameters
     * - alpha_a, alpha_b, alpha_c, alpha_d: Thermal expansion parameters
     * - Cp parameters: Heat capacity parameters
     * - kappa parameters: Thermal conductivity parameters
     * - T_center_taylor_series: Center temperature for Taylor series expansions
     * - physical_density: Material density
     * - temp_ref: Reference temperature
     * - mode: Stress/strain state (0=plane stress, 1=plane strain, 2=general 3D)
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * @brief Computes the diffusivity for the critical time step
     * 
     * @param[in] Fn Pointer to deformation gradient
     * @param[in] q Pointer to internal variables
     * @param[in] ndm Dimension of the problem
     * @param[in] component Component index for multi-field problems
     * @return Thermal diffusivity
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * @brief Implements the constitutive law for heat transfer
     * 
     * Computes the heat flux and updates internal variables based on the
     * temperature gradient and current state.
     * 
     * @param[in] concentration0 Previous concentration field
     * @param[in] concentration Current concentration field
     * @param[in] Dconcentration0 Previous concentration gradient
     * @param[in] Dconcentration Current concentration gradient
     * @param[out] P Heat flux
     * @param[in,out] q Internal variables
     * @param[out] tangent Tangent modulus
     * @param[out] dPdu Derivative of flux with respect to field
     * @param[in] dtime Time step
     * @param[in] ndf Degrees of freedom per node
     * @param[in] ndm Dimension of the computational domain
     * @param[in] compute_tangents Flag for computing tangent moduli
     * @param[in] artVisc_interface_activate Flag for artificial viscosity
     */
    void Constitutive(const real* concentration0,
                      const real* concentration,
                      const real* Dconcentration0,
                      const real* Dconcentration,
                      real* P,
                      real* q,
                      real* tangent,
                      real* dPdu,
                      real dtime,
                      const int ndf,
                      const int ndm,
                      bool compute_tangents = false,
                      bool artVisc_interface_activate = true) const override;

    /**
     * @brief Computes the source term for the heat equation
     *
     * Calculates the source term and its derivative for the heat equation,
     * accounting for entropy changes and temperature-dependent properties.
     *
     * @param[in] concentration0 Previous concentration field
     * @param[in] concentration Current concentration field
     * @param[in] Dconcentration0 Previous concentration gradient field
     * @param[in] Dconcentration Current concentration gradient field
     * @param[in,out] q Internal variables
     * @param[in] dt Time step
     * @param[out] f Source term
     * @param[out] df Derivative of source term
     * @param[out] dfdGrad Derivative of source term with respect to gradients
     * @param[in] ndm Number of spatial dimensions
     * @param[in] ndf Degrees of freedom per node
     */
    void Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const;

    /**
     * @brief Computes the convective flux term
     * 
     * @param[in] concentration0 Previous concentration field
     * @param[in] concentration Current concentration field
     * @param[in,out] q Internal variables
     * @param[in] dt Time step
     * @param[out] F Convective flux
     * @param[out] dF Derivative of convective flux
     * @param[in] ndf Degrees of freedom per node
     * @param[in] ndm Dimension of the computational domain
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * @brief Returns the heat capacity
     * 
     * @param[in] internalVariables Pointer to internal variables
     * @param[in] component Component index for multi-field problems
     * @return Heat capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    /**
     * @brief Returns the bulk modulus
     * 
     * @param[in] internal Pointer to internal variables
     * @return Bulk modulus
     */
    real bulkModulus(real const* internal) const;

    /**
     * @brief Calculates temperature from the Kirchhoff variable phi
     * 
     * Inverts the Kirchhoff transformation to obtain temperature from phi
     * using a Newton-Raphson iteration method.
     * 
     * @param[in] phi0 Kirchhoff variable value
     * @param[in] T0 Initial temperature guess
     * @return Calculated temperature
     */
    real _T_from_phi(const real phi0, const real T0) const;

    /**
     * @brief Returns the reference value of the Kirchhoff variable
     * 
     * @return Reference value of phi
     */
    real _get_phi_ref() const { return _phi_ref; };

    /**
     * @brief Approximates thermal conductivity and its derivatives
     * 
     * Computes the thermal conductivity (kappa) and its derivatives using
     * a Taylor series approximation
     * 
     * @param[in] T Temperature
     * @param[out] kappaD Derivative of kappa with respect to temperature
     * @param[out] kappa Thermal conductivity
     * @param[out] kappaI Integral of kappa (used for Kirchhoff transformation)
     */
    void _approximate_kappa(const real T,
                      real &kappaD,
                      real &kappa,
                      real &kappaI) const;
    
  protected:
    /**
     * @brief Calculates thermal expansion coefficient and its derivatives
     * 
     * Computes the thermal expansion coefficient (alpha) and its derivatives with respect
     * to temperature using the formula: alpha = a + b*T - c*exp(-d*T)
     * 
     * @param[in] T Temperature
     * @param[out] alpha Thermal expansion coefficient
     * @param[out] alphat First derivative of alpha with respect to temperature
     * @param[out] alphatt Second derivative of alpha with respect to temperature
     * @param[out] alphattt Third derivative of alpha with respect to temperature
     */
    void _get_alpha(const real T,
                      real &alpha,
                      real &alphat,
                      real &alphatt,
                      real &alphattt) const;

    /**
     * @brief Calculates shear modulus and its derivatives
     * 
     * Computes the shear modulus (G) and its derivatives with respect to temperature
     * using the formula: G = G_at_0 + Gt*T
     * 
     * @param[in] T Temperature
     * @param[out] G Shear modulus
     * @param[out] Gt First derivative of G with respect to temperature
     * @param[out] Gtt Second derivative of G with respect to temperature
     * @param[out] Gttt Third derivative of G with respect to temperature
     */
    void _get_shear_modulus(const real T,
                      real &G,
                      real &Gt,
                      real &Gtt,
                      real &Gttt) const;
    
    /**
     * @brief Calculates bulk modulus and its derivatives
     * 
     * Computes the bulk modulus (B) and its derivatives with respect to temperature
     * based on Young's modulus and Poisson's ratio
     * 
     * @param[in] T Temperature
     * @param[out] B Bulk modulus
     * @param[out] Bt First derivative of B with respect to temperature
     * @param[out] Btt Second derivative of B with respect to temperature
     * @param[out] Bttt Third derivative of B with respect to temperature
     */
    void _get_bulk_modulus(const real T,
                      real &B,
                      real &Bt,
                      real &Btt,
                      real &Bttt) const;

    /**
     * @brief Calculates Young's modulus and its derivatives
     * 
     * Computes Young's modulus (E) and its derivatives with respect to temperature
     * using the formula: E = E_at_0 + Et*T
     * 
     * @param[in] T Temperature
     * @param[out] E Young's modulus
     * @param[out] Et First derivative of E with respect to temperature
     * @param[out] Ett Second derivative of E with respect to temperature
     * @param[out] Ettt Third derivative of E with respect to temperature
     */
    void _get_youngs_modulus(const real T,
                      real &E,
                      real &Et,
                      real &Ett,
                      real &Ettt) const;
    
    /**
     * @brief Calculates Poisson's ratio and its derivatives
     * 
     * Computes Poisson's ratio (nu) and its derivatives with respect to temperature
     * based on Young's modulus and shear modulus: nu = E/(2*G) - 1
     * 
     * @param[in] T Temperature
     * @param[out] nu Poisson's ratio
     * @param[out] nut First derivative of nu with respect to temperature
     * @param[out] nutt Second derivative of nu with respect to temperature
     * @param[out] nuttt Third derivative of nu with respect to temperature
     */
    void _get_poisson_ratio(const real T,
                      real &nu,
                      real &nut,
                      real &nutt,
                      real &nuttt) const;

    /**
     * @brief Calculates thermal free energy and its derivatives
     * 
     * Computes the thermal component of the free energy and its derivatives
     * with respect to temperature
     * 
     * @param[in] T Temperature
     * @param[out] W Thermal free energy
     * @param[out] Wt First derivative of W with respect to temperature
     * @param[out] Wtt Second derivative of W with respect to temperature
     * @param[out] Wttt Third derivative of W with respect to temperature
     */
    void _get_thermal_free_energy(const real T,
                      real &W,
                      real &Wt,
                      real &Wtt,
                      real &Wttt) const;

    /**
     * @brief Calculates a hypergeometric factor used in thermal conductivity approximation
     * 
     * @param[in] T Temperature
     * @param[in] n Order of the term
     * @param[in] d Denominator parameter
     * @return Hypergeometric factor
     */
    real _hyper_factor(const real T,
                      int n,
                      const real d) const;

    /**
     * @brief Calculates factorial of n
     * 
     * @param[in] n Integer input
     * @return n!
     */
    int _factorial(const int n) const;

    /**
     * @brief Calculates binomial coefficient (n choose k)
     * 
     * @param[in] n Upper value
     * @param[in] k Lower value
     * @return Binomial coefficient n!/(k!(n-k)!)
     */
    int _n_choose_k(const int n, const int k) const;

    /**
     * @brief Calculates entropy and its variation
     * 
     * Computes the entropy and its first variation based on temperature,
     * Jacobian of deformation, and deviatoric strain
     * 
     * @param[in] T Temperature
     * @param[in] J Jacobian of deformation
     * @param[in] devStrainSquared Square of deviatoric strain
     * @param[out] eta Entropy
     * @param[out] D_eta First variation of entropy
     */
    void _entropy_and_variation(const real T, const real J, const real devStrainSquared, real &eta, real &D_eta ) const;

    /**
     * @brief Mode flag for stress/strain state
     * 
     * 0 = plane stress, 1 = plane strain, 2 = general 3D
     */
    int _mode;
    
    /**
     * @brief Heat capacity parameters
     * 
     * Parameters for the heat capacity model:
     * Cp = Cp_constant + Cp_linear*T + Cp_expconstant*exp(-Cp_exp_param*T)
     */
    real _Cp_exp_param;
    real _Cp_constant;
    real _Cp_linear;
    real _Cp_expconstant;
    
    /**
     * @brief Physical density of the material
     */
    real _physical_density;
    
    /**
     * @brief Thermal conductivity parameters
     * 
     * Parameters for the thermal conductivity model:
     * kappa = kappa_constant + kappa_exp_factor*exp(-kappa_exp_param*T)/(T+kappa_exp_rational_denom)
     */
    real _kappa_constant;
    real _kappa_exp_factor;
    real _kappa_exp_param;
    real _kappa_exp_rational_denom;
    
    /**
     * @brief Center temperature for Taylor series expansions
     */
    real _T_center_taylor_series;

    /**
     * @brief Young's modulus parameters
     * 
     * Parameters for temperature-dependent Young's modulus:
     * E = E_at_0 + Et*T
     */
    real _E_at_0;
    real _Et;

    /**
     * @brief Shear modulus parameters
     * 
     * Parameters for temperature-dependent shear modulus:
     * G = G_at_0 + Gt*T
     */
    real _G_at_0;
    real _Gt;

    /**
     * @brief Thermal expansion parameters
     * 
     * Parameters for temperature-dependent thermal expansion:
     * alpha = alpha_a + alpha_b*T - alpha_c*exp(-alpha_d*T)
     */
    real _alpha_a;
    real _alpha_b;
    real _alpha_c;
    real _alpha_d;

    /**
     * @brief Reference temperature
     * 
     * Default initial condition temperature
     */
    real _temp_ref;
    
    /**
     * @brief Reference Jacobian
     * 
     * Default initial condition for volumetric deformation (1.0 = undeformed)
     */
    real _jacobian_ref = 1;

    /**
     * @brief Reference values for transfer operators
     * 
     * Parameters calculated for thermal-mechanical coupling
     */
    real _phi_ref;
    real _eta_ref;
    
    /**
     * @brief Reference parameters for wave speed calculation
     * 
     * Parameters needed for celerity function and time stepping
     */
    real _bulk_ref;
    real _nu_ref;
    real _rho;
    
  private:
    /**
     * @brief Sets up the mapping from internal variable names to their locations
     * 
     * Defines the structure of the internal variables array with the following variables:
     * - Entropy (index 0)
     * - Temperature Estimate (index 1)
     * - Jacobian (index 2)
     * - devStrainSquared (index 3)
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
