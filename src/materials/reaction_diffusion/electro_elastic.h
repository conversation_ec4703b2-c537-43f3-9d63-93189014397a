/**
 * @file electro_elastic.h
 * @brief Electro-elastic coupled reaction-diffusion material model
 * <AUTHOR> Development Team
 * @date 2011-2023
 *
 * This file contains the ElectroElastic class which implements a coupled
 * electro-elastic reaction-diffusion material model. This model combines
 * electrical, mechanical, and chemical phenomena for multiphysics simulations.
 */

#ifndef SUMMIT_ELECTRO_ELASTIC_H
#define SUMMIT_ELECTRO_ELASTIC_H

#include "../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material ElectroElastic
                                         // 0 homogeneous reactions
#define ELECTRO_ELASTIC_NUMBER_INTERNAL_VARIABLES 12

namespace summit {

/**
 * @brief Electro-elastic coupled reaction-diffusion material
 *
 * The ElectroElastic class implements a multiphysics material model that couples
 * electrical, mechanical, and chemical phenomena. This model is particularly
 * useful for applications involving:
 *
 * - Piezoelectric materials with chemical reactions
 * - Electrochemical processes with mechanical deformation
 * - Smart materials with coupled field effects
 * - Battery and fuel cell modeling
 * - Electro-mechanical actuators and sensors
 *
 * Key features:
 * - Coupled electrical-mechanical-chemical behavior
 * - Strain-dependent electrical properties
 * - Electric field-dependent reaction rates
 * - Mechanical stress effects on diffusion
 * - Multiple internal variables for complex coupling
 *
 * @note This model requires 12 internal variables for proper multiphysics coupling
 */
class ElectroElastic : public ReactionDiffusionMaterial {
  public:

    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    ElectroElastic(const std::string& name);

    /**
     * Destructor
     */
    virtual ~ElectroElastic();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    ElectroElastic(const ElectroElastic&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    ElectroElastic& operator=(const ElectroElastic&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * ElectroElastic constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf) const;


    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 
  
  protected: 

    /**
     * final mass fraction
     */
    real _rho;

    /**
     * solid thermal conductivity
     */
    real _C1;

    /**
     * first term
     */
    real _C2;

    /**
     * first term
     */
    real _mu;

    /**
     * first term
     */
    real _lambda;    

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
