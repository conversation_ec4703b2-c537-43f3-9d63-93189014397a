/**
 * @file nonlinear_conduction.h
 * @brief Nonlinear heat conduction material model
 * <AUTHOR> Development Team
 * @date 2011-2023
 */

#ifndef SUMMIT_NONLINEAR_CONDUCTION_H
#define SUMMIT_NONLINEAR_CONDUCTION_H

#include "../reaction_diffusion_material.h"
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

// Number of internal variables:
// the value is used to size the memory allocation for the material NonlinearConduction
                                         // 0 homogeneous reactions
#define NONLINEAR_CONDUCTION_NUMBER_INTERNAL_VARIABLES 6

namespace summit {

/**
 * @brief Nonlinear heat conduction material model
 *
 * Implements nonlinear heat conduction with temperature-dependent properties:
 * ρcp ∂T/∂t = ∇·(k(T)∇T) + Q̇(T)
 *
 * Features:
 * - Temperature-dependent thermal conductivity k(T)
 * - Temperature-dependent heat capacity cp(T)
 * - Nonlinear heat generation Q̇(T)
 * - Phase change effects (latent heat)
 * - Radiation boundary conditions
 *
 * Applications:
 * - High-temperature heat transfer
 * - Phase change materials
 * - Nonlinear thermal processes
 * - Temperature-dependent material properties
 *
 * @note Uses 6 internal variables for complex thermal history tracking
 */
class NonlinearConduction : public ReactionDiffusionMaterial {
  public:

    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    NonlinearConduction(const std::string& name);

    /**
     * Destructor
     */
    virtual ~NonlinearConduction();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    NonlinearConduction(const NonlinearConduction&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    NonlinearConduction& operator=(const NonlinearConduction&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    #ifdef WITH_YAML_CPP
    /**
     * Method to load material properties from a YAML node
     * @param[in] node the YAML node for this specific material
     */
    virtual void Load(const YAML::Node &node);
    #endif
    
    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * NonlinearConduction constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf) const;


    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 
  
  protected: 

    /**
     * final mass fraction
     */
    real _rho;

    /**
     * solid Cv
     */
    real _Cv;

    /**
     * first term
     */
    real _A;

    /**
     * second term
     */
    real _B;

    /**
     * third term
     */
    real _C;

    /**
     * fourth term
     */
    real _D;

    /**
     * coefficient of thermal expansion
     */
    real _cte;

    /**
     * bulk modulus
     */
    real _bulk;

    /**
     * reference temperature
     */
    real _Tref;

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
