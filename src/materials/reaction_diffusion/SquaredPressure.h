/**
 * @file SquaredPressure.h
 * @brief Squared pressure thermal reaction-diffusion material model
 * <AUTHOR> Development Team
 * @date 2011-2023
 *
 * This file contains the SquaredPressure class which implements a reaction-diffusion
 * material model with squared pressure dependence and thermal effects. This model
 * is used for problems involving pressure-dependent reaction rates and thermal coupling.
 */

#ifndef SUMMIT_SQUARED_PRESSURE_THERMAL_H
#define SUMMIT_SQUARED_PRESSURE_THERMAL_H

#include "../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material SquaredPressure
                                         // 0 homogeneous reactions
#define SQUARED_PRESSURE_THERMAL_NUMBER_INTERNAL_VARIABLES 9

namespace summit {

/**
 * @brief Squared pressure thermal reaction-diffusion material
 *
 * The SquaredPressure class implements a reaction-diffusion material model where
 * the reaction rate depends on the square of the pressure and includes thermal
 * effects. This model is particularly useful for:
 *
 * - Pressure-dependent chemical reactions
 * - Thermal coupling in reactive systems
 * - Combustion and explosion modeling
 * - High-pressure chemical processes
 *
 * Key features:
 * - Quadratic pressure dependence in reaction terms
 * - Thermal coupling with temperature-dependent properties
 * - Multiple internal variables for complex reaction kinetics
 * - Integration with the Summit reaction-diffusion framework
 *
 * @note This model requires 9 internal variables for proper state tracking
 */
class SquaredPressure : public ReactionDiffusionMaterial {
  public:

    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    SquaredPressure(const std::string& name);

    /**
     * Destructor
     */
    virtual ~SquaredPressure();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    SquaredPressure(const SquaredPressure&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    SquaredPressure& operator=(const SquaredPressure&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * SquaredPressure constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf) const;

    /**
     * Method to know if the interface law allows for crack propagation
     * @return true if fracture is possible
     */
    int IsPressureSquared() const { return _variational; }

    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 
  
    /**
     * Method to perform change of variables for nonlinear heat transfer
     */
    real _P_from_phi(const real phi0, const real T0) const;

    /**
     * Method to perform change of variables for nonlinear heat transfer
     */
    real _phi_from_P(const real P, const real T0) const;

    /**
     * Method to perform change of variables for nonlinear heat transfer
     */
    real _get_phi_ref() const{ return _phiRef; };

    /**
     * Method to perform change of variables for nonlinear heat transfer
     */
    real _get_theta_ref() const{ return _thetaRef; };
    
  protected:

    //cp and kappa material parameters one could modify
    real _conductivity;
    real _permeability;
    real _heatCapacity;
    real _gasConstant;
    real _viscosity;
    real _molarMass;
    real _porosity;
    real _phiRef;
    real _thetaRef;
    int _variational;// zero, we do the default, one we add reaction terms, two we do MOMS
    int _modeOperation;// zero, we do the default, one we add reaction terms, two we do MOMS

    real _rateConstant;
    real _activationEnergy;
    real _rateExponent;
    real _reactionEnthalpy;
    real _reactionMass;
    real _Coefficient;
    real _typicalLengthScale;

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();

};
}  // namespace summit

#endif
