/**
 * @file linear_piezo.h
 * @brief Linear piezoelectric material model
 * <AUTHOR> Development Team
 * @date 2011-2023
 */

#ifndef SUMMIT_LINEAR_PIEZO_H
#define SUMMIT_LINEAR_PIEZO_H

#include "../reaction_diffusion_material.h"

namespace summit {

/**
 * @brief Linear piezoelectric material model
 *
 * Implements linear piezoelectric coupling between mechanical and electrical fields.
 * The governing equations couple:
 * - Mechanical equilibrium: ∇·σ = 0
 * - Electrical equilibrium: ∇·D = 0
 * - Constitutive relations: σ = C:ε - e^T·E, D = e:ε + κ·E
 *
 * Where:
 * - σ: stress tensor, ε: strain tensor
 * - E: electric field, D: electric displacement
 * - C: elastic stiffness, e: piezoelectric coupling, κ: permittivity
 *
 * Applications:
 * - Piezoelectric sensors and actuators
 * - Energy harvesting devices
 * - Smart material structures
 * - Ultrasonic transducers
 */
class LinearPiezo : public ReactionDiffusionMaterial {
  public:
  
    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    LinearPiezo(const std::string& name);

    /**
     * Destructor
     */
    virtual ~LinearPiezo();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    LinearPiezo(const LinearPiezo&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    LinearPiezo& operator=(const LinearPiezo&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);
    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;
    /**
     * LinearPiezo constitutive update, all tensors are ROW MAJOR
     */
    virtual void Constitutive(const real* V0,
                              const real* V,
                              const real* E0,
                              const real* E,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const;
    /**
     * set the source term
     */
    void Source(const real* V0, const real* V, real* q, real* dt, real* f, real* df, size_t ndf) const;


    /**
     * set the source term
     */
    void ConvectiveFlux(const real* V0, const real* V, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 
  
  protected:

      /**
     * density
     */
    real _rho;

    /**
     * piezoelectric constants (row major)
     */
    real _d[18];

    /**
     * dielectric constants - these are symmetric! (row major)
     */
    real _p[9];

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
