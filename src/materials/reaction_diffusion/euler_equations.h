/**
 * @file euler_equations.h
 * <AUTHOR> pickard (<EMAIL>)
 * @brief A material model implementing the Euler equations for compressible flow
 * 
 * This file implements a material model for the Euler equations, which describe
 * inviscid compressible flow and form the basis for many computational fluid
 * dynamics applications.
 */

#ifndef SUMMIT_EULER_EQUATIONS_H
#define SUMMIT_EULER_EQUATIONS_H

#include "../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material EulerEquations
                                         // 0 homogeneous reactions
#define EULER_EQUATIONS_NUMBER_INTERNAL_VARIABLES 3

namespace summit {

/**
 * @brief Implementation of the Euler equations for compressible flow
 * 
 * @details This class implements the Euler equations for inviscid compressible flow,
 * which are a set of conservation laws for mass, momentum, and energy. The model
 * accounts for:
 * 
 * - Mass conservation
 * - Momentum conservation in all spatial directions
 * - Energy conservation
 * - Compressibility effects
 * - Shock waves and discontinuities
 * 
 * The governing equations in conservation form are:
 * 
 * \f[ \frac{\partial \rho}{\partial t} + \nabla \cdot (\rho \vec{v}) = 0 \f]
 * \f[ \frac{\partial (\rho \vec{v})}{\partial t} + \nabla \cdot (\rho \vec{v} \otimes \vec{v} + p\mathbf{I}) = 0 \f]
 * \f[ \frac{\partial E}{\partial t} + \nabla \cdot ((E + p)\vec{v}) = 0 \f]
 * 
 * where \f$\rho\f$ is density, \f$\vec{v}\f$ is velocity, \f$p\f$ is pressure,
 * \f$E\f$ is total energy per unit volume, and \f$\mathbf{I}\f$ is the identity tensor.
 * 
 * Applications include:
 * - High-speed aerodynamics
 * - Gas dynamics
 * - Astrophysical flows
 * - Blast wave propagation
 */
class EulerEquations : public ReactionDiffusionMaterial {
  public:

    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    EulerEquations(const std::string& name);

    /**
     * Destructor
     */
    virtual ~EulerEquations();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    EulerEquations(const EulerEquations&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    EulerEquations& operator=(const EulerEquations&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * EulerEquations constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, const real* Dconcentration0, const real* Dconcentration, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const;


    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 
  
    /**
     * Compute the bulk modulus
     */
    void upwindStabilization(const real* concentration_L, const real* concentration_L0, const real* concentration_R, const real* concentration_R0, const real* Normal, real* C, real* dC) const;//this should be made virtual and reimplemented in future

  protected: 
    /**
     * bulk modulus
     */
    int _spatial_dimension;

    /**
     * bulk modulus
     */
    bool _implicit;

    /**
     * reference temperature
     */
    real _gamma;

    /**
     * Lax Friedrich Parameter
     */
    real _lax_parameter;

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
