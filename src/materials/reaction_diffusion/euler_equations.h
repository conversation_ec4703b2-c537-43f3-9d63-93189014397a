/**
 * @file euler_equations.h
 * @brief Euler equations for compressible fluid dynamics
 * <AUTHOR> Development Team
 * @date 2011-2023
 *
 * This file contains the EulerEquations class which implements the Euler equations
 * for compressible fluid dynamics within the reaction-diffusion framework. This
 * allows for coupled fluid-chemical simulations.
 */

#ifndef SUMMIT_EULER_EQUATIONS_H
#define SUMMIT_EULER_EQUATIONS_H

#include "../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material EulerEquations
                                         // 0 homogeneous reactions
#define EULER_EQUATIONS_NUMBER_INTERNAL_VARIABLES 3

namespace summit {

/**
 * @brief Euler equations for compressible fluid dynamics
 *
 * The EulerEquations class implements the Euler equations for compressible,
 * inviscid fluid flow within the reaction-diffusion framework. This enables
 * coupled fluid-chemical simulations for applications such as:
 *
 * - Combustion and detonation modeling
 * - Compressible reactive flows
 * - Gas dynamics with chemical reactions
 * - Shock-chemistry interactions
 * - High-speed aerodynamics with reactions
 *
 * Key features:
 * - Compressible fluid dynamics (density, momentum, energy)
 * - Inviscid flow assumption (no viscous effects)
 * - Coupling with chemical reaction systems
 * - Conservative form suitable for shock capturing
 * - Integration with Summit's DG framework
 *
 * The governing equations are:
 * - Continuity: ∂ρ/∂t + ∇·(ρv) = 0
 * - Momentum: ∂(ρv)/∂t + ∇·(ρv⊗v + pI) = 0
 * - Energy: ∂(ρE)/∂t + ∇·((ρE + p)v) = 0
 *
 * @note This model requires 3 internal variables for proper state tracking
 */
class EulerEquations : public ReactionDiffusionMaterial {
  public:

    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    EulerEquations(const std::string& name);

    /**
     * Destructor
     */
    virtual ~EulerEquations();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    EulerEquations(const EulerEquations&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    EulerEquations& operator=(const EulerEquations&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * EulerEquations constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf) const;


    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    // also change to conductivity or something more sensible 

  protected: 
    /**
     * bulk modulus
     */
    int _spatial_dimension;

    /**
     * bulk modulus
     */
    bool _implicit;

    /**
     * reference temperature
     */
    real _gamma;

    /**
     * Lax Friedrich Parameter
     */
    real _lax_parameter;

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
