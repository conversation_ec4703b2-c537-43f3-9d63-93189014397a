/**
 * @file nonlocal_damage.h
 * @brief Nonlocal damage material model
 * <AUTHOR> Development Team
 * @date 2011-2023
 */

#ifndef SUMMIT_NONLOCAL_DAMAGE_H
#define SUMMIT_NONLOCAL_DAMAGE_H

#include "../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material NonlocalDamage
                                         // 0 homogeneous reactions
#define NONLOCAL_DAMAGE_NUMBER_INTERNAL_VARIABLES 3

namespace summit {

/**
 * @brief Nonlocal damage material model
 *
 * Implements nonlocal damage mechanics using a reaction-diffusion approach.
 * The model regularizes damage evolution through a characteristic length scale:
 * ∂d/∂t = f(ε,d) + c∇²d
 *
 * Where:
 * - d: damage variable (0 = intact, 1 = fully damaged)
 * - ε: strain measure
 * - c: nonlocal parameter (related to characteristic length)
 * - f: damage evolution function
 *
 * Features:
 * - Mesh-objective damage evolution
 * - Avoids spurious localization
 * - Captures size effects in fracture
 * - Regularizes strain softening
 *
 * Applications:
 * - Concrete and rock mechanics
 * - Composite material failure
 * - Ductile fracture modeling
 * - Strain localization analysis
 *
 * @note Uses 3 internal variables for damage state tracking
 */
class NonlocalDamage : public ReactionDiffusionMaterial {
  public:
    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    NonlocalDamage(const std::string& name);

    /**
     * Destructor
     */
    virtual ~NonlocalDamage();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    NonlocalDamage(const NonlocalDamage&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    NonlocalDamage& operator=(const NonlocalDamage&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * NonlocalDamage constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration, const real* concentration0, real* q, real* dt, real* f, real* df, size_t ndf) const;

    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;
  
  protected: 

    /**
     * energy of cavitation
     */
    real _G;

    /**
     * length scale
     */
    real _Epsilon;

    /**
     * analogue to density, the "dynamic modulus"
     */
    real _Zeta;
    
    /**
     * damage threshold
     */
    real _offset;
    
  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
