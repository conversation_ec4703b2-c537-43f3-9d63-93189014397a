/**
 * @file lin_conv_diff.h
 * @brief Linear convection-diffusion material model
 * <AUTHOR> Development Team
 * @date 2011-2023
 */

#ifndef SUMMIT_LINEAR_CONV_DIFF_CONDUCTION_H
#define SUMMIT_LINEAR_CONV_DIFF_CONDUCTION_H

#include "../reaction_diffusion_material.h"

// Number of internal variables:
// the value is used to size the memory allocation for the material LinearConvectionDiffusion
                                         // 0 homogeneous reactions
#define LIN_CONV_DIFF_CONDUCTION_NUMBER_INTERNAL_VARIABLES 0

namespace summit {

/**
 * @brief Linear convection-diffusion material model
 *
 * Implements the linear convection-diffusion equation:
 * ∂c/∂t + u·∇c = D∇²c + S
 *
 * Where:
 * - c: concentration or scalar field
 * - u: velocity field (constant)
 * - D: diffusion coefficient
 * - S: source term
 *
 * Applications include:
 * - Scalar transport in fluid flows
 * - Heat conduction with convection
 * - Contaminant transport
 * - Chemical species transport
 *
 * @note Uses 0 internal variables for stateless transport
 */
class LinearConvectionDiffusion : public ReactionDiffusionMaterial {
  public:

    virtual int number_unknowns() const;
    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    LinearConvectionDiffusion(const std::string& name);

    /**
     * Destructor
     */
    virtual ~LinearConvectionDiffusion();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    LinearConvectionDiffusion(const LinearConvectionDiffusion&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    LinearConvectionDiffusion& operator=(const LinearConvectionDiffusion&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const;

    /**
     * LinearConvectionDiffusion constitutive update, all tensors are ROW MAJOR
     */
    void Constitutive(const real* concentration,
                              const real* u1,
                              const real* Dconcentration,
                              const real* Du1,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dtime,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const override;

    /**
     * set the source term
     */
    void Source(const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf) const;


    /**
     * set the source term
     */
    void ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internalVariables = 0, const int component = 0) const;

    /**
     * Compute the bulk modulus
     */
    real bulkModulus(real const* internal) const;
  
  protected: 

    /**
     * final mass fraction
     */
    real _rhoC;

    /**
     * x velocity
     */
    real _ux;

    /**
     * y velocity
     */
    real _uy;


    /**
     * Source term
     */
    real _S;

    /**
     * diffusion
     */
    real _D;

  private:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void _setInternalVariableMap();
};
}  // namespace summit

#endif
