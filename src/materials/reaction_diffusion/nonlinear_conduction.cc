#include <cmath>
#include <cstdio>
#include <cstdlib>
#include <iostream>

#include "nonlinear_conduction.h"
#include "../../mathlib/mathlib.h"
#include <pyre/journal.h>

// ****************************************************
//           INTERNAL VARIABLES q
// ****************************************************
//    Number of Internal variables:    0
// ****************************************************

summit::NonlinearConduction::NonlinearConduction(const std::string& name)
  : ReactionDiffusionMaterial(name, NONLINEAR_CONDUCTION_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    _setInternalVariableMap();
}

summit::NonlinearConduction::~NonlinearConduction() {}

#ifdef WITH_YAML_CPP
void summit::NonlinearConduction::Load(const YAML::Node &node) {
    pyre::journal::error_t error("summit.materials.reactiondiffusion.nonlinear-conduction");

    try {
        _rho  = node["density"].as<real>();
        _Cv   = node["heat-capacity"].as<real>();
        _cte  = node["cte"].as<real>();
        _bulk = node["bulk-modulus"].as<real>();
        _Tref = node["reference-temperature"].as<real>();

        std::vector<real> coeffs = node["k(T) coefficients"].as<std::vector<real>>();
        if (coeffs.size() != 4)
            error << "NonlinearConduction::Load: The number of coefficients must be 4"
                  << pyre::journal::endl(__HERE__);
        _A = coeffs[0]; _B = coeffs[1]; _C = coeffs[2]; _D = coeffs[3];
    } catch (...) {
        error << "NonlinearConduction::Load: A required key could not be found in the YAML tree."
              << pyre::journal::endl(__HERE__);
    }

    return; // Done!
}
#endif

void summit::NonlinearConduction::Load(const std::string& filename,
                                                   const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    _rho = values[0];

    _Cv = values[1];

    _A = values[2];

    _B = values[3];

    _C = values[4];

    _D = values[5];

    _cte = values[6];
    
    _bulk = values[7];

    _Tref = values[8];
    

    // all done
    return;
}

void summit::NonlinearConduction::Display()
{
    // Check material properties
    Message::Info("Nonlinear Heat Conduction Model:");
    Message::Info("\tDensity....... = %e", _rho);
    Message::Info("\tHeat Capacity....... = %e", _Cv);
    Message::Info("\tSeries Term A....... = %e", _A);
    Message::Info("\tSeries Term B....... = %e", _B);
    Message::Info("\tSeries Term C....... = %e", _C);
    Message::Info("\tSeries Term D....... = %e", _D);
    Message::Info("\tCoefficient of Thermal Expansion....... = %e", _cte);
    Message::Info("\tBulk Modulus....... = %e", _bulk);
    Message::Info("\tReference Temperature....... = %e", _Tref);
    // end of method
    return;
}

summit::real summit::NonlinearConduction::diffusivity(const real* Fn,
                                                                  const real* q,
                                                                  const int ndm,
                                                                  const int component) const
{
    // diffusivity
    //return _thermal_conductivity / (_rho * _heat_capacity);
    //this is important for stabilizing the DG elements (I think)
    return _D / (_rho * _Cv);
}

void summit::NonlinearConduction::Constitutive(const real* concentration0,
                                                           const real* concentration,
                                                           const real* Dconcentration0,
                                                           const real* Dconcentration,
                                                           real* P,
                                                           real* q,
                                                           real* tangent,
                                                           real* dPdu,
                                                           real dtime,
                                                           const int ndf, // number of components in concentration
                                                           const int ndm, // spatial dimension
                                                           bool compute_tangents,
                                                           bool artVisc_interface_activate) const
{
    real K =(_A * std::pow(concentration[0],3) + _B * std::pow(concentration[0],2) + _C * std::pow(concentration[0],1) + _D);
    real dK = (_A * 3.0 * std::pow(concentration[0],2) + _B * 2.0 * std::pow(concentration[0],1) + _C);
    //std::cout << "K is: " << K <<std::endl;
    for (int component = 0; component < this->number_unknowns(); component++){
        for (int dimension = 0; dimension < ndm; dimension++){
            P[dimension + component * ndm] = K * Dconcentration[dimension + component * ndm];
            if(compute_tangents){
                tangent[(dimension + component * ndm) * (ndm * ndf) + dimension + component * ndm] = K;
                dPdu[(dimension + component * ndm) * ndf + component] = dK * Dconcentration[dimension + component * ndm];
            }
        }
    }
    // this sets the q[0]erature in the internals for transfer to the solid!
    q[0] = concentration0[0];
    q[5] = (concentration[0] - concentration0[0]) / dtime;
    return;
}


summit::real summit::NonlinearConduction::capacity(real const* internal, const int component) const
{
    return 0.0;//_Cv * _rho;    
}

void summit::NonlinearConduction::_setInternalVariableMap()
{   
    // all done
    SetLocationInInternalTable("Temperature", 0, 1);
    SetLocationInInternalTable("Jacobian", 1, 1);
    SetLocationInInternalTable("Jacobian Old", 2, 1);
    SetLocationInInternalTable("plastic work", 3, 1);
    SetLocationInInternalTable("plastic work old", 4, 1);
    SetLocationInInternalTable("Rate", 5, 1);
    return;
}

void summit::NonlinearConduction::Source(const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf) const
{   
    // nonlinear heat capacity
    f[0] = - (_rho*_Cv - concentration[0]*_cte*_cte*_bulk) / dt[0] * (concentration[0] - concentration0[0]);
    df[0] = - (_rho*_Cv - concentration[0]*_cte*_cte*_bulk) / dt[0];
    df[0] +=   _cte*_cte*_bulk  * (concentration[0] - concentration0[0])/ dt[0];
    // add plastic heating rate
    f[0] += q[3] - q[4];
    // add thermoelastic effect
    f[0] += (concentration[0] + _Tref) * (-_bulk * _cte) * (q[1] - q[2]) / dt[0];
    df[0] += (-_bulk * _cte) * (q[1] - q[2]) / dt[0];  
    // update Jacobians and plastic work
    q[2] = q[1];
    q[4] = q[3];
    return;
}

void summit::NonlinearConduction::ConvectiveFlux(const real* concentration0, const real* concentration, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const
{
    return;
}

int summit::NonlinearConduction::number_unknowns() const
{
    return 1;
}

real summit::NonlinearConduction::bulkModulus(real const* internal) const
{
    return _D;
}
// end of file
