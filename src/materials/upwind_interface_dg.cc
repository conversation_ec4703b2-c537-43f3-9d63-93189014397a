// -*- C++ -*-
//
// rrgroup
// summit development team
// massachusetts institute of technology
// (c) 2012-2013 all rights reserved
//

#include "upwind_interface_dg.h"
#include <iostream>
#include <cstdlib>
#include <cmath>
#include <pyre/journal.h>
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif
#include "../utils/util.h"
#include "../mathlib/mathmat.h"
#include "../mathlib/mathlib.h"
#include "../mathlib/mathvec.h"
#include "../io/summit_message.h"
#include "../restart/checkpoint.h"
#define NTIMESFRAC 2

summit::UpwindInterfaceDG::UpwindInterfaceDG(Checkpoint* checkpoint, const char* name)
  : InterfaceDG(checkpoint, name)
{

    DataSet* ds = checkpoint->OpenDataSet("numberOfComponents");
    ds->read(&_numberComponents);

    this->SetInternalVariableMap();

    return;
}


summit::UpwindInterfaceDG::UpwindInterfaceDG() : InterfaceDG("UpwindInterfaceDG", UPWIND_INTERFACE_DG_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    this->SetInternalVariableMap();
}

summit::UpwindInterfaceDG::UpwindInterfaceDG(const std::string& name)
  : InterfaceDG(name, UPWIND_INTERFACE_DG_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    this->SetInternalVariableMap();
}

summit::UpwindInterfaceDG::UpwindInterfaceDG(const std::string& name, const int nInt) : InterfaceDG(name, nInt) {}

summit::UpwindInterfaceDG::UpwindInterfaceDG(const std::string& name,
                               const int leftMatLabel,
                               int rightMatLabel,
                               const real beta,
                               const int numberComponents,
                               const int nInt)
  : InterfaceDG(name, leftMatLabel, rightMatLabel, beta, nInt),
    _numberComponents(numberComponents)
{
    // fill the map
    this->SetInternalVariableMap();
}

summit::UpwindInterfaceDG::~UpwindInterfaceDG() {}

#ifdef WITH_YAML_CPP
/* Example YAML material definition:
    - name: some-name
        adjacent: [other-name, other-name]
        materialID: 247 # UpwindInterfaceDG
        components:
        - {beta: 0.5, interface-type: Dirichlet, interface-value: 0.0}
        - {beta: 0.5, interface-type: Neumann, interface-value: 1.0}
*/

void summit::UpwindInterfaceDG::Load(const YAML::Node &node) {
    pyre::journal::error_t error("summit.materials.interfaceDG");

    // Load from YAML file
    std::vector<YAML::Node> components;
    try {
        _leftMatLabel  = node["adjacent"][0].as<int>();
        _rightMatLabel = node["adjacent"][1].as<int>();
        components = node["components"].as<std::vector<YAML::Node>>();
    } catch (...) {
        error << "UpwindInterfaceDG::Load: A required key could not be found in the YAML tree."
              << pyre::journal::endl(__HERE__);
    }

    // Check number of components and resize storage appropriately
    _numberComponents = components.size();
    if (_numberComponents == 0)
        error << "UpwindInterfaceDG::Load: At least one component expected" << pyre::journal::endl(__HERE__);
    _betas.resize(_numberComponents);
    _interface_types.resize(_numberComponents);
    _interface_values.resize(_numberComponents);

    // Read component-wise information
    try {
        for (int i = 0; i < _numberComponents; i++) {
            YAML::Node component = components[i];
            _betas[i] = component["beta"].as<real>();
            _interface_values[i] = component["interface-value"].as<real>();
            std::string type = component["interface-type"].as<std::string>();
            if (type == "neumann" || type == "Neumann") 
                _interface_types[i] = 0;
            else if (type == "dirichlet" || type == "Dirichlet") 
                _interface_types[i] = 1;
            else
                error << "UpwindInterfaceDG::Load: Unknown interface type " << type << pyre::journal::endl(__HERE__);
        }
    } catch (...) {
        error << "UpwindInterfaceDG::Load: Error reading component beta, interface_value, or interface_type keys from YAML tree."
              << pyre::journal::endl(__HERE__);
    }
    _beta = _betas[0];

    return; // Done!
}
#endif

void summit::UpwindInterfaceDG::Load(const std::string& filename, const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    /** properties in input file
     *
     *  101
     *  left material label
     *  right material label
     *  beta
     *  sigma_c
     *  Gc
     *  gamma
     *  eta
     *  typeCriterionFractureInitiation
     *
     */

    // if (values.size() != 9) {
    //     // throw message and die
    //     std::cout << "values.size(): " << values.size()<< std::endl;
    //     throw std::runtime_error(
    //       "UpwindInterfaceDG::Load: I expect you to give me 9 input parameters in the material "
    //       "description file");
    // }
    _leftMatLabel = (int)values[0];
    _rightMatLabel = (int)values[1];
    _numberComponents = values[2];

    _betas.resize(_numberComponents);
    _interface_types.resize(_numberComponents);
    _interface_values.resize(_numberComponents);
    // fill g attribute
    for (int i = 0; i < _numberComponents; i++) {
        _betas[i] = values[2 + 1 + i*3];
        _interface_types[i] = values[2 + 2 + i*3];
        _interface_values[i] = values[2 + 3 + i*3];
    }
    _beta = _betas[0];
    //    _typeCriterionFractureInitiation = (int)values[8];
    // end of method
    return;
}

void summit::UpwindInterfaceDG::Display()
{
    // display
    Message::Info(" Material Characteristics : (UpwindInterfaceDG)");
    Message::Info("\t Label of left material ............................................... =\t%d",
                  _leftMatLabel);
    Message::Info("\t Label of right material .............................................. =\t%d",
                  _rightMatLabel);
    Message::Info("\t Stability parameter of DG method (beta) .............................. =\t%e",
                  _beta);

    Message::Info("\t Betas .......................");
    for (size_t i = 0; i < _betas.size(); i++) {
        Message::Info("\t beta %d : %e", i, _betas[i]);
    }
    Message::Info("\t Interface Types .......................");
    for (size_t i = 0; i < _interface_types.size(); i++) {
        Message::Info("\t Interface %d : %d", i, _interface_types[i]);
    }
    Message::Info("\t Interface Values .......................");
    for (size_t i = 0; i < _interface_values.size(); i++) {
        Message::Info("\t Interface %d : %d", i, _interface_values[i]);
    }
}

void summit::UpwindInterfaceDG::getBetasForDGStability(real* myBetas) const
{   
    // Not all components of the DG problem need to get the same beta
    for(int i =0;i<_numberComponents;i++){
        myBetas[i] = _betas[i];
    }
    return;
}

int summit::UpwindInterfaceDG::materialBufferSizeForComm(const int dim) const
{
    return 0;
}

void summit::UpwindInterfaceDG::getInterfaceDamage(const real* internalVariables, real* myDamage) const
{
    // One could `damage' only some components of the thermo-chemical DG formulation.
    // Here all components of the problem get the same value.
    // Also the damage does not need to be a linear function of the CZM damage, but it is here.
    for(int i =0;i<_numberComponents;i++){
        myDamage[i] = internalVariables[0];
    }
    return;
}

void summit::UpwindInterfaceDG::getInterfaceTypes(const real* internalVariables, int* myTypes) const
{
    // Is this a dirichlet, or a Neumann interface after failure?
    // 0 neumann
    // 1 dirichlet
    for(int i =0;i<_numberComponents;i++){
        myTypes[i] = _interface_types[i];
    }
    return;
}

void summit::UpwindInterfaceDG::getInterfaceValues(const real* internalVariables, real* myValues) const
{
    // What is the value of the Dirichlet or Neumann condition after failure
    for(int i =0;i<_numberComponents;i++){
        myValues[i] = _interface_values[i];
    }
    return;
}

void summit::UpwindInterfaceDG::setInterfaceDamage(real* internalVariables, real interfaceDamage) const
{
    internalVariables[0] = interfaceDamage;
    return;
}

summit::real summit::UpwindInterfaceDG::getConductance(const real* internalVariables) const
{
    return 0.0;
}

void summit::UpwindInterfaceDG::Update(real* internalVariables) const
{
    return;
}

void summit::UpwindInterfaceDG::SetInternalVariableMap()
{
    this->SetLocationInInternalTable("interface damage", 0, 1);
    return;
}

void summit::UpwindInterfaceDG::packBufferForCommunication(const int ndm,
                                                    real numberTimesFailureCriterionIsSatisfied,
                                                    real* tractionLeft,
                                                    real* normalDeformedLeft,
                                                    real* interfaceMaterial_bufferForComm) const
{
    // std::cout << "PackBufferForCommunication is normally useless for UpwindInterfaceDG" << std::endl;
    // end of method
    return;
}

void summit::UpwindInterfaceDG::UnpackBufferForCommunication(
  const int ndm,
  int* numberTimesFailureCriterionIsSatisfied,
  real* tractionLeft,
  real* normalDeformedLeft,
  const real* interfaceMaterial_bufferForComm) const
{
    // std::cout << "UnpackBufferForCommunication is normally useless for UpwindInterfaceDG" << std::endl;
    // end of method
    return;
}

void summit::UpwindInterfaceDG::WriteForRestart(Checkpoint* checkpoint,
                                         const char* name,
                                         const char* tag) const
{
    if (tag == nullptr) {
        summit::InterfaceDG::WriteForRestart(checkpoint, name, "UpwindInterfaceDG");
    }
    else {
        summit::InterfaceDG::WriteForRestart(checkpoint, name, tag);
    }

    std::vector<int> dims(1);

    // Write data sets
    dims[0] = 1;
    checkpoint->WriteDataSet("numberOfComponents", dims, SUMMIT_INT,
                             &_numberComponents);

    return;
}
// Register SMA as option for class to instantiate
REGISTER(Material, UpwindInterfaceDG);
// end of file
