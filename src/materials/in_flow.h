/**
 * @file in_flow.h
 * @brief Inflow boundary condition material for fluid dynamics simulations
 * <AUTHOR> Development Team
 * @date 2012-2023
 *
 * This file contains the InFlow class which implements inflow boundary conditions
 * for fluid dynamics simulations using the Roe flux formulation. It handles
 * prescribed inflow conditions at domain boundaries.
 */

#ifndef SUMMIT_IN_FLOW_H
#define SUMMIT_IN_FLOW_H

#include "roe_flux.h"

namespace summit {

class Checkpoint;

/**
 * @brief Inflow boundary condition material for fluid dynamics
 *
 * The InFlow class implements inflow boundary conditions for fluid dynamics
 * simulations. It inherits from RoeFlux and provides specialized functionality
 * for handling prescribed inflow conditions at domain boundaries.
 *
 * Key features:
 * - Implements Roe flux formulation for inflow boundaries
 * - Supports prescribed velocity and pressure conditions
 * - Handles compressible and incompressible flow regimes
 * - Integrates with the Summit material library system
 *
 * @note This class is specifically designed for boundary conditions, not bulk materials
 */
class InFlow : public RoeFlux {
  public:
    /**
     * Constructor (default)
     */
    InFlow();

    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    InFlow(const std::string& name);

    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     * @param[in] nInt number of internal variables
     */
    InFlow(const std::string& name, const int nInt);

    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     * @param[in] leftMatLabel Left material Label
     * @param[in] rightMatLabel Right material Label
     * @param[in] beta stability parameter
     * @param[in] numberComponents Type of criterion that must be satisfied at
     *            quadrature points to initiate the fracture
     */
    InFlow(const std::string& name,
           const int leftMatLabel,
           const int rightMatLabel,
           const real beta,
           const int numberComponents,
           const int nInt = 0);

    /**
     * Constructor
     * @param[in] checkpoint object associated with restart
     * @param[in] name the name of the group within the checkpoint object
     */
    InFlow(Checkpoint* checkpoint, const char* name);

    /**
     * Destructor
     */
    virtual ~InFlow();

    /**
     * Method to display the material parameters to the output chanel
     */
    void Display() override;

    virtual MaterialType GetMaterialType() const { return MATERIAL_InFlow; }

    /**
     * Method to know if the interface law is for boundary conditions
     * @return true if this model is for external surfaces
     */
    bool IsBoundary() const { return true; }

    /**
     * @brief Base implementation of inflow condition for gas dynamics
     *
     * The base implementation provides the capability to do surface integrals
     * this function cannot depend on the gradient
     * TODO improve documentation //(up,qp,np,ib,ui,p,param,time)
     */
    void inviscidBoundaryFlux(const int ndm, const int ndf, real dt, real measInterface, real* internal_new, const real* concentration_L, const real* concentration_L0, const real* Normal, const real* Coord, real* fDotN, real* dFDotNdC) const;

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    InFlow(const InFlow&);

    static Register<Material, InFlow> reg;
    
    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    InFlow& operator=(const InFlow&);

    /**
     * @brief this computes the Flux doted with the normal
     * It should really borrow the bulk material's implementation for consistency
     * This could be improved in afuture rewrite
     *
     */
    void flux_dotted_n(const real* concentration, const real* Normal, real* FdotN) const;

    real _Minf = 0.5;
    real _Pinf = 1.0/(this->getGamma()*_Minf*_Minf);
    real _MassInfinity = 1.0;
    real _xMomentumInfinity = 1.0;
    real _yMomentumInfinity = 0.0;
    real _EnergyInfinity = 0.5+_Pinf/(this->getGamma()-1.0);

};

}  // namespace summit

#endif // SUMMIT_IN_FLOW_H

// end of file