// -*- C++ -*-
//
// rrgroup
// summit development team
// massachusetts institute of technology
// (c) 2012-2013 all rights reserved
//


#ifndef SUMMIT_INTERFACE_DG_H_
#define SUMMIT_INTERFACE_DG_H_

#include "material.h"
#include "mechanical_material.h"
#include "../summit_enum.h"
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif
namespace summit {

class Checkpoint;
/**
 * Class for interface material for DG
 */
class InterfaceDG : public Material {
  public:
    // To allow the use of interface law with more than 1 stability parameter (usefull for shell)
    enum PARAMETER {
        DEFAULT = 0,
        MAX = 1,
        MIN = 2,
        TIME_STEP = 3,
        SHL_BENDING = 4,
        SHL_MEMBRANE = 5,
        SHL_SHEARING = 6,
    };

    /**
     * Constructor (default)
     */
    InterfaceDG(const int leftMatLabel = 1,
                const int rightMatLabel = 1,
                real beta = 10.,
                const int nInt = 1);

    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    InterfaceDG(const std::string& name);

    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     * @param[in] nInt number of internal variables
     */
    InterfaceDG(const std::string& name, const int nInt);

    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     * @param[in] leftMatLabel Left material Label
     * @param[in] rightMatLabel Right material Label
     * @param[in] beta stability parameter
     * @param[in] nInt number of internal variables
     */
    InterfaceDG(const std::string& name,
                const int leftMatLabel,
                const int rightMatLabel,
                const real beta,
                const int nInt);

    virtual MaterialType GetMaterialType() const { return MATERIAL_InterfaceDG; }

    /**
     * Constructor to build the object from a restart binary file.
     * @param[in] checkpoint the manager of the binary file
     * @param[in] name the name of the group the object will be read from
     */
    InterfaceDG(Checkpoint* checkpoint, const char* name);

    /**
     * Destructor
     */
    virtual ~InterfaceDG();

    /**
     * Method to display the material parameters on the output screen
     */
    virtual void Display();

    /**
     * Method to set internal variables in case of initial fracture
     * @param[in] an array (vector) that contains some values for the initialization of the specific
     * law
     * @param[in,out] an array with the internal variables of a quadrature point
     */
    virtual void InitCrack(const std::vector<real>& init_values, real* q) const;

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    InterfaceDG(const InterfaceDG&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    InterfaceDG& operator=(const InterfaceDG&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    #ifdef WITH_YAML_CPP
    /**
     * Method to load material properties from a YAML node
     * @param[in] node the YAML node for this specific material
     */
    virtual void Load(const YAML::Node &node);
    #endif

    /**
     * Method to compute the critical wavespeed
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real Celerity(const real* Fn, const real* q, const int ndm) const;

    /**
     * Constitutive law, all tensors are passed in ROW MAJOR
     * The input/output parameters depend on the specific cohesive law
     */
    virtual void Constitutive(const real* displacementJump,
                              const real* normalDeformedLeft,
                              const real* tractionLeft,
                              const real* DG_flux,
                              real* coefficients,
                              real* internalVariables,
                              real* effectiveCohesiveTraction,
                              real* tangent,
                              real contactCharacteristicLength,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents,
                              bool initiateFracture = false,
                              bool artVisc_interface_activate = true) const;

    /**
     * @brief Method to update the history variables of the cohesive law (if any)
     *  This method is called by the region after computing the constitutive update
     *   for a converged configuration
     *
     * @param internalVariables the material internal variables
     */
    virtual void Update(real* internalVariables) const;

    /**
     * Material tangent
     * @param[in] ujump the displacement jump vector
     * @param[in] qold the internal variables array of the Gauss point at previous iteration
     * @param[in] normal the current unit normal director
     * @param[in,out] dFcohdu the tangent of the cohesive forces
     * @param[in] ndf the number of degrees of freedom per node
     * @param[in] dim the spatial dimension
     */
    virtual void TangentNumeric(const real* ujump,
                                const real* qold,
                                const real* normal,
                                real* dFcohdu,
                                const int ndf,
                                const int dim) const;

    /**
     * Evaluation of failure criterion, all tensors are passed in ROW MAJOR
     * @param[in] P_L_new left first Piola-Kirchoff stress tensor
     * @param[in] F_L_new strains energetically conjugated to P_L_new
     * @param[in] P_R_new right first Piola-Kirchoff stress tensor
     * @param[in] F_R_new strains energetically conjugated to P_R_new
     * @param[in] internal internal variables
     * @param[in] normalL normal to the left element
     * @param[in] side should be either INTER, LEFT or RIGHT to consider the left normal correctly
     * @param[in] ndm spatial dimension of the computational domain
     * @param[out] interfaceMaterial_bufferForComm bufferForComm computed from the cohesive law
     */
    virtual void EvaluateFailureCriterion(const real* P_L_new,
                                          const real* F_L_new,
                                          const real* P_R_new,
                                          const real* F_R_new,
                                          const real* internal,
                                          const real* normalL,
                                          summit::SIDE side,
                                          const int ndm,
                                          real* interfaceMaterial_bufferForComm) const;

    /**
     * Unpack the information of the buffer for communication in parallel
     * @param[in] ndm spatial dimension of the computational domain
     * @param[out] numberTimesFailureCriterionIsSatisfied number of times that failure criterion is
     * satisfied
     *               The three possible values are:
     *                 - 0:  The criterion is not satisfied neither by the left element nor the
     * right element
     *                 - 1:  The criterion is satisfied either by the left element or the right
     * element
     *                 - 2:  The criterion is satisfied by left and right elements
     * @param[out] tractionLeft traction vector for the left element
     * @param[out] normalDeformedLeft normal vector to the left element in the deformed
     * configuration
     * @param[in] interfaceMaterial_bufferForComm buffer for communication
     */
    virtual void UnpackBufferForCommunication(const int ndm,
                                              int* numberTimesFailureCriterionIsSatisfied,
                                              real* tractionLeft,
                                              real* normalDeformedLeft,
                                              const real* interfaceMaterial_bufferForComm) const;

    /**
     * Decide the fracture initiation for a set of nquad quadrature (sample or evaluation) points
     *    The criterion to decide the fracture initiation depends on the interface material
     * @param[in] nquad dimension of vectors numberTimesFailureCriterionIsSatisfied and
     * initiateFracture
     * @param[in] numberTimesFailureCriterionIsSatisfied int vector of dimension nquad
     *              Each component indicates the number of times that failure criterion is satisfied
     *              for a specific quadrature (or sample) point quad.
     *              The three possible values are:
     *                 - 0:  The criterion is not satisfied neither by the left element nor the
     * right element
     *                 - 1:  The criterion is satisfied either by the left element or the right
     * element
     *                 - 2:  The criterion is satisfied by left and right elements
     * @param[out] initiateFracture int vector of dimension nquad
     *              Each component indicates if the fracture has to be initiated or not
     *              The two possible values are:
     *                 - true:  The fracture must be initiated
     *                 - false: The fracture does not have to be initiated
     */
    virtual void DecideFractureInitiation(const int nquad,
                                          int* numberTimesFailureCriterionIsSatisfied,
                                          bool* initiateFracture) const;

    /**
     * Decide if contact after fracture initiation is applied or not
     * @param[in] nquad dimension of vector contactActivationStatusPerQuadraturePoint
     * @param[in] contactActivationStatusPerQuadraturePoint is an int vector of dimension nquad
     *            status of the contact activation in each quadrature point
     *            0: no need of contact, 1: contact should be activated
     *            The decision is taken based on the information of this vector
     * @param[out] bool true:  contact is activated
     *                  false: contact is not activated
     */
    virtual bool DecideContactActivation(const int nquad,
                                         int* contactActivationStatusPerQuadraturePoint) const;

    /**
     * @return the stability parameter
     */
    virtual real StabilityParameter(PARAMETER param = DEFAULT) const;

    /**
     * To return the size of the buffer for a specific cohesive material
     *    The buffer is used to store the information needed to evaluate the fracture criterion
     * @param dim is the spatial dimension
     * @return size of the buffer
     */
    virtual int materialBufferSizeForComm(const int dim) const;

    /**
     * Identify the interface law corresponding to 2 bulk laws
     * @param[in] the label of the left material 0-based index
     * @param[in] the label of the right material 0-based index
     * @return true if match false otherwise
     */
    bool interKey(const int leftMatLabel, const int rightMatLabel) const;

    /**
     * Method to know if the interface law allows for crack propagation
     * @return true if fracture is possible
     */
    virtual bool IsFracture() const { return false; }

    /**
     * Method to know if the interface law allows for crack propagation
     * @return true if fracture is possible
     */
    virtual bool IsVariational() const { return false; }

    virtual void contactProjectionAndDamageEvolution(const real* averg_hs_C,
                                             const real* dispJump,
                                             const real* dgTraction,
                                             const real* cohesive_traction,
                                             const real* coefficients,
                                             const real* normal,
                                             const int ndm,
                                             const real myJac,
                                             real* internalVariables,
                                             real* projectedJump,
                                             real* projectedTraction,
                                             bool* undergoingDamage) const;

    /**
     * @return the fracture characteristic length
     */
    virtual real CharacteristicLength(const real* internalVariables) const;

    /**
     * @return the minimum interpenetration for contact
     */
    virtual const real getMinimumInterpenetrationForContactActivation(
      const real* internalVariables) const;

    /**
     * Get the vector with the initial displacement jump from the internal variables
     * @return the initial displacement jump
     */
    virtual const real* const getInitialDisplacementJump(const real* internalVariables) const;

    virtual void WriteForRestart(Checkpoint* checkpoint,
                                 const char* name,
                                 const char* tag = nullptr) const;

  protected:
    /**
     * Left and Right material Label
     * from which this law is the interface
     * 1-based indices!
     */
    int _leftMatLabel;
    int _rightMatLabel;
    /**
     * Stability parameter of DG method
     */
    real _beta;

  private:
    static Register<Material, InterfaceDG> reg;
};
}  // namespace summit

#endif  // SUMMIT_INTERFACE_DG_H_
