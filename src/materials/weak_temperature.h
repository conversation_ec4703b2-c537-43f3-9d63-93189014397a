/**
 * @file weak_temperature.h
 * @brief Weak temperature boundary condition material for thermal simulations
 * <AUTHOR> Development Team
 * @date 2012-2023
 *
 * This file contains the WeakTemperature class which implements weak temperature
 * boundary conditions for thermal and coupled thermal-mechanical simulations.
 * This material provides a flexible framework for enforcing temperature constraints
 * through penalty methods and weak formulations.
 */

#ifndef SUMMIT_WEAK_TEMPERATURE_H
#define SUMMIT_WEAK_TEMPERATURE_H

#include "upwind_interface_dg.h"

namespace summit {

class Checkpoint;

/**
 * @brief Weak temperature boundary condition material for thermal simulations
 *
 * The WeakTemperature class implements weak temperature boundary conditions for
 * thermal and coupled thermal-mechanical simulations using discontinuous Galerkin
 * methods. Unlike strong (essential) boundary conditions that directly modify
 * the solution, weak boundary conditions are enforced through penalty methods
 * and interface flux terms in the weak formulation.
 *
 * ## Theoretical Foundation
 *
 * ### Strong vs. Weak Boundary Conditions
 *
 * #### Strong (Essential) Boundary Conditions
 * - **Direct enforcement**: T = T_prescribed at boundary nodes
 * - **Modification of system**: Eliminates degrees of freedom
 * - **Exact satisfaction**: Boundary condition satisfied exactly
 * - **Implementation complexity**: Requires special handling in assembly
 *
 * #### Weak (Natural) Boundary Conditions
 * - **Penalty enforcement**: T ≈ T_prescribed through penalty terms
 * - **Preservation of system**: All degrees of freedom retained
 * - **Approximate satisfaction**: Boundary condition satisfied in weak sense
 * - **Implementation simplicity**: Handled through interface flux terms
 *
 * ### Mathematical Formulation
 *
 * For the heat conduction equation:
 * ρcp ∂T/∂t - ∇·(k∇T) = Q̇
 *
 * The weak form with temperature boundary conditions becomes:
 * ∫_Ω ρcp ∂T/∂t v dΩ + ∫_Ω k∇T·∇v dΩ + ∫_Γ_T α(T - T_prescribed)v dΓ = ∫_Ω Q̇v dΩ
 *
 * Where:
 * - **α**: Penalty parameter [W/(m²·K)]
 * - **T_prescribed**: Prescribed temperature [K]
 * - **Γ_T**: Temperature boundary
 * - **v**: Test function
 *
 * ## Penalty Method Implementation
 *
 * ### Penalty Parameter Selection
 * The penalty parameter α controls the enforcement strength:
 * - **Too small**: Boundary condition poorly enforced
 * - **Too large**: Numerical conditioning problems
 * - **Optimal range**: α ≈ k/h where k is thermal conductivity, h is mesh size
 *
 * ### Penalty Term Contribution
 * The penalty term adds to the system:
 * - **Stiffness matrix**: K_penalty = ∫_Γ_T α v u dΓ
 * - **Load vector**: F_penalty = ∫_Γ_T α T_prescribed v dΓ
 *
 * ### Consistency and Stability
 * For optimal performance, the penalty method should be:
 * - **Consistent**: Exact solution satisfies the weak form
 * - **Stable**: Penalty parameter provides sufficient constraint
 * - **Well-conditioned**: Penalty parameter not too large
 *
 * ## Applications
 *
 * ### Thermal Boundary Conditions
 *
 * #### Prescribed Temperature (Dirichlet)
 * - **Isothermal surfaces**: T = T_constant
 * - **Time-dependent heating**: T = f(t)
 * - **Spatially varying temperature**: T = f(x,y,z)
 * - **Coupled temperature**: T = f(other_fields)
 *
 * #### Temperature Constraints
 * - **Maximum temperature limits**: T ≤ T_max
 * - **Minimum temperature limits**: T ≥ T_min
 * - **Temperature ranges**: T_min ≤ T ≤ T_max
 * - **Phase change constraints**: T = T_melt during melting
 *
 * ### Multi-Physics Coupling
 *
 * #### Thermal-Mechanical Coupling
 * - **Thermal expansion**: Temperature affects stress/strain
 * - **Mechanical heating**: Plastic work generates heat
 * - **Contact heating**: Friction generates temperature rise
 * - **Coupled boundary conditions**: Temperature and displacement constraints
 *
 * #### Thermal-Chemical Coupling
 * - **Reaction temperature**: Chemical reactions affect temperature
 * - **Temperature-dependent kinetics**: Reaction rates depend on temperature
 * - **Catalytic surfaces**: Surface temperature controls reaction rates
 * - **Phase change chemistry**: Temperature controls phase transitions
 *
 * #### Thermal-Fluid Coupling
 * - **Conjugate heat transfer**: Fluid-solid interface temperature
 * - **Convective boundaries**: Temperature coupling with fluid flow
 * - **Boiling/condensation**: Phase change at interfaces
 * - **Thermal buoyancy**: Temperature drives fluid motion
 *
 * ## Numerical Implementation
 *
 * ### Discontinuous Galerkin Integration
 * The weak temperature condition is implemented through interface terms:
 * - **Penalty flux**: F_penalty = α(T_interior - T_prescribed)
 * - **Consistency flux**: F_consistency = k∇T·n (for symmetric methods)
 * - **Stabilization flux**: F_stabilization = β h^(-1)(T_jump) (for stability)
 *
 * ### Time Integration
 * Supports various time integration schemes:
 * - **Explicit methods**: Penalty term treated explicitly
 * - **Implicit methods**: Penalty term contributes to Jacobian
 * - **IMEX schemes**: Penalty term can be implicit or explicit
 *
 * ### Nonlinear Solver Integration
 * For temperature-dependent properties:
 * - **Newton-Raphson**: Penalty term contributes to Jacobian
 * - **Picard iteration**: Penalty parameter may be updated
 * - **Continuation methods**: Penalty parameter can be ramped
 *
 * ## Advantages of Weak Temperature Conditions
 *
 * ### Flexibility
 * - **Easy implementation**: No special handling of constrained nodes
 * - **Mesh independence**: Works with any mesh topology
 * - **Multi-point constraints**: Can handle complex constraint patterns
 * - **Adaptive refinement**: No special treatment for refined boundaries
 *
 * ### Robustness
 * - **Numerical stability**: Penalty parameter provides stabilization
 * - **Conditioning control**: Penalty parameter can be tuned
 * - **Convergence properties**: Consistent and stable formulation
 * - **Error estimates**: A posteriori error estimation available
 *
 * ### Coupling Capabilities
 * - **Multi-physics**: Natural framework for coupled problems
 * - **Interface problems**: Handles material interfaces naturally
 * - **Contact problems**: Suitable for contact temperature constraints
 * - **Optimization**: Compatible with design optimization frameworks
 *
 * ## Disadvantages and Considerations
 *
 * ### Approximate Enforcement
 * - **Penalty error**: Boundary condition satisfied approximately
 * - **Parameter dependence**: Solution depends on penalty parameter choice
 * - **Convergence rate**: May reduce convergence rate for some problems
 * - **Consistency requirements**: Requires careful implementation for consistency
 *
 * ### Computational Considerations
 * - **Additional terms**: Penalty terms increase computational cost
 * - **Conditioning**: Large penalty parameters may worsen conditioning
 * - **Memory requirements**: Additional storage for penalty terms
 * - **Assembly complexity**: More complex assembly process
 *
 * ## Parameter Guidelines
 *
 * ### Penalty Parameter Selection
 * - **Physical scaling**: α ≈ k/h (thermal conductivity / mesh size)
 * - **Dimensionless form**: α_hat = α h / k ≈ 1
 * - **Problem dependent**: May need tuning for specific applications
 * - **Adaptive selection**: Can be computed automatically
 *
 * ### Stability Requirements
 * - **Lower bound**: α > α_min for stability
 * - **Upper bound**: α < α_max for conditioning
 * - **Optimal range**: α_min < α < α_max
 * - **Mesh dependence**: α should scale with mesh refinement
 *
 * @note This class inherits from UpwindInterfaceDG for DG interface handling
 * @note Supports both 2D and 3D thermal simulations
 * @note Can be combined with other boundary condition materials
 * @note Includes provisions for restart capability and checkpointing
 *
 * @warning Penalty parameter selection is critical for performance
 * @warning Large penalty parameters may cause conditioning problems
 * @warning Boundary condition is satisfied approximately, not exactly
 * @warning Requires careful validation for critical applications
 *
 * @see UpwindInterfaceDG for base interface functionality
 * @see SurfaceHeating for alternative thermal boundary conditions
 * @see Wall for adiabatic boundary conditions
 */
class WeakTemperature : public UpwindInterfaceDG {
  public:
    /**
     * Constructor (default)
     */
    WeakTemperature();

    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     */
    WeakTemperature(const std::string& name);

    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     * @param[in] nInt number of internal variables
     */
    WeakTemperature(const std::string& name, const int nInt);

    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     * @param[in] leftMatLabel Left material Label
     * @param[in] rightMatLabel Right material Label
     * @param[in] beta stability parameter
     * @param[in] numberComponents Type of criterion that must be satisfied at
     *            quadrature points to initiate the fracture
     */
    WeakTemperature(const std::string& name,
           const int leftMatLabel,
           const int rightMatLabel,
           const real beta,
           const int numberComponents,
           const int nInt = 0);

    /**
     * Constructor
     * @param[in] checkpoint object associated with restart
     * @param[in] name the name of the group within the checkpoint object
     */
    WeakTemperature(Checkpoint* checkpoint, const char* name);

    /**
     * Destructor
     */
    virtual ~WeakTemperature();

    /**
     * Method to display the material parameters to the output chanel
     */
    void Display() override;

    virtual MaterialType GetMaterialType() const { return MATERIAL_WeakTemperature; }

    /**
     * Method to know if the interface law is for boundary conditions
     * @return true if this model is for external surfaces
     */
    bool IsBoundary() const { return true; }
    
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    #ifdef WITH_YAML_CPP
    /**
     * Method to load material properties from a YAML node
     * @param[in] node the YAML node for this specific material
     */
    virtual void Load(const YAML::Node &node);
    #endif

        /**
     * @brief returns the DG parameter for a single component of a multi-component problem
     * 
     * @param component which component of the problem are we talking about 
     * @return real 
     */
    void getInterfaceDamage(const real* internalVariables, real* myDamage) const;

    /**
     * @brief returns the DG parameter for a single component of a multi-component problem
     * 
     * @param component which component of the problem are we talking about 
     * @return real 
     */
    void getInterfaceTypes(const real* internalVariables, int* myTypes) const;

    /**
     * @brief returns the DG parameter for a single component of a multi-component problem
     * 
     * @param component which component of the problem are we talking about 
     * @return real 
     */
    void getInterfaceValues(const real* internalVariables, real* myValues) const;
    
        /**
     * @brief Base implementation of viscous boundary flux
     *
     * The base implementation provides the capability to do surface integrals
     * this function can depend on the gradient
     * TODO improve documentation //(up,qp,np,ib,ui,p,param,time)
     */
    virtual void viscousBoundaryFlux(const int ndm, const int ndf, real dt, real measInterface, real* internal_new, const real* concentration_L, const real* concentration_L0, const real* Dconcentration_L, const real* Dconcentration_L0, const real* Normal, const real* Coord, real* fDotN, real* dFDotNdC, real* dFDotNdgradC) const;
  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    WeakTemperature(const WeakTemperature&);

    static Register<Material, WeakTemperature> reg;
    
    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    WeakTemperature& operator=(const WeakTemperature&);

    real _theta;
    real _myFactor;
};

}  // namespace summit

#endif // SUMMIT_WEAK_TEMPERATURE_H

// end of file