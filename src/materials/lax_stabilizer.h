/**
 * @file lax_stabilizer.h
 * @brief Lax-<PERSON>s stabilization for interface materials
 * <AUTHOR> Development Team
 * @date 2012-2023
 *
 * This file contains the LaxStabilizer class which implements Lax-Friedrichs
 * stabilization for interface materials in discontinuous Galerkin formulations.
 * The stabilization helps ensure numerical stability in hyperbolic systems.
 */

#ifndef SUMMIT_LAX_STABILIZER_H
#define SUMMIT_LAX_STABILIZER_H

// #include "interface_dg.h"
#include "upwind_interface_dg.h"
#include "../summit_enum.h"
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

// Number of internal variables:
// the value is used to size the memory allocation for the material LaxStabilizer
#define LAX_STABILIZER_NUMBER_INTERNAL_VARIABLES 0
namespace summit {

class Checkpoint;

/**
 * @brief Lax-<PERSON>s stabilization for interface materials
 *
 * The LaxStabilizer class implements Lax-Friedrichs stabilization for interface
 * materials in discontinuous Galerkin formulations. This stabilization technique
 * is particularly useful for hyperbolic systems where numerical stability is
 * crucial for accurate solutions.
 *
 * Key features:
 * - Implements Lax-Friedrichs flux stabilization
 * - Inherits from UpwindInterfaceDG for upwind capabilities
 * - Supports various interface configurations
 * - Provides numerical stability for hyperbolic problems
 * - Integrates with the Summit material library system
 *
 * @note This class is designed for interface stabilization, not bulk materials
 */
class LaxStabilizer : public UpwindInterfaceDG {
  public:
    /**
     * Constructor (default)
     * This method should not be used when deriving from LaxStabilizer
     */
    LaxStabilizer();

    /**
     * Constructor
     * This method should not be used when deriving from LaxStabilizer
     * @param[in] name a string that defines the name of the material
     */
    LaxStabilizer(const std::string& name);

    /**
     * Constructor
     * This method should be used when deriving from LaxStabilizer
     * @param[in] name a string that defines the name of the material
     * @param[in] nInt number of internal variables
     */
    LaxStabilizer(const std::string& name, const int nInt);

    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     * @param[in] leftMatLabel Left material Label
     * @param[in] rightMatLabel Right material Label
     * @param[in] beta stability parameter
     * @param[in] numberComponents Type of criterion that must be satisfied at
     *            quadrature points to initiate the fracture
     */
    LaxStabilizer(const std::string& name,
               const int leftMatLabel,
               const int rightMatLabel,
               const real beta,
               const int numberComponents,
               const int nInt = LAX_STABILIZER_NUMBER_INTERNAL_VARIABLES);

    virtual MaterialType GetMaterialType() const { return MATERIAL_LaxStabilizer; }

    /**
     * Constructor
     * @param[in] checkpoint object associated with restart
     * @param[in] name the name of the group within the checkpoint object
     */
    LaxStabilizer(Checkpoint* checkpoint, const char* name);

    /**
     * Destructor
     */
    virtual ~LaxStabilizer();

    /**
     * Method to display the material parameters to the output chanel
     */
    void Display() override;

        /**
     * @brief Implementation of upwind stabilization (LAX_STABILIZER_NUMBER_INTERNAL_VARIABLES flux) for convection
     *
     *
     * @param[in] concentration_L Current concentration values at the left state
     * @param[in] concentration_L0 Previous time step concentration values at the left state
     * @param[in] concentration_R Current concentration values at the right state
     * @param[in] concentration_R0 Previous time step concentration values at the right state
     * @param[in] Normal Normal vector at the interface, pointing from left to right
     * @param[out] C Stabilization terms (array of length number_unknowns())
     * @param[out] dC Derivatives of stabilization terms (for implicit schemes)
     */
    virtual void upwindStabilization(const real* concentration_L, const real* concentration_L0, const real* concentration_R, const real* concentration_R0, const real* Normal, real* C, real* dC) const;//this should be made virtual and reimplemented in 

    /**
     * @brief returns the DG parameter for a single component of a multi-component problem
     * 
     * @param component which component of the problem are we talking about 
     * @return real 
     */
    void getInterfaceDamage(const real* internalVariables, real* myDamage) const;

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    LaxStabilizer(const LaxStabilizer&);

    static Register<Material, LaxStabilizer> reg;
    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    LaxStabilizer& operator=(const LaxStabilizer&);

    real _ux;

    real _uy;

  public:  
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

  protected:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void SetInternalVariableMap();
};
}  // namespace summit

#endif

// end of file
