#include "reaction_diffusion_material.h"
#include <cstdlib>
#include <cstdio>
#include <iostream>
#include <math.h>
#include <cstring>
#include <fstream>

#include "../mathlib/mathlib.h"

/**
 * @brief Parse material parameters from input file line
 *
 * Extracts numerical parameters from a space-separated string, typically
 * from a material input file. Provides error checking and validation.
 *
 * @param[in] filename Source filename for error reporting
 * @param[in] line String containing space-separated numerical values
 * @return Vector of parsed parameter values
 */
std::vector<summit::real> summit::ReactionDiffusionMaterial::GetMaterialParameters(
  const std::string& filename, const std::string& line)
{
    // instantiate a ifstream object and try to open it
    std::ifstream source(filename.c_str());
    // if it fails
    if (source.fail()) {
        // say something useful...
        std::cout << "Error in ReactionDiffusionMaterial::Load : cannot find material file: "
                  << filename.c_str() << std::endl;
        //... and die
        exit(1);
    }

    // split it and cast the values to string, here needs to cast string into a non const
    std::vector<std::string> string_values =
      StringUtil<int>::SplitIntoString(const_cast<std::string&>(line));

    // convert into a vector of real (and removed 2 first elements: material number and name)
    std::vector<real> values = vecString2vecReal(string_values);

    // end of method
    return values;
}

void summit::ReactionDiffusionMaterial::SetLocationInInternalTable(std::string const& name,
                                                              int position,
                                                              size_t size)
{
    _mapIntVarNameToLocation.insert(
      mapStringToPair_t::value_type(name, std::make_pair(position, size)));

    // all done
    return;
}

real summit::ReactionDiffusionMaterial::bulkModulus(real const* internal) const
{
    return 0;
}
// end of file
