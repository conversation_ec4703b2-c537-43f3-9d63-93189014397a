#include "reaction_diffusion_material.h"
#include <cstdlib>
#include <cstdio>
#include <iostream>
#include <math.h>
#include <cstring>
#include <fstream>

#include "../mathlib/mathlib.h"

std::vector<summit::real> summit::ReactionDiffusionMaterial::GetMaterialParameters(
  const std::string& filename, const std::string& line)
{
    // instantiate a ifstream object and try to open it
    std::ifstream source(filename.c_str());
    // if it fails
    if (source.fail()) {
        // say something useful...
        std::cout << "Error in ReactionDiffusionMaterial::Load : cannot find material file: "
                  << filename.c_str() << std::endl;
        //... and die
        exit(1);
    }

    // split it and cast the values to string, here needs to cast string into a non const
    std::vector<std::string> string_values =
      StringUtil<int>::SplitIntoString(const_cast<std::string&>(line));

    // convert into a vector of real (and removed 2 first elements: material number and name)
    std::vector<real> values = vecString2vecReal(string_values);

    // end of method
    return values;
}

void summit::ReactionDiffusionMaterial::SetLocationInInternalTable(std::string const& name,
                                                              int position,
                                                              size_t size)
{
    _mapIntVarNameToLocation.insert(
      mapStringToPair_t::value_type(name, std::make_pair(position, size)));

    // all done
    return;
}

real summit::ReactionDiffusionMaterial::bulkModulus(real const* internal) const
{
    return 0;
}

/**
 * @brief Base implementation of upwind stabilization (no stabilization)
 *
 * The base implementation provides no stabilization by setting the stabilization
 * term to zero. Derived classes should override this method to implement
 * appropriate upwinding schemes for their specific physics.
 *
 * This default implementation is suitable for diffusion-dominated problems
 * where numerical stabilization is not required. For advection-dominated problems,
 * derived classes should implement more sophisticated upwinding schemes.
 *
 * @param[in] concentration_L Current concentration values at the left state
 * @param[in] concentration_L0 Previous time step concentration values at the left state
 * @param[in] concentration_R Current concentration values at the right state
 * @param[in] concentration_R0 Previous time step concentration values at the right state
 * @param[in] Normal Normal vector at the interface, pointing from left to right
 * @param[out] C Stabilization terms (array of length number_unknowns())
 * @param[out] dC Derivatives of stabilization terms (for implicit schemes)
 */
void summit::ReactionDiffusionMaterial::upwindStabilization(const real* concentration_L, const real* concentration_L0, const real* concentration_R, const real* concentration_R0, const real* Normal, real* C, real* dC) const
{
    C[0] = 0;
    return;
}
// end of file
