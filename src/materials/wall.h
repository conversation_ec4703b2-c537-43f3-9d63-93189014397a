/**
 * @file wall.h
 * @brief Wall boundary condition material for fluid dynamics simulations
 * <AUTHOR> Development Team
 * @date 2012-2023
 *
 * This file contains the Wall class which implements wall boundary conditions
 * for fluid dynamics simulations. Wall boundaries represent solid surfaces
 * where the fluid velocity must satisfy no-slip or slip conditions, and
 * heat transfer may be specified or computed.
 */

#ifndef SUMMIT_WALL_H
#define SUMMIT_WALL_H

#include "roe_flux.h"

namespace summit {

class Checkpoint;

/**
 * @brief Wall boundary condition material for fluid dynamics simulations
 *
 * The Wall class implements wall boundary conditions for compressible and
 * incompressible fluid dynamics simulations. Wall boundaries represent solid
 * surfaces that interact with the fluid through viscous and thermal effects.
 * This class inherits from RoeFlux to leverage robust flux calculations while
 * implementing specific wall boundary condition logic.
 *
 * ## Physical Boundary Conditions
 *
 * ### Velocity Boundary Conditions
 *
 * #### No-Slip Condition (Viscous Walls)
 * For viscous flows, the fluid velocity at the wall equals the wall velocity:
 * - **Stationary wall**: u_wall = 0, v_wall = 0, w_wall = 0
 * - **Moving wall**: u_wall = U_wall, v_wall = V_wall, w_wall = W_wall
 * - **Rotating wall**: Velocity computed from angular velocity and position
 *
 * #### Slip Condition (Inviscid Walls)
 * For inviscid flows, only the normal velocity component is zero:
 * - **Normal velocity**: u·n = 0 (no penetration)
 * - **Tangential velocity**: Free to slip along the wall
 * - **Pressure**: Computed from momentum equations
 *
 * ### Thermal Boundary Conditions
 *
 * #### Isothermal Wall
 * Temperature is prescribed at the wall:
 * - **Constant temperature**: T_wall = T_prescribed
 * - **Time-dependent**: T_wall = f(t)
 * - **Spatially varying**: T_wall = f(x,y,z)
 *
 * #### Adiabatic Wall
 * No heat transfer through the wall:
 * - **Zero heat flux**: ∂T/∂n = 0
 * - **Natural boundary condition**: Emerges from weak formulation
 * - **Temperature gradient**: Computed from energy equation
 *
 * #### Heat Flux Wall
 * Prescribed heat flux at the wall:
 * - **Constant flux**: q̇_wall = constant
 * - **Convective heating**: q̇ = h(T_∞ - T_wall)
 * - **Radiative heating**: q̇ = εσ(T_∞⁴ - T_wall⁴)
 *
 * ## Mathematical Formulation
 *
 * ### Momentum Equations at Walls
 * For the compressible Navier-Stokes equations:
 * ∂(ρu)/∂t + ∇·(ρu⊗u + pI - τ) = 0
 *
 * At the wall boundary:
 * - **Velocity constraint**: u = u_wall
 * - **Stress computation**: τ = μ(∇u + ∇uᵀ) - (2/3)μ(∇·u)I
 * - **Pressure**: Computed from normal momentum balance
 *
 * ### Energy Equation at Walls
 * For the energy equation:
 * ∂(ρE)/∂t + ∇·((ρE + p)u - k∇T - u·τ) = 0
 *
 * At the wall boundary:
 * - **Temperature**: Prescribed or computed from heat flux
 * - **Heat conduction**: q = -k∇T
 * - **Viscous heating**: Φ = τ:∇u
 *
 * ## Numerical Implementation
 *
 * ### Discontinuous Galerkin Treatment
 * Wall boundaries are implemented through interface flux terms:
 * - **Momentum flux**: Enforces velocity constraints
 * - **Energy flux**: Handles thermal boundary conditions
 * - **Upwind stabilization**: Ensures numerical stability
 *
 * ### Flux Calculation Strategy
 * The wall flux is computed by:
 * 1. **Mirror state construction**: Create ghost state satisfying wall conditions
 * 2. **Roe flux evaluation**: Apply standard Roe flux between interior and ghost states
 * 3. **Boundary condition enforcement**: Modify flux to satisfy wall constraints
 *
 * ### Ghost State Construction
 * For different wall types:
 * - **Slip wall**: Mirror normal velocity, preserve tangential velocity
 * - **No-slip wall**: Set all velocity components to wall velocity
 * - **Isothermal wall**: Set temperature to prescribed value
 * - **Adiabatic wall**: Mirror temperature to ensure zero normal gradient
 *
 * ## Applications
 *
 * ### Aerospace Engineering
 * - **Aircraft surfaces**: Wing and fuselage boundaries
 * - **Engine components**: Combustor and turbine walls
 * - **Hypersonic vehicles**: High-temperature wall effects
 * - **Propulsion systems**: Nozzle and inlet walls
 *
 * ### Automotive Engineering
 * - **Vehicle aerodynamics**: Body surface boundaries
 * - **Engine cooling**: Cylinder and head walls
 * - **Exhaust systems**: Pipe and manifold walls
 * - **Brake systems**: Disc and caliper surfaces
 *
 * ### Industrial Applications
 * - **Heat exchangers**: Tube and shell walls
 * - **Turbomachinery**: Blade and casing surfaces
 * - **Process equipment**: Reactor and vessel walls
 * - **HVAC systems**: Duct and component boundaries
 *
 * ### Biomedical Engineering
 * - **Cardiovascular flows**: Arterial walls
 * - **Respiratory systems**: Airway surfaces
 * - **Medical devices**: Catheter and stent walls
 * - **Drug delivery**: Microfluidic channel walls
 *
 * ## Advanced Features
 *
 * ### Moving Walls
 * - **Prescribed motion**: Time-dependent wall velocity
 * - **Fluid-structure interaction**: Coupled wall motion
 * - **Rotating machinery**: Turbomachinery applications
 * - **Deforming boundaries**: Adaptive mesh considerations
 *
 * ### Rough Walls
 * - **Surface roughness effects**: Modified wall functions
 * - **Turbulence modeling**: Enhanced wall treatment
 * - **Heat transfer augmentation**: Roughness-induced mixing
 * - **Pressure loss modeling**: Friction factor correlations
 *
 * ### Porous Walls
 * - **Transpiration cooling**: Mass injection through walls
 * - **Perforated surfaces**: Acoustic liner applications
 * - **Filtration boundaries**: Porous media interfaces
 * - **Catalytic walls**: Surface reaction modeling
 *
 * ## Numerical Considerations
 *
 * ### Mesh Requirements
 * - **Boundary layer resolution**: Adequate near-wall mesh density
 * - **Wall-normal spacing**: y⁺ considerations for turbulent flows
 * - **Aspect ratio control**: Avoid highly skewed elements
 * - **Mesh orthogonality**: Proper normal vector computation
 *
 * ### Stability Considerations
 * - **CFL condition**: Wall proximity affects time step
 * - **Viscous stability**: Diffusion number limitations
 * - **Thermal stability**: Heat conduction time scales
 * - **Boundary layer stability**: High Reynolds number effects
 *
 * ### Accuracy Requirements
 * - **Wall function accuracy**: Near-wall gradient resolution
 * - **Heat transfer prediction**: Temperature gradient accuracy
 * - **Skin friction computation**: Wall shear stress calculation
 * - **Pressure distribution**: Wall pressure accuracy
 *
 * @note This class inherits from RoeFlux for robust flux computation
 * @note Supports both 2D and 3D wall boundary conditions
 * @note Can handle moving, rough, and porous wall effects
 * @note Integrates with turbulence models for wall function treatment
 *
 * @warning Requires adequate mesh resolution near walls
 * @warning Moving walls may require special time integration
 * @warning High-temperature walls need careful property evaluation
 * @warning Turbulent flows require appropriate wall treatment
 *
 * @see RoeFlux for base flux computation capabilities
 * @see SurfaceHeating for thermal boundary condition alternatives
 * @see WeakTemperature for temperature-based boundary conditions
 */
class Wall : public RoeFlux {
  public:
    /**
     * @brief Default constructor for Wall boundary condition
     *
     * Creates a Wall object with default parameters suitable for basic
     * wall boundary condition applications. Default settings provide
     * a foundation for standard no-slip, adiabatic wall conditions.
     *
     * @note Prefer named constructors for better code clarity
     * @see Wall(const std::string&) for named construction
     */
    Wall();

    /**
     * @brief Named constructor for Wall boundary condition
     *
     * Creates a Wall object with a specified name identifier. This constructor
     * is suitable for standard wall boundary conditions where default internal
     * variable allocation and parameters are sufficient.
     *
     * @param[in] name Descriptive name for the wall material (e.g., "AircraftWing", "PipeWall")
     *
     * @pre name must be a non-empty string
     * @note Uses default internal variable count for standard wall applications
     * @note Inherits Roe flux capabilities for robust boundary flux computation
     * @see Wall(const std::string&, const int) for custom internal variables
     */
    Wall(const std::string& name);

    /**
     * @brief Constructor with custom internal variable allocation
     *
     * Creates a Wall object with specified name and number of internal variables.
     * This constructor is useful for advanced wall models that require additional
     * state variables for complex boundary condition implementations.
     *
     * @param[in] name Descriptive name for the wall material
     * @param[in] nInt Number of internal variables for state tracking
     *
     * @pre name must be a non-empty string
     * @pre nInt must be non-negative
     *
     * @note Internal variables can store:
     *       - Wall temperature history for thermal inertia
     *       - Surface roughness parameters for turbulence modeling
     *       - Moving wall velocity components
     *       - Transpiration or suction/blowing rates
     *       - Surface catalytic reaction states
     */
    Wall(const std::string& name, const int nInt);

    /**
     * @brief Full constructor with interface coupling parameters
     *
     * Creates a Wall object with complete specification of interface coupling
     * parameters for multi-material fluid simulations. This constructor provides
     * full control over the wall boundary behavior and numerical stability.
     *
     * @param[in] name Descriptive name for the wall boundary material
     * @param[in] leftMatLabel Material label for the left-side bulk material (1-based index)
     * @param[in] rightMatLabel Material label for the right-side bulk material (1-based index)
     * @param[in] beta Stability parameter for interface flux stabilization [dimensionless]
     * @param[in] numberComponents Number of flow solution components (e.g., 5 for 3D Euler)
     * @param[in] nInt Number of internal variables (default: 0)
     *
     * @pre name must be a non-empty string
     * @pre leftMatLabel and rightMatLabel must be valid material indices (≥ 1)
     * @pre beta must be non-negative (typically 0.0 to 2.0)
     * @pre numberComponents must be positive (typically 4 for 2D, 5 for 3D compressible flow)
     * @pre nInt must be non-negative
     *
     * @note The stability parameter beta controls numerical dissipation:
     *       - beta = 0.0: Minimal dissipation (may require small time steps)
     *       - beta = 1.0: Standard stabilization for most wall applications
     *       - beta > 1.0: Enhanced stability for high Reynolds number or moving walls
     * @note Material labels must correspond to valid entries in the material library
     * @note For compressible Euler equations: numberComponents = 4 (2D) or 5 (3D)
     * @note For Navier-Stokes equations: same component count with viscous effects
     * @note Moving walls or complex thermal conditions may require additional internal variables
     */
    Wall(const std::string& name,
           const int leftMatLabel,
           const int rightMatLabel,
           const real beta,
           const int numberComponents,
           const int nInt = 0);

    /**
     * Constructor
     * @param[in] checkpoint object associated with restart
     * @param[in] name the name of the group within the checkpoint object
     */
    Wall(Checkpoint* checkpoint, const char* name);

    /**
     * Destructor
     */
    virtual ~Wall();

    /**
     * Method to display the material parameters to the output chanel
     */
    void Display() override;

    virtual MaterialType GetMaterialType() const { return MATERIAL_Wall; }

    /**
     * Method to know if the interface law is for boundary conditions
     * @return true if this model is for external surfaces
     */
    bool IsBoundary() const { return true; }

    /**
     * @brief Base implementation of Wall condition for gas dynamics
     *
     * The base implementation provides the capability to do surface integrals
     * this function cannot depend on the gradient
     * TODO improve documentation //(up,qp,np,ib,ui,p,param,time)
     */
    void inviscidBoundaryFlux(const int ndm, const int ndf, real dt, real measInterface, real* internal_new, const real* concentration_L, const real* concentration_L0, const real* Normal, const real* Coord, real* fDotN, real* dFDotNdC) const;

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    Wall(const Wall&);

    static Register<Material, Wall> reg;
    
    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    Wall& operator=(const Wall&);

    /**
     * @brief this computes the Flux doted with the normal
     * It should really borrow the bulk material's implementation for consistency
     * This could be improved in afuture rewrite
     *
     */
    void flux_dotted_n(const real* concentration, const real* Normal, real* FdotN) const;

};

}  // namespace summit

#endif // SUMMIT_IN_FLOW_H

// end of file