// -*- C++ -*-
//
// rrgroup
// summit development team
// massachusetts institute of technology
// (c) 2012-2023 all rights reserved
//

#include "wall.h"
#include <iostream>
#include <cstdlib>
#include <cmath>
#include <pyre/journal.h>
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif
#include "../utils/util.h"
#include "../io/summit_message.h"
#include "../restart/checkpoint.h"

summit::Wall::Wall() : RoeFlux("Wall", 0)
{
}

summit::Wall::Wall(const std::string& name) : <PERSON><PERSON>lux(name, 0)
{
}

summit::Wall::Wall(const std::string& name, const int nInt) : <PERSON><PERSON>lux(name, nInt)
{
}

summit::Wall::Wall(const std::string& name,
                       const int leftMatLabel,
                       const int rightMatLabel,
                       const real beta,
                       const int numberComponents,
                       const int nInt)
  : <PERSON><PERSON>lux(name, leftMatLabel, rightMatLabel, beta, numberComponents, nInt)
{
}

summit::Wall::Wall(Checkpoint* checkpoint, const char* name) : <PERSON>Flux(checkpoint, name)
{
}

summit::Wall::~Wall()
{
}

/**
 * @brief Compute inviscid boundary flux for wall boundary conditions
 *
 * This method computes the inviscid flux contribution at wall boundaries for
 * compressible flow simulations. The wall boundary condition enforces the
 * no-penetration condition (zero normal velocity) while allowing tangential
 * slip for inviscid flows.
 *
 * ## Mathematical Formulation
 *
 * For inviscid wall boundaries, the key constraint is:
 * **u·n = 0** (no penetration condition)
 *
 * ### Ghost State Construction
 * The wall flux is computed using a ghost state approach:
 * 1. **Mirror normal velocity**: u_ghost·n = -u_interior·n
 * 2. **Preserve tangential velocity**: u_ghost·t = u_interior·t
 * 3. **Preserve thermodynamic state**: ρ_ghost = ρ_interior, p_ghost = p_interior
 *
 * ### Implementation Details
 * For 2D compressible Euler equations:
 * - **Density**: ρ_ghost = ρ_interior
 * - **x-momentum**: (ρu)_ghost = (ρu)_interior - 2ρ(u·n)n_x
 * - **y-momentum**: (ρv)_ghost = (ρv)_interior - 2ρ(u·n)n_y
 * - **Energy**: (ρE)_ghost = (ρE)_interior
 *
 * The flux is then computed as the Roe flux between interior and ghost states.
 *
 * @param[in] ndm Number of spatial dimensions (2 or 3)
 * @param[in] ndf Number of degrees of freedom per node (4 for 2D, 5 for 3D Euler)
 * @param[in] dt Time step size [s]
 * @param[in] measInterface Interface measure [m or m²]
 * @param[in,out] internal_new Updated internal variables
 * @param[in] concentration_L Interior state conservative variables [ρ, ρu, ρv, ρE]
 * @param[in] concentration_L0 Previous time step interior state
 * @param[in] Normal Outward unit normal vector from wall [nx, ny]
 * @param[in] Coord Wall surface coordinates [m]
 * @param[out] fDotN Normal flux components
 * @param[out] dFDotNdC Flux Jacobian matrix
 *
 * @note This function cannot depend on the gradient (inviscid assumption)
 * @note Implements slip wall condition for inviscid flows
 * @note Uses ghost state method with velocity mirroring
 *
 * @see RoeFlux::upwindStabilization() for flux computation details
 */
void summit::Wall::inviscidBoundaryFlux(const int ndm, const int ndf, real dt, real measInterface, real* internal_new, const real* concentration_L, const real* concentration_L0, const real* Normal, const real* Coord, real* fDotN, real* dFDotNdC) const
{
    
    std::vector<real> um(4);
    real uNormal = concentration_L[1] * Normal[0] + concentration_L[2] * Normal[1];
    um[0] = concentration_L[0];
    um[1] = concentration_L[1] - 2.0 * uNormal * Normal[0];
    um[2] = concentration_L[2] - 2.0 * uNormal * Normal[1];
    um[3] = concentration_L[3];

    std::vector<real> C;
    C.resize(_numberComponents);
    for (int c = 0; c < _numberComponents; c++) {
        C[c] = 0.0;
    }
    std::vector<real> dC;
    dC.resize(_numberComponents*_numberComponents*2);// derivative of stab term with respect to left
    // and right state
    upwindStabilization(concentration_L, concentration_L0, um.data(),um.data(), Normal, C.data(), dC.data());
    
    std::vector<real> junk(4);
    flux_dotted_n(concentration_L, Normal, junk.data());
    for (int comp = 0;comp< 4; comp++){
        fDotN[comp] =  -0.5*junk[comp];// minus signs are holdover from the sign errors in sumMIT!!! very important
        junk[comp] = 0.0;
    }
    flux_dotted_n(um.data(), Normal, junk.data());
    for (int comp = 0;comp< 4; comp++){
        fDotN[comp] +=  -0.5*junk[comp] - C[comp];// minus signs are holdover from the sign errors in sumMIT!!! very important
        //fDotN[comp] = 0.0;
    }
    // if(Coord[1]>0.15){
    //     std::cout << "fDotN[2]: "<< fDotN[2] << "  C[2]: "<< C[2] << "  Normal[0]: "<< Normal[0] << "  Normal[1]: "<< Normal[1] <<  std::endl;
    // }

    return;
}

/**
 * @brief this computes the Flux doted with the normal
 * It should really borrow the bulk material's implementation for consistency
 * This could be improved in afuture rewrite
 *
 */
void summit::Wall::flux_dotted_n(const real* concentration, const real* Normal, real* FdotN) const
{
    real rho = concentration[0];
    real uv = concentration[1]/rho;
    real vv = concentration[2]/rho;
    real p = (this->getGamma()-1.0) * (concentration[3] - 0.5 *rho*(uv*uv+vv*vv));

    FdotN[0] = rho * (uv * Normal[0]+ vv * Normal[1]);
    FdotN[1] = (uv * uv * rho + p) * Normal[0] + (uv * vv * rho) * Normal[1];
    FdotN[2] = (uv * vv * rho)* Normal[0] + (vv * vv * rho + p) * Normal[1];
    FdotN[3] = (uv * (concentration[3]+p)) * Normal[0] + (vv * (concentration[3]+p)) * Normal[1];

    return;
}

void summit::Wall::Display()
{
    // display
    Message::Info(" Material Characteristics : (Wall)");
    Message::Info("\t Label of left material ............................................... =\t%d",
                  _leftMatLabel);
    Message::Info("\t Label of right material .............................................. =\t%d",
                  _rightMatLabel);
}

// Register Wall as option for class to instantiate
REGISTER(Material, Wall);

// end of file