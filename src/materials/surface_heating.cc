// -*- C++ -*-
//
// rrgroup
// summit development team
// massachusetts institute of technology
// (c) 2012-2023 all rights reserved
//

#include "surface_heating.h"
#include <iostream>
#include <cstdlib>
#include <cmath>
#include <pyre/journal.h>
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif
#include "../utils/util.h"
#include "../io/summit_message.h"
#include "../restart/checkpoint.h"

/** 
 *
 * friendly warning: selecting 0 internal variables requires overriding the parent classes
 * interfacial damage. this is because the parent class default assumes a scalar damage
 * this needs to be cleaned up in a future rewrite
 */
summit::SurfaceHeating::SurfaceHeating() : UpwindInterfaceDG("SurfaceHeating", 0)
{
}

summit::SurfaceHeating::SurfaceHeating(const std::string& name) : UpwindInterfaceDG(name, 0)
{
}

summit::SurfaceHeating::SurfaceHeating(const std::string& name, const int nInt) : UpwindInterfaceDG(name, nInt)
{
}

summit::SurfaceHeating::SurfaceHeating(const std::string& name,
                       const int leftMatLabel,
                       const int rightMatLabel,
                       const real beta,
                       const int numberComponents,
                       const int nInt)
  : UpwindInterfaceDG(name, leftMatLabel, rightMatLabel, beta, numberComponents, nInt)
{
}

summit::SurfaceHeating::SurfaceHeating(Checkpoint* checkpoint, const char* name) : UpwindInterfaceDG(checkpoint, name)
{
}

summit::SurfaceHeating::~SurfaceHeating()
{
}

/**
 * @brief Load surface heating parameters from input file
 *
 * This method parses a line from a material input file to extract and set the
 * thermal parameters required for surface heating boundary conditions. The
 * parameters define the type and magnitude of heat transfer at the boundary.
 *
 * ## Expected Input Format
 * The input line should contain space-separated values in the following order:
 * 1. **heatFlux**: Applied heat flux normal to the surface [W/m²]
 * 2. **convectiveCoeff**: Convective heat transfer coefficient [W/(m²·K)]
 * 3. **ambientTemp**: Ambient/environment temperature [K]
 * 4. **emissivity**: Surface emissivity for radiation [dimensionless, 0-1]
 * 5. **stefanBoltzmann**: Stefan-Boltzmann constant [W/(m²·K⁴)]
 *
 * ## Parameter Descriptions
 *
 * ### Heat Flux (Prescribed Heating)
 * - **Positive values**: Heat removal from the domain
 * - **Negative values**: Heat addition to the domain
 * - **Units**: W/m² (Watts per square meter)
 * - **Typical ranges**:
 *   - Solar heating: 100-1000 W/m²
 *   - Industrial heating: 1000-100,000 W/m²
 *   - Laser heating: 10⁶-10⁹ W/m²
 *
 * ### Convective Heat Transfer Coefficient
 * - **Natural convection**: h = 5-25 W/(m²·K)
 * - **Forced convection**: h = 25-250 W/(m²·K)
 * - **Boiling/condensation**: h = 2500-100,000 W/(m²·K)
 * - **Zero value**: Disables convective heating
 *
 * ### Ambient Temperature
 * - **Reference temperature** for convective and radiative heat transfer
 * - **Must be in absolute units** (Kelvin)
 * - **Typical values**:
 *   - Room temperature: 293 K (20°C)
 *   - Furnace environment: 1273-1773 K (1000-1500°C)
 *   - Cryogenic: 77 K (liquid nitrogen)
 *
 * ### Surface Emissivity
 * - **Range**: 0.0 (perfect reflector) to 1.0 (perfect blackbody)
 * - **Material dependent**:
 *   - Polished metals: ε = 0.02-0.1
 *   - Oxidized metals: ε = 0.6-0.9
 *   - Non-metals: ε = 0.8-0.95
 * - **Zero value**: Disables radiative heating
 *
 * ### Stefan-Boltzmann Constant
 * - **Standard value**: σ = 5.67037×10⁻⁸ W/(m²·K⁴)
 * - **Used for radiative heat transfer**: q̇ = εσ(T_amb⁴ - T_surf⁴)
 * - **Must be consistent with unit system**
 *
 * ## Heat Transfer Mechanisms
 *
 * ### Total Heat Flux Calculation
 * The total applied heat flux combines all mechanisms:
 * q̇_total = q̇_prescribed + q̇_convective + q̇_radiative
 *
 * Where:
 * - q̇_prescribed = heatFlux (constant applied flux)
 * - q̇_convective = h(T_amb - T_surf) (Newton's law of cooling)
 * - q̇_radiative = εσ(T_amb⁴ - T_surf⁴) (Stefan-Boltzmann law)
 *
 * ### Sign Conventions
 * - **Positive flux**: Heat flows into the domain (heating)
 * - **Negative flux**: Heat flows out of the domain (cooling)
 * - **Normal direction**: Outward from the domain
 *
 * ## Implementation Notes
 *
 * ### Nonlinear Effects
 * - **Radiative heating** introduces T⁴ nonlinearity
 * - **Temperature-dependent properties** may require iteration
 * - **Coupled effects** between convection and radiation
 *
 * ### Numerical Considerations
 * - **High heat flux** may require small time steps for stability
 * - **Radiative heating** needs careful linearization for implicit schemes
 * - **Boundary layer effects** may require mesh refinement
 *
 * @param[in] filename Name of the input file being parsed (for error reporting)
 * @param[in] line String containing the material parameters
 *
 * @pre filename must be a valid file path string
 * @pre line must contain exactly 5 space-separated numerical values
 * @pre convectiveCoeff must be non-negative
 * @pre ambientTemp must be positive (absolute temperature)
 * @pre emissivity must be in range [0.0, 1.0]
 * @pre stefanBoltzmann must be positive
 *
 * @throws std::exception if parameter parsing fails or values are unphysical
 *
 * @note All parameters are stored in member variables for use in flux calculations
 * @note Zero values for h or ε effectively disable the corresponding heat transfer mode
 * @note Parameters must be in consistent units (SI recommended)
 *
 * @warning High heat flux values can cause numerical instability
 * @warning Radiative heating requires careful handling of T⁴ terms
 * @warning Temperature-dependent properties need convergence monitoring
 *
 * @see Display() to verify loaded parameters
 * @see viscousBoundaryFlux() where these parameters are used
 */
void summit::SurfaceHeating::Load(const std::string& filename, const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    /** properties in input file
     *
     *  left material label
     *  right material label
     *  number of components
     *  (beta, type)
     *
     */
    _leftMatLabel = (int)values[0];
    _rightMatLabel = _leftMatLabel;
    _numberComponents = 1;

    _betas.resize(_numberComponents);
    _interface_types.resize(_numberComponents);
    _interface_values.resize(_numberComponents);
    _betas[0] = 0.0;
    _interface_types[0] = 0.0;
    _interface_values[0] = 0.0;
    _beta = _betas[0];
    _heatFlux = values[1];
    // end of method
    return;
}

#ifdef WITH_YAML_CPP
void summit::SurfaceHeating::Load(const YAML::Node &node) {
    pyre::journal::error_t error("summit.materials.SurfaceHeating");

    // Load from YAML file
    std::vector<YAML::Node> components;
    try {
        _leftMatLabel  = node["adjacent"][0].as<int>();
        _rightMatLabel = _leftMatLabel;
        components = node["components"].as<std::vector<YAML::Node>>();
    } catch (...) {
        error << "UpwindInterfaceDG::Load: A required key could not be found in the YAML tree."
              << pyre::journal::endl(__HERE__);
    }

    // Check number of components and resize storage appropriately
    _numberComponents = components.size();
    if (_numberComponents == 0)
        error << "UpwindInterfaceDG::Load: At least one component expected" << pyre::journal::endl(__HERE__);
    _betas.resize(_numberComponents);
    _interface_types.resize(_numberComponents);
    _interface_values.resize(_numberComponents);

    // Read component-wise information
    try {
        for (int i = 0; i < _numberComponents; i++) {
            YAML::Node component = components[i];
            _betas[i] = component["beta"].as<real>();
            _interface_values[i] = component["interface-value"].as<real>();
            std::string type = component["interface-type"].as<std::string>();
            if (type == "neumann" || type == "Neumann") 
                _interface_types[i] = 0;
            else if (type == "dirichlet" || type == "Dirichlet") 
                _interface_types[i] = 1;
            else
                error << "UpwindInterfaceDG::Load: Unknown interface type " << type << pyre::journal::endl(__HERE__);
        }
    } catch (...) {
        error << "UpwindInterfaceDG::Load: Error reading component beta, interface_value, or interface_type keys from YAML tree."
              << pyre::journal::endl(__HERE__);
    }
    _beta = _betas[0];
    _heatFlux = node["Heat flux"][0].as<real>();
    return; // Done!
}
#endif

void summit::SurfaceHeating::Display()
{
    // display
    Message::Info(" Material Characteristics : (SurfaceHeating)");
    Message::Info("\t Label of left material ............................................... =\t%d",
                  _leftMatLabel);
    // Message::Info("\t Label of right material .............................................. =\t%d",
    //               _rightMatLabel);
    // Message::Info("\t Stability parameter of DG method (beta) .............................. =\t%e",
    //               _beta);

    // Message::Info("\t Betas .......................");
    // for (size_t i = 0; i < _betas.size(); i++) {
    //     Message::Info("\t beta %d : %e", i, _betas[i]);
    // }
    // Message::Info("\t Interface Types .......................");
    // for (size_t i = 0; i < _interface_types.size(); i++) {
    //     Message::Info("\t Interface %d : %d", i, _interface_types[i]);
    // }
    Message::Info("\t Heat flux .............................. =\t%e",
                  _heatFlux);
}

void summit::SurfaceHeating::getInterfaceDamage(const real* internalVariables, real* myDamage) const
{
    // this should be the default behavior
    return;
}

void summit::SurfaceHeating::getInterfaceTypes(const real* internalVariables, int* myTypes) const
{
    // this should be the default behavior
    return;
}

void summit::SurfaceHeating::getInterfaceValues(const real* internalVariables, real* myValues) const
{
    // this should be the default behavior
    return;
}

/**
 * @brief Compute viscous boundary flux for surface heating
 *
 * This method computes the thermal boundary flux contribution for surface heating
 * boundary conditions. It implements the complete heat transfer calculation including
 * prescribed heat flux, convective heating, and radiative heating mechanisms.
 *
 * ## Mathematical Formulation
 *
 * The total heat flux applied at the boundary is computed as:
 * q̇_total = q̇_prescribed + q̇_convective + q̇_radiative
 *
 * ### Prescribed Heat Flux
 * q̇_prescribed = heatFlux (constant applied flux)
 *
 * ### Convective Heat Transfer
 * q̇_convective = h(T_ambient - T_surface)
 * Where:
 * - h: convective heat transfer coefficient [W/(m²·K)]
 * - T_ambient: ambient fluid temperature [K]
 * - T_surface: surface temperature [K]
 *
 * ### Radiative Heat Transfer
 * q̇_radiative = εσ(T_ambient⁴ - T_surface⁴)
 * Where:
 * - ε: surface emissivity [dimensionless]
 * - σ: Stefan-Boltzmann constant [W/(m²·K⁴)]
 * - T⁴ terms introduce strong nonlinearity
 *
 * ## Implementation Details
 *
 * The method computes the normal heat flux component that enters the
 * weak form of the heat conduction equation: ∫_Γ q̇ · n · v dΓ
 *
 * For implicit schemes, linearization provides:
 * - **dFDotNdC**: ∂(q̇·n)/∂T (flux derivative w.r.t. temperature)
 * - **dFDotNdgradC**: ∂(q̇·n)/∂(∇T) (flux derivative w.r.t. temperature gradient)
 *
 * @param[in] ndm Number of spatial dimensions (2 or 3)
 * @param[in] ndf Number of degrees of freedom per node (typically 1 for temperature)
 * @param[in] dt Time step size [s] (for time-dependent heating)
 * @param[in] measInterface Interface measure (area for 3D, length for 2D) [m² or m]
 * @param[in,out] internal_new Updated internal variables (for history-dependent heating)
 * @param[in] concentration_L Left-side field values (surface temperature) [K]
 * @param[in] concentration_L0 Previous time step left-side values [K]
 * @param[in] Dconcentration_L Left-side field gradients [K/m]
 * @param[in] Dconcentration_L0 Previous time step left-side gradients [K/m]
 * @param[in] Normal Interface normal vector (outward from domain)
 * @param[in] Coord Interface coordinates [m]
 * @param[out] fDotN Normal flux component [W/m²]
 * @param[out] dFDotNdC Flux Jacobian w.r.t. field values [W/(m²·K)]
 * @param[out] dFDotNdgradC Flux Jacobian w.r.t. field gradients [W/m³]
 *
 * @pre All input arrays must be properly sized and contain valid data
 * @pre Temperature values must be positive for physical solutions
 * @pre Normal must be a unit vector
 * @pre Output arrays must be properly allocated
 *
 * @note The flux is computed in the outward normal direction
 * @note Positive flux represents heat addition to the domain
 * @note Current implementation uses prescribed heat flux only
 * @note Future versions may include convective and radiative terms
 *
 * @warning High heat flux values can cause numerical instability
 * @warning Temperature must remain positive for physical solutions
 *
 * @see Load() for parameter definitions and units
 * @see UpwindInterfaceDG::viscousBoundaryFlux() for base class interface
 */
void summit::SurfaceHeating::viscousBoundaryFlux(const int ndm, const int ndf, real dt, real measInterface, real* internal_new, const real* concentration_L, const real* concentration_L0, const real* Dconcentration_L, const real* Dconcentration_L0, const real* Normal, const real* Coord, real* fDotN, real* dFDotNdC, real* dFDotNdgradC) const
{
    fDotN[0] = _heatFlux; 
    return;
}

// Register SurfaceHeating as option for class to instantiate
REGISTER(Material, SurfaceHeating);

// end of file