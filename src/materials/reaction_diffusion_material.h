#ifndef SUMMIT_REACTION_DIFFUSION_H
#define SUMMIT_REACTION_DIFFUSION_H

#include <cstdio>
#include <string>
#include <vector>
#include <map>
#include <cstring>
#include <fstream>

// base class
#include "material.h"

#include "../utils/string-util.h"
#include "../summit_base.h"

namespace summit {

/**
 * @brief Base class for reaction-diffusion material models
 *
 * The ReactionDiffusionMaterial class provides the foundation for all reaction-diffusion
 * material models in Summit. It extends the base Material class with specific functionality
 * for coupled reaction-diffusion systems.
 *
 * Key features:
 * - Unified interface for reaction-diffusion physics
 * - Support for multiple chemical species
 * - Coupled diffusion and reaction terms
 * - Nonlinear constitutive relationships
 * - Time-dependent material properties
 *
 * Derived classes implement specific physics:
 * - Heat conduction with temperature-dependent properties
 * - Chemical species transport with reactions
 * - Multi-physics coupling (thermal-chemical-mechanical)
 * - Specialized equations (Euler, porous medium, etc.)
 *
 * The class provides virtual interfaces for:
 * - Diffusion coefficient computation
 * - Reaction source term calculation
 * - Convective flux computation
 * - Constitutive relationship evaluation
 *
 * @note This is an abstract base class - cannot be instantiated directly
 * @see Material for base material functionality
 */
class ReactionDiffusionMaterial : public Material {
  protected:
    /**
     * Typedef
     */
    typedef std::map<std::string, std::pair<int, size_t> > mapStringToPair_t;

  public:
    /**
     * Constructor
     */
    ReactionDiffusionMaterial(const std::string& name, int nInt)
      : Material(name, nInt)
    {
    }

    /**
     * Destructor
     */
    virtual ~ReactionDiffusionMaterial(){};

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    ReactionDiffusionMaterial(const ReactionDiffusionMaterial&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    ReactionDiffusionMaterial& operator=(const ReactionDiffusionMaterial&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line) = 0;

    /**
     * 
     */
    virtual int number_unknowns() const = 0;

    /**
     * Compute the bulk modulus
     */
    virtual real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future
    
    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const = 0;

    /**
     * Constitutive law, all tensors are passed in ROW MAJOR
     */
    virtual void Constitutive(const real*,
                              const real*,
                              const real*,
                              const real*,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dt,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const = 0;

    virtual void Source(const real* Tbulk_q0, const real* Tbulk_q, real* q, real* dt, real* f, real* df, size_t ndf) const = 0;

    virtual void ConvectiveFlux(const real* Tbulk_q0, const real* Tbulk_q, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const = 0;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internal = 0, const int component = 0) const = 0;



  protected:
    /**
     * Method to get the material parameters from the material file
     * @param[in] filename a string
     * @param[in] line a string
     * @param[out] vector of real
     */
    std::vector<real> GetMaterialParameters(const std::string& filename, const std::string& line);

    /**
     * Method to set the postion and the size of an internal variable in the _internal
     * ElementQuadratureField from its name
     */
    void SetLocationInInternalTable(std::string const& name, int position, size_t size);
};
}  // namespace summit

#endif  // SUMMIT_THERMAL_MODEL_H
