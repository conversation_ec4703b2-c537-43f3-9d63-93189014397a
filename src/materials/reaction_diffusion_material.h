#ifndef SUMMIT_REACTION_DIFFUSION_H
#define SUMMIT_REACTION_DIFFUSION_H

#include <cstdio>
#include <string>
#include <vector>
#include <map>
#include <cstring>
#include <fstream>

// base class
#include "material.h"

#include "../utils/string-util.h"
#include "../summit_base.h"

namespace summit {

class ReactionDiffusionMaterial : public Material {
  protected:
    /**
     * Typedef
     */
    typedef std::map<std::string, std::pair<int, size_t> > mapStringToPair_t;

  public:
    /**
     * Constructor
     */
    ReactionDiffusionMaterial(const std::string& name, int nInt)
      : Material(name, nInt)
    {
    }

    /**
     * Destructor
     */
    virtual ~ReactionDiffusionMaterial(){};

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    ReactionDiffusionMaterial(const ReactionDiffusionMaterial&);

    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    ReactionDiffusionMaterial& operator=(const ReactionDiffusionMaterial&);

  public:
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line) = 0;

    /**
     * 
     */
    virtual int number_unknowns() const = 0;

    /**
     * Compute the bulk modulus
     */
    virtual real bulkModulus(real const* internal) const;//this should be made virtual and reimplemented in future

    /**
     * @brief Base implementation of upwind stabilization (no stabilization)
     *
     * The base implementation provides no stabilization by setting the stabilization
     * term to zero. Derived classes should override this method to implement
     * appropriate upwinding schemes for their specific physics.
     *
     * This default implementation is suitable for diffusion-dominated problems
     * where numerical stabilization is not required. For advection-dominated problems,
     * derived classes should implement more sophisticated upwinding schemes.
     *
     * @param[in] concentration_L Current concentration values at the left state
     * @param[in] concentration_L0 Previous time step concentration values at the left state
     * @param[in] concentration_R Current concentration values at the right state
     * @param[in] concentration_R0 Previous time step concentration values at the right state
     * @param[in] Normal Normal vector at the interface, pointing from left to right
     * @param[out] C Stabilization terms (array of length number_unknowns())
     * @param[out] dC Derivatives of stabilization terms (for implicit schemes)
     */
    virtual void upwindStabilization(const real* concentration_L, const real* concentration_L0, const real* concentration_R, const real* concentration_R0, const real* Normal, real* C, real* dC) const;//this should be made virtual and reimplemented in 
    
    /**
     * Method to compute the diffusivity for the critical time step
     * @param[in] Fn a pointer of real
     * @param[in] q a pointer of real which is the internal variables
     * @param[in] ndm an integer which is the dimension (number of components in the strain tensor?)
     */
    virtual real diffusivity(const real* Fn, const real* q, const int ndm, const int component) const = 0;

    /**
     * Constitutive law, all tensors are passed in ROW MAJOR
     */
    virtual void Constitutive(const real*,
                              const real*,
                              const real*,
                              const real*,
                              real* P,
                              real* q,
                              real* tangent,
                              real* dPdu,
                              real dt,
                              const int ndf,
                              const int ndm,
                              bool compute_tangents = false,
                              bool artVisc_interface_activate = true) const = 0;

    virtual void Source(const real* Tbulk_q0, const real* Tbulk_q, real* q, real* dt, real* f, real* df, size_t ndf) const ;
    virtual void Source(const real* Tbulk_q0, const real* Tbulk_q, const real* DTbulk_q0, const real* DTbulk_q, real* q, real* dt, real* f, real* df, real* dfdGrad, size_t ndm, size_t ndf) const ;

    virtual void ConvectiveFlux(const real* Tbulk_q0, const real* Tbulk_q, real* q, real* dt, real* F, real* dF, size_t ndf, size_t ndm) const = 0;

    /**
     * Access the capacity
     */
    virtual real capacity(real const* internal = 0, const int component = 0) const = 0;



  protected:
    /**
     * Method to get the material parameters from the material file
     * @param[in] filename a string
     * @param[in] line a string
     * @param[out] vector of real
     */
    std::vector<real> GetMaterialParameters(const std::string& filename, const std::string& line);

    /**
     * Method to set the postion and the size of an internal variable in the _internal
     * ElementQuadratureField from its name
     */
    void SetLocationInInternalTable(std::string const& name, int position, size_t size);
};
}  // namespace summit

#endif  // SUMMIT_THERMAL_MODEL_H
