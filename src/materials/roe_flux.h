/**
 * @file roe_flux.h
 * @brief Roe flux interface material for compressible fluid dynamics
 * <AUTHOR> Development Team
 * @date 2012-2023
 *
 * This file contains the RoeFlux class which implements the Roe approximate Riemann
 * solver for interface flux calculations in compressible fluid dynamics simulations.
 * The Roe flux is a fundamental building block for discontinuous Galerkin methods
 * applied to hyperbolic conservation laws, particularly the Euler and Navier-Stokes equations.
 */

#ifndef SUMMIT_ROE_FLUX_H
#define SUMMIT_ROE_FLUX_H

// #include "interface_dg.h"
#include "upwind_interface_dg.h"
#include "../summit_enum.h"
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

// Number of internal variables:
// the value is used to size the memory allocation for the material Roe<PERSON><PERSON>
#define ROE_FLUX_NUMBER_INTERNAL_VARIABLES 0
namespace summit {

class Checkpoint;

/**
 * @brief Roe flux interface material for compressible fluid dynamics
 *
 * The RoeFlux class implements the Roe approximate Riemann solver, which is one of
 * the most widely used and robust methods for computing numerical fluxes at interfaces
 * in compressible fluid dynamics simulations. This class serves as both a standalone
 * interface material and as a base class for more specialized flux formulations.
 *
 * ## Theoretical Background
 *
 * The Roe flux method, developed by <PERSON> in 1981, provides an approximate solution
 * to the Riemann problem at interfaces between fluid elements. The key innovation is the
 * construction of a linearized Riemann solver that:
 * - Satisfies the Rankine-Hugoniot jump conditions exactly for isolated shocks
 * - Provides proper upwinding based on characteristic wave speeds
 * - Maintains conservation properties across interfaces
 * - Handles both subsonic and supersonic flow regimes
 *
 * ## Mathematical Formulation
 *
 * For the Euler equations in conservative form:
 * ∂U/∂t + ∇·F(U) = 0
 *
 * Where U = [ρ, ρu, ρv, ρw, ρE]ᵀ is the conservative variable vector and F(U) is the
 * flux tensor. The Roe flux at an interface is computed as:
 *
 * F_Roe = 1/2 * (F(U_L) + F(U_R)) - 1/2 * |A_Roe| * (U_R - U_L)
 *
 * Where:
 * - U_L, U_R are the left and right states
 * - A_Roe is the Roe-averaged Jacobian matrix
 * - |A_Roe| is the absolute value of the Jacobian (upwind dissipation)
 *
 * ## Key Features
 *
 * ### Roe Averaging
 * The method uses special Roe-averaged quantities that ensure the linearized system
 * satisfies the Rankine-Hugoniot conditions:
 * - Roe-averaged density: ρ̃ = √(ρ_L * ρ_R)
 * - Roe-averaged velocity: ũ = (√ρ_L * u_L + √ρ_R * u_R) / (√ρ_L + √ρ_R)
 * - Roe-averaged enthalpy: H̃ = (√ρ_L * H_L + √ρ_R * H_R) / (√ρ_L + √ρ_R)
 *
 * ### Wave Speed Calculation
 * Computes characteristic wave speeds (eigenvalues):
 * - λ₁ = ũ - c̃ (left-running acoustic wave)
 * - λ₂ = λ₃ = λ₄ = ũ (entropy and vorticity waves)
 * - λ₅ = ũ + c̃ (right-running acoustic wave)
 *
 * ### Entropy Fix
 * Implements entropy fixes to handle sonic points and prevent non-physical solutions:
 * - Harten's entropy fix for transonic flows
 * - Proper treatment of expansion shocks
 * - Maintains entropy condition across interfaces
 *
 * ## Applications
 *
 * This class is particularly well-suited for:
 * - **Compressible Euler equations**: Inviscid gas dynamics
 * - **Navier-Stokes equations**: Viscous compressible flows
 * - **Aerodynamics simulations**: External flow around bodies
 * - **Internal flow problems**: Nozzles, turbomachinery
 * - **Shock-dominated flows**: Supersonic and hypersonic applications
 * - **Multi-species flows**: Reactive and non-reactive gas mixtures
 *
 * ## Inheritance Hierarchy
 *
 * RoeFlux inherits from UpwindInterfaceDG, which provides:
 * - Base upwind flux functionality
 * - Interface material framework
 * - Stability parameter management
 * - Integration with Summit's DG infrastructure
 *
 * ## Implementation Details
 *
 * ### Memory Management
 * - Uses ROE_FLUX_NUMBER_INTERNAL_VARIABLES (0) internal variables
 * - Efficient memory allocation for flux calculations
 * - Minimal storage requirements for stateless flux computation
 *
 * ### Numerical Robustness
 * - Handles vacuum states and near-vacuum conditions
 * - Prevents division by zero in density calculations
 * - Maintains positivity of density and pressure
 * - Proper treatment of stagnation conditions
 *
 * ### Performance Optimization
 * - Vectorized flux calculations where possible
 * - Efficient eigenvalue and eigenvector computations
 * - Minimal branching in critical computational loops
 * - Cache-friendly memory access patterns
 *
 * @note This class serves as a base for specialized flux formulations like HLLC, AUSM+, etc.
 * @note The implementation includes various entropy fixes and robustness enhancements
 * @note Supports both 2D and 3D flow configurations
 * @note Can be extended for multi-species and reactive flow applications
 *
 * @warning Requires proper initialization of thermodynamic properties
 * @warning May require entropy fixes for transonic flow applications
 * @warning Performance-critical code - modifications should be carefully tested
 *
 * @see UpwindInterfaceDG for base interface functionality
 * @see EulerEquations for the governing equations implementation
 * @see LaxStabilizer for alternative flux stabilization methods
 */
class RoeFlux : public UpwindInterfaceDG {
  public:
    /**
     * @brief Default constructor for RoeFlux interface material
     *
     * Creates a RoeFlux object with default parameters. This constructor initializes
     * the Roe flux interface material with standard settings suitable for basic
     * compressible flow applications.
     *
     * @warning This constructor should not be used when deriving from RoeFlux
     * @note Prefer the named constructor for better code clarity
     * @see RoeFlux(const std::string&) for named construction
     */
    RoeFlux();

    /**
     * @brief Named constructor for RoeFlux interface material
     *
     * Creates a RoeFlux object with a specified name identifier. This constructor
     * is suitable for standalone Roe flux applications where no derived classes
     * are involved.
     *
     * @param[in] name Descriptive name for the material (e.g., "AirRoeFlux", "CombustionRoe")
     *
     * @pre name must be a non-empty string
     * @warning This constructor should not be used when deriving from RoeFlux
     * @note For derived classes, use the constructor with internal variable count
     * @see RoeFlux(const std::string&, const int) for derived class construction
     */
    RoeFlux(const std::string& name);

    /**
     * @brief Constructor for derived classes with custom internal variables
     *
     * Creates a RoeFlux object with a specified name and number of internal variables.
     * This constructor should be used when deriving from RoeFlux to create specialized
     * flux formulations that require additional state variables.
     *
     * @param[in] name Descriptive name for the derived material
     * @param[in] nInt Number of internal variables required by the derived class
     *
     * @pre name must be a non-empty string
     * @pre nInt must be non-negative
     *
     * @note This is the preferred constructor for derived classes
     * @note Internal variables can store additional state for complex flux formulations
     * @see ROE_FLUX_NUMBER_INTERNAL_VARIABLES for base class internal variable count
     */
    RoeFlux(const std::string& name, const int nInt);

    /**
     * @brief Full constructor with material coupling and stability parameters
     *
     * Creates a RoeFlux object with complete specification of interface coupling
     * parameters. This constructor is used for complex multi-material interfaces
     * where specific material labels and stability parameters are required.
     *
     * @param[in] name Descriptive name for the interface material
     * @param[in] leftMatLabel Material label for the left-side bulk material (1-based index)
     * @param[in] rightMatLabel Material label for the right-side bulk material (1-based index)
     * @param[in] beta Stability parameter for interface flux stabilization [dimensionless]
     * @param[in] numberComponents Number of solution components (e.g., 5 for 3D Euler equations)
     *
     * @pre name must be a non-empty string
     * @pre leftMatLabel and rightMatLabel must be valid material indices (≥ 1)
     * @pre beta must be non-negative (typically 0.0 to 2.0)
     * @pre numberComponents must be positive (typically 4 for 2D, 5 for 3D Euler)
     *
     * @note The stability parameter beta controls numerical dissipation:
     *       - beta = 0.0: Pure Roe flux (minimal dissipation)
     *       - beta = 1.0: Standard stabilization
     *       - beta > 1.0: Enhanced stability for difficult cases
     * @note Material labels must correspond to valid entries in the material library
     * @note Number of components should match the governing equation system
     */
    RoeFlux(const std::string& name,
               const int leftMatLabel,
               const int rightMatLabel,
               const real beta,
               const int numberComponents,
               const int nInt = ROE_FLUX_NUMBER_INTERNAL_VARIABLES);

    virtual MaterialType GetMaterialType() const { return MATERIAL_RoeFlux; }

    /**
     * Constructor
     * @param[in] checkpoint object associated with restart
     * @param[in] name the name of the group within the checkpoint object
     */
    RoeFlux(Checkpoint* checkpoint, const char* name);

    /**
     * Destructor
     */
    virtual ~RoeFlux();

    /**
     * Method to display the material parameters to the output chanel
     */
    void Display() override;

    /**
     * Method to know if the interface law is for boundary conditions
     * @return true if this model is for external surfaces
     */
    real getGamma() const { return _gamma; }

    /**
     * @brief Implementation of upwind stabilization (Roe flux) for gas dynamics
     *
     *
     * @param[in] concentration_L Current concentration values at the left state
     * @param[in] concentration_L0 Previous time step concentration values at the left state
     * @param[in] concentration_R Current concentration values at the right state
     * @param[in] concentration_R0 Previous time step concentration values at the right state
     * @param[in] Normal Normal vector at the interface, pointing from left to right
     * @param[out] C Stabilization terms (array of length number_unknowns())
     * @param[out] dC Derivatives of stabilization terms (for implicit schemes)
     */
    virtual void upwindStabilization(const real* concentration_L, const real* concentration_L0, const real* concentration_R, const real* concentration_R0, const real* Normal, real* C, real* dC) const;//this should be made virtual and reimplemented in 

    /**
     * @brief returns the DG parameter for a single component of a multi-component problem
     * 
     * @param component which component of the problem are we talking about 
     * @return real 
     */
    void getInterfaceDamage(const real* internalVariables, real* myDamage) const;

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    RoeFlux(const RoeFlux&);

    static Register<Material, RoeFlux> reg;
    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    RoeFlux& operator=(const RoeFlux&);

    real _gamma = 1.4;

  public:  
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

  protected:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void SetInternalVariableMap();
};
}  // namespace summit

#endif

// end of file
