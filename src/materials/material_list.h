/**
 * @file material_list.h
 * <AUTHOR> (<EMAIL>), 2023
 * 
 * This file contains the list of all material models in Summit, defined using the preprocessor macro 
 * MATERIAL_MODEL, which is used to define enums, switch statements, and include statements throughout
 * summit where necessary.
 * 
 * **List of header files:**
 * \snippet{lineno} this headers
 * 
 * **List of class names and IDs:**
 * \snippet{lineno} this materials
 * 
 * @def MATERIAL_MODEL(name, id)
 * @brief A preprocessor macro that is used to define a material model for class \a name with ID \a id.
 */

#ifdef _DOXYGEN_ // Doxygen only
    #define MATERIAL_MODEL(name, id)
#endif

/**
 * Include statements for all material models go here.
 * Paths are relative to the src/materials directory
 */
#ifdef MATERIAL_LIST_H
//! [headers]
    #include "neohookean/neohookean.h"
    #include "vumat/neohookean/vumat_neohookean.h"
    #include "neohookean/neohookean_compressible.h"
    #include "camclay/camclay.h"
    #include "uhmwp/bergstrom_hybrid.h"
    #include "ceramic/ceramic.h"
    #include "seme/seme.h"
    #include "sevf/sevf.h"
    #include "phaseTrans/phaseTrans.h"
    #include "parametric_poroelastic/parametric_pore_pressure_poroelastic.h"
    #include "parametric_poroelastic/parametric_poroelastic.h"
    #include "parametric_elastic/parametric_elastic.h"
    #include "elastic/elastic.h"
    #include "Prony_viscoelastic/Prony_viscoelastic.h"
    #include "camclay/camclay_power_law.h"
    #include "J2_plastic/J2_plastic_pow.h"
    #include "J2_plastic/J2_plastic_pow_deletion.h"
    #include "J2_plastic/J2_plastic_jc.h"
    #include "J2_plastic/J2_plastic_jc_damage.h"
    #include "bcc_cuitino/bcc_cuitino.h"
    #include "polyconvex/polyconvex.h"
    #include "polyconvex_orthotropic/polyconvex_orthotropic.h"
    #include "sma/SMA_elastic.h"
    #include "vumat/brain-tissue-MIT/vumat_brain_tissue.h"
    #include "parametric_sma/parametric_sma.h"
    #include "J2_linear/J2_linear.h"
    #include "stokeslet_flow.h"
    #include "gent-hyperelastic/gent-hyperelastic.h"
    #include "gent-compressible/gent_compressible.h"
    #include "elastic/elastic_eos_Clifton.h"
    #include "elastic/elastic_eos_Tait.h"
    #include "elastic/elastic_eos_MG.h"
    #include "vumat/polyurea-MIT/vumat_polyurea.h"
    #include "elastic/elastic_eos_Glass.h"
    #include "elastic/elastic_eos_Murnaghan.h"
    #include "elastic/elastic_eos_BM.h"
    #include "inelastic_eos/inelastic_eos_simple.h"
    #include "inelastic_eos/inelastic_eos_glass.h"
    #include "inelastic_eos/inelastic_eos_glass_coupledshear.h"
    #include "shell/shell_elastic.h"
    #include "vumat/cortical/homogeneous_vumat_cortical.h"
    #include "shell/shell_plastic.h"
    #include "vumat/cortical/homogeneous_vumat_cortical_aniso.h"
    #include "vumat/cortical/homogeneous_vumat_cortical_damage.h"
    #include "shell/nl_shell_neoHookean.h"
    #include "shell/nl_shell_fabric.h"
    #include "beam/beam_KL_TF_elastic.h"
    #include "gel_ISN/gel_ISN.h"
    #include "beam/contact_beam_with_friction.h"
    #include "hyperelasticity/ogden_hyperfoam.h"
    #include "granular-compaction/granular-compaction-smallDef.h"
    #include "granular-compaction/granular-compaction-largeDef.h"
    #include "glass-Densification/glass_camclay.h"
    #include "vumat/ice-crrel/umat_ice.h"
    #include "smc/SMC.h"
    #include "interface_dg.h"
    #include "cohesiveDG/cohesive_dg.h"
    #include "cohesiveDG/variational_czm.h"
    #include "shell_interface.h"
    #include "cohesiveDG/cohesive_dg_parametric_tsl.h"
    #include "cohesiveDG/cohesive_dg_uniform_pressure.h"
    #include "cohesiveDG/cohesive_dg_fluid.h"
    #include "cohesiveDG/cohesive_dg_fluid_poroelastic.h"
    #include "cohesiveDG/cohesive_dg_fluid_parametric.h"
    #include "cohesiveDG/cohesive_dg_MC.h"
    #include "cohesiveDG/cohesive_dg_fluid_poroelastic_parametric.h"
    #include "cohesiveDG/cohesive_dg_ductile.h"
    #include "vumat/cortical/parametric_vumat_cortical.h"
    #include "vumat/cortical/parametric_vumat_cortical_damage.h"
    #include "vumat/cortical/parametric_vumat_cortical_aniso.h"
    #include "vumat/cortical/parametric_vumat_cortical_aniso_damage.h"
    #include "cohesiveDG/cohesive_dg_parametric_MC.h"
    #include "cohesiveDG/cohesive_dg_parametric.h"
    #include "contact_interface_dg.h"
    #include "peridynamic/peridynamic_elastic.h"
    #include "peridynamic/peridynamic_j2_linear.h"
    #include "peridynamic/peridynamic_iso.h"
    #include "peridynamic/peridynamic_elastic_modeI.h"
    #include "peridynamic/peridynamic_iso_modeI.h"
    #include "upwind_interface_dg.h"
    #include "permeable_flow/permeable_flow.h"
    #include "channel_flow/channel_flow.h"
    #include "channel_flow/parametric_power_law_flow.h"
    #include "channel_flow/parametric_channel_flow.h"
    #include "porous_medium_flow/isotropic_porous_medium_flow.h"
    #include "porous_medium_flow/parametric_isotropic_crack_medium_flow.h"
    #include "porous_medium_flow/isotropic_crack_medium_flow.h"
    #include "channel_flow/slurry_flow.h"
    #include "channel_flow/parametric_slurry_flow.h"
    #include "channel_flow/slurry_flow_temperature_parametric.h"
    #include "advective_flow/advective_flow.h"
    #include "advective_flow/channel_advective_flow.h"
    #include "thermal_model/channel_thermal_model.h"
    #include "porous_medium_flow/isotropic_porous_medium_gas_flow.h"
    #include "porous_medium_flow/parametric_isotropic_crack_medium_gas_flow.h"
    #include "porous_medium_flow/isotropic_crack_medium_gas_flow.h"
    #include "damped_elastic/damped_elastic.h"
    #include "parametrically_damped_elastic/parametrically_damped_elastic.h"
    #include "damped_elastic/parametric_damped_elastic.h"
    #include "black_oil_model/isotropic_porous_medium_black_oil_model.h"
    #include "black_oil_model/isotropic_crack_medium_black_oil_model.h"
    #include "black_oil_model/parametric_isotropic_porous_medium_black_oil_model.h"
    #include "thermal_model/isotropic_bulk_medium_thermal_model.h"
    #include "thermal_model/isotropic_crack_medium_thermal_model.h"
    #include "thermal_model/parametric_isotropic_crack_medium_thermal_model.h"
    #include "elastic/elastic_mass_scale.h"
    #include "reaction_diffusion/trick.h"
    #include "shell/plate_interface.h"
    #include "shell/shell_cohesive_exponential.h"
    #include "shell/shell_cohesive_linear.h"
    #include "reaction_diffusion/bessire_source.h"
    #include "elastic/elastic_thermal.h"
    #include "multiphysics/ulm_concrete/ulm_concrete.h"
    #include "elastic/poro_elastic_thermal.h"
    #include "neohookean/neohookean_thermal.h"
    #include "multiphysics/pilling_bedworth_swelling_heat_conducting_eyring_creep/PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion.h"
    #include "multiphysics/pilling_bedworth_swelling_heat_conducting_eyring_creep/PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic.h"
    #include "multiphysics/pyrolyzing_poroelastic_fracture/pyrolyzing_poroelastic_fracture_mechanics.h"
    #include "multiphysics/pyrolyzing_poroelastic_fracture/pyrolyzing_poroelastic_fracture_reaction_diffusion.h"
    #include "reaction_diffusion/nonlinear_conduction.h"
    #include "elastic/elastic_eos_cavitation.h"
    #include "reaction_diffusion/variational_conduction.h"
    #include "multiphysics/armero_simo/ArmeroSimoThermal.h"
    #include "reaction_diffusion/SquaredPressure.h"
    #include "reaction_diffusion/lin_conv_diff.h"
    #include "reaction_diffusion/porous_medium_equation.h"
    #include "reaction_diffusion/electro_elastic.h"
    #include "neohookean/neohookean_electric.h"
    #include "reaction_diffusion/linear_piezo.h"
    #include "elastic/anisotropic_electric.h"
    #include "multiphysics/pilling_bedworth_swelling_heat_conducting_eyring_creep/PillingBedworthSwellingHeatConductingEyringCreep.h"
    #include "multiphysics/armero_simo/ArmeroSimo.h"
    #include "reaction_diffusion/TACOT.h"
    #include "reaction_diffusion/TorresHerrador.h"
    #include "elastic/pica.h"
    #include "reaction_diffusion/General_Pyrolysis.h"
    #include "reaction_diffusion/Nonlinear_Heat_Conduction_Table.h"
    #include "reaction_diffusion/FIAT_Pyrolysis.h"
    #include "reaction_diffusion/nonlocal_damage.h"
    #include "multiphysics/linear_thermo_poro_elasticity/linear-thermo-poro-elasticity.h"
    #include "shell/shell_elasticRM.h"
    #include "shell/shell_plasticRM.h"
    #include "reaction_diffusion/multispecies_diffusion.h"
    #include "reaction_diffusion/multispecies_dissolved.h"
    #include "reaction_diffusion/euler_equations.h"
    #include "elastic/thermochemoelastic.h"
//! [headers]
#endif

/**
 * Preprocessor macros that define material models in Summit go here
 */
#ifdef MATERIAL_MODEL
//! [materials]
    MATERIAL_MODEL(Neohookean, 0)
    MATERIAL_MODEL(VumatNeohookean, 1)
    MATERIAL_MODEL(NeohookeanCompressible, 2)
    MATERIAL_MODEL(Camclay, 3)
    MATERIAL_MODEL(BergstromHybrid, 4)
    MATERIAL_MODEL(Ceramic, 5)
    MATERIAL_MODEL(Seme, 6)
    MATERIAL_MODEL(Sevf, 7)
    MATERIAL_MODEL(PhaseTrans, 8)
    MATERIAL_MODEL(ParametricPorePressurePoroelastic, 9)
    MATERIAL_MODEL(ParametricPoroelastic, 11)
    MATERIAL_MODEL(ParametricElastic, 12)
    MATERIAL_MODEL(Elastic, 13)
    MATERIAL_MODEL(PronyViscoElastic, 14)
    MATERIAL_MODEL(Camclay_Power_Law, 15)
    MATERIAL_MODEL(J2PlasticPow, 16)
    MATERIAL_MODEL(J2PlasticPowDel, 17)
    MATERIAL_MODEL(J2PlasticJC, 19)
    MATERIAL_MODEL(J2PlasticJCDamage, 20)
    MATERIAL_MODEL(BCCCuitino, 21)
    MATERIAL_MODEL(Polyconvex, 23)
    MATERIAL_MODEL(PolyconvexOrthotropic, 24)
    MATERIAL_MODEL(SMA_elastic, 26)
    MATERIAL_MODEL(VumatBrainTissue, 28)
    MATERIAL_MODEL(ParametricSMA, 31)
    MATERIAL_MODEL(J2Linear, 34)
    MATERIAL_MODEL(StokesletFlow, 35)
    MATERIAL_MODEL(GentHyperelastic, 37)
    MATERIAL_MODEL(GentCompressibleHyperelastic, 38)
    MATERIAL_MODEL(ElasticEOSClifton, 40)
    MATERIAL_MODEL(ElasticEOSTait, 41)
    MATERIAL_MODEL(ElasticEOSMG, 42)
    MATERIAL_MODEL(VumatPolyurea, 43)
    MATERIAL_MODEL(ElasticEOSGlass, 44)
    MATERIAL_MODEL(ElasticEOSMurnaghan, 45)
    MATERIAL_MODEL(ElasticEOSBM, 46)
    MATERIAL_MODEL(InelasticEOSSimple, 47)
    MATERIAL_MODEL(InelasticEOSGlass, 48)
    MATERIAL_MODEL(InelasticEOSGlassCoupledShear, 49)
    MATERIAL_MODEL(ShellElastic, 50)
    MATERIAL_MODEL(HomogeneousVumatCortical, 51)
    MATERIAL_MODEL(ShellPlastic, 52)
    MATERIAL_MODEL(HomogeneousVumatCorticalAniso, 53)
    MATERIAL_MODEL(HomogeneousVumatCorticalDamage, 54)
    MATERIAL_MODEL(ShellNeoHookean, 55)
    MATERIAL_MODEL(ShellFabricMaterial, 56)
    MATERIAL_MODEL(BeamKLTorsionFreeElastic, 57)
    MATERIAL_MODEL(GelISN, 60)
    MATERIAL_MODEL(ContactBeamWithFriction, 61)
    MATERIAL_MODEL(OgdenHyperFoam, 71)
    MATERIAL_MODEL(GranularCompactionSmallDef, 72)
    MATERIAL_MODEL(GranularCompactionLargeDef, 73)
    MATERIAL_MODEL(GlassCamclay, 75)
    MATERIAL_MODEL(UmatIce, 82)
    MATERIAL_MODEL(SMC, 88)
    MATERIAL_MODEL(InterfaceDG, 100)
    MATERIAL_MODEL(CohesiveDG, 101)
    MATERIAL_MODEL(ShellInterfaceMaterial, 102)
    MATERIAL_MODEL(CohesiveDGParametricTSL, 103)
    MATERIAL_MODEL(CohesiveDGUniformPressure, 104)
    MATERIAL_MODEL(CohesiveDGFluid, 105)
    MATERIAL_MODEL(CohesiveDGFluidPoroelastic, 107)
    MATERIAL_MODEL(CohesiveDGFluidParametric, 110)
    MATERIAL_MODEL(CohesiveDG_MC, 111)
    MATERIAL_MODEL(CohesiveDGFluidPoroelasticParametric, 112)
    MATERIAL_MODEL(CohesiveDG_Ductile, 113)
    MATERIAL_MODEL(VariationalCZM, 1044)
    MATERIAL_MODEL(ParametricVumatCortical, 114)
    MATERIAL_MODEL(ParametricVumatCorticalDamage, 115)
    MATERIAL_MODEL(ParametricVumatCorticalAniso, 116)
    MATERIAL_MODEL(ParametricVumatCorticalAnisoDamage, 117)
    MATERIAL_MODEL(ParametricCohesiveDG_MC, 120)
    MATERIAL_MODEL(ParametricCohesiveDG, 130)
    MATERIAL_MODEL(ContactInterfaceDG, 150)
    MATERIAL_MODEL(PeridynamicElastic, 200)
    MATERIAL_MODEL(PeridynamicJ2Linear, 201)
    MATERIAL_MODEL(PeridynamicIso, 202)
    MATERIAL_MODEL(PeridynamicElasticModeI, 203)
    MATERIAL_MODEL(PeridynamicIsoModeI, 204)
    MATERIAL_MODEL(UpwindInterfaceDG, 247)
    MATERIAL_MODEL(PermeableFlow, 401)
    MATERIAL_MODEL(ChannelFlow, 402)
    MATERIAL_MODEL(ParametricPowerLawFlow, 403)
    MATERIAL_MODEL(ParametricChannelFlow, 404)
    MATERIAL_MODEL(IsotropicPorousMediumFlow, 405)
    MATERIAL_MODEL(ParametricIsotropicCrackMediumFlow, 406)
    MATERIAL_MODEL(IsotropicCrackMediumFlow, 407)
    MATERIAL_MODEL(SlurryFlow, 412)
    MATERIAL_MODEL(ParametricSlurryFlow, 413)
    MATERIAL_MODEL(SlurryFlowTemperatureParametric, 414)
    MATERIAL_MODEL(AdvectiveFlow, 420)
    MATERIAL_MODEL(ChannelAdvectiveFlow, 421)
    MATERIAL_MODEL(ChannelThermalModel, 422)
    MATERIAL_MODEL(IsotropicPorousMediumIdealGasFlow, 505)
    MATERIAL_MODEL(ParametricIsotropicCrackMediumIdealGasFlow, 506)
    MATERIAL_MODEL(IsotropicCrackMediumRealGasFlow, 515)
    MATERIAL_MODEL(IsotropicCrackMediumIdealGasFlow, 507)
    MATERIAL_MODEL(ParametricIsotropicCrackMediumRealGasFlow, 516)
    MATERIAL_MODEL(DampedElastic, 666)
    MATERIAL_MODEL(ParametricallyDampedElastic, 667)
    MATERIAL_MODEL(ParametricDampedElastic, 668)
    MATERIAL_MODEL(IsotropicPorousMediumBlackOilModel, 701)
    MATERIAL_MODEL(IsotropicCrackMediumBlackOilModel, 702)
    MATERIAL_MODEL(ParametricIsotropicPorousMediumBlackOilModel, 703)
    MATERIAL_MODEL(IsotropicBulkMediumThermalModel, 801)
    MATERIAL_MODEL(IsotropicCrackMediumThermalModel, 802)
    MATERIAL_MODEL(ParametricIsotropicCrackMediumThermalModel, 803)
    MATERIAL_MODEL(ElasticMassScale, 1000)
    MATERIAL_MODEL(Trick, 1002)
    MATERIAL_MODEL(PlateInterfaceMaterial, 1004)
    MATERIAL_MODEL(ShellCohesiveExponential, 1005)
    MATERIAL_MODEL(ShellCohesiveLinear, 1006)
    MATERIAL_MODEL(BessireSource, 1007)
    MATERIAL_MODEL(ElasticThermal, 1011)
    MATERIAL_MODEL(UlmConcrete, 1015)
    MATERIAL_MODEL(NeohookeanThermal, 1012)
    MATERIAL_MODEL(PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion, 1013)
    MATERIAL_MODEL(PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic, 1014)
    MATERIAL_MODEL(NonlinearConduction, 1023)
    MATERIAL_MODEL(ElasticEOSCavitation, 1024)
    MATERIAL_MODEL(VariationalConduction, 1026)
    MATERIAL_MODEL(LinearConvectionDiffusion, 1027)
    MATERIAL_MODEL(PorousMediumEquation, 1028)
    MATERIAL_MODEL(ElectroElastic, 1029)
    MATERIAL_MODEL(NeohookeanElectric, 1030)
    MATERIAL_MODEL(LinearPiezo, 1031)
    MATERIAL_MODEL(AnisotropicElectric, 1032)
    MATERIAL_MODEL(PillingBedworthSwellingHeatConductingEyringCreep, 1033)
    MATERIAL_MODEL(TACOT, 1034)
    MATERIAL_MODEL(TorresHerrador, 1035)
    MATERIAL_MODEL(PICA, 1038)
    MATERIAL_MODEL(GeneralPyrolysis, 1040)
    MATERIAL_MODEL(NonlinearHeatConductionTable, 1041)
    MATERIAL_MODEL(FIATPyrolysis, 1042)
    MATERIAL_MODEL(NonlocalDamage, 1025)
    MATERIAL_MODEL(ShellElasticRM, 5000)
    MATERIAL_MODEL(ShellPlasticRM, 5002)
    MATERIAL_MODEL(MultispeciesDiffusion, 1102)
    MATERIAL_MODEL(MultispeciesDissolved, 1103)
    MATERIAL_MODEL(LinearThermoPoroElasticity, 1105)
    MATERIAL_MODEL(EulerEquations, 1046)
    MATERIAL_MODEL(ArmeroSimo, 1039)
    MATERIAL_MODEL(ArmeroSimoThermal, 1043)
    MATERIAL_MODEL(SquaredPressure, 1045)
    MATERIAL_MODEL(PoroElasticThermal, 1047)
    MATERIAL_MODEL(ThermoChemoElastic, 1104)
    MATERIAL_MODEL(PyrolyzingPoroElasticFractureMechanics, 1036)
    MATERIAL_MODEL(PyrolyzingPoroElasticFractureReactionDiffusion, 1037)
//! [materials]
#endif

// End of file
