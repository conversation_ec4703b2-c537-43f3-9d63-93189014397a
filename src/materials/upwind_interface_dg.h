#ifndef SUMMIT_UPWIND_INTERFACE_DG_H
#define SUMMIT_UPWIND_INTERFACE_DG_H

#include "interface_dg.h"
#include "../summit_enum.h"
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

// Number of internal variables:
// the value is used to size the memory allocation for the material UpwindInterfaceDG
#define UPWIND_INTERFACE_DG_NUMBER_INTERNAL_VARIABLES 1
namespace summit {

class Checkpoint;

/**
 * @brief Upwind interface material for discontinuous Galerkin methods
 *
 * The UpwindInterfaceDG class extends InterfaceDG with upwind stabilization
 * capabilities for hyperbolic and convection-dominated problems. It provides
 * the foundation for flux-based interface treatments in DG formulations.
 *
 * Key features:
 * - Upwind flux computation for stability
 * - Characteristic-based wave speed calculation
 * - Support for various flux formulations (Roe, HLLC, Lax-Friedrichs)
 * - Boundary condition enforcement through interface fluxes
 * - Multi-physics coupling capabilities
 *
 * Applications:
 * - Compressible fluid dynamics (Euler/Navier-Stokes)
 * - Scalar transport equations
 * - Reaction-diffusion systems
 * - Thermal boundary conditions
 * - Multi-material interfaces
 *
 * The class serves as a base for specialized flux formulations and
 * provides virtual interfaces for upwind stabilization computation.
 *
 * @note Uses 1 internal variable for flux state tracking
 * @see InterfaceDG for base interface functionality
 */
class UpwindInterfaceDG : public InterfaceDG {
  public:
    /**
     * Constructor (default)
     * This method should not be used when deriving from UpwindInterfaceDG
     */
    UpwindInterfaceDG();

    /**
     * Constructor
     * This method should not be used when deriving from UpwindInterfaceDG
     * @param[in] name a string that defines the name of the material
     */
    UpwindInterfaceDG(const std::string& name);

    /**
     * Constructor
     * This method should be used when deriving from UpwindInterfaceDG
     * @param[in] name a string that defines the name of the material
     * @param[in] nInt number of internal variables
     */
    UpwindInterfaceDG(const std::string& name, const int nInt);

    /**
     * Constructor
     * @param[in] name a string that defines the name of the material
     * @param[in] leftMatLabel Left material Label
     * @param[in] rightMatLabel Right material Label
     * @param[in] beta stability parameter
     * @param[in] numberComponents Type of criterion that must be satisfied at
     *            quadrature points to initiate the fracture
     */
    UpwindInterfaceDG(const std::string& name,
               const int leftMatLabel,
               const int rightMatLabel,
               const real beta,
               const int numberComponents,
               const int nInt = UPWIND_INTERFACE_DG_NUMBER_INTERNAL_VARIABLES);

    virtual MaterialType GetMaterialType() const { return MATERIAL_UpwindInterfaceDG; }

    /**
     * Constructor
     * @param[in] checkpoint object associated with restart
     * @param[in] name the name of the group within the checkpoint object
     */
    UpwindInterfaceDG(Checkpoint* checkpoint, const char* name);

    /**
     * Destructor
     */
    virtual ~UpwindInterfaceDG();
    /**
     * Method to display the material parameters to the output chanel
     */
    virtual void Display();

  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    UpwindInterfaceDG(const UpwindInterfaceDG&);

    static Register<Material, UpwindInterfaceDG> reg;
    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    UpwindInterfaceDG& operator=(const UpwindInterfaceDG&);

  public:  

    /**
     * @brief returns the DG parameter for a single component of a multi-component problem
     * 
     * @param component which component of the problem are we talking about 
     * @return real 
     */
    void getBetasForDGStability(real* myBetas) const;

    /**
     * @brief Base implementation of upwind stabilization (no stabilization)
     *
     * The base implementation provides no stabilization by setting the stabilization
     * term to zero. Derived classes should override this method to implement
     * appropriate upwinding schemes for their specific physics.
     *
     * This default implementation is suitable for diffusion-dominated problems
     * where numerical stabilization is not required. For advection-dominated problems,
     * derived classes should implement more sophisticated upwinding schemes.
     *
     * @param[in] concentration_L Current concentration values at the left state
     * @param[in] concentration_L0 Previous time step concentration values at the left state
     * @param[in] concentration_R Current concentration values at the right state
     * @param[in] concentration_R0 Previous time step concentration values at the right state
     * @param[in] Normal Normal vector at the interface, pointing from left to right
     * @param[out] C Stabilization terms (array of length number_unknowns())
     * @param[out] dC Derivatives of stabilization terms (for implicit schemes)
     */
    virtual void upwindStabilization(const real* concentration_L, const real* concentration_L0, const real* concentration_R, const real* concentration_R0, const real* Normal, real* C, real* dC) const;
    
    /**
     * @brief Base implementation of viscous boundary flux
     *
     * The base implementation provides the capability to do surface integrals
     * this function can depend on the gradient
     * TODO improve documentation //(up,qp,np,ib,ui,p,param,time)
     */
    virtual void viscousBoundaryFlux(const int ndm, const int ndf, real dt, real measInterface, real* internal_new, const real* concentration_L, const real* concentration_L0, const real* Dconcentration_L, const real* Dconcentration_L0, const real* Normal, const real* Coord, real* fDotN, real* dFDotNdC, real* dFDotNdgradC) const;

    /**
     * @brief Base implementation of inviscid boundary flux
     *
     * The base implementation provides the capability to do surface integrals
     * this function cannot depend on the gradient
     * TODO improve documentation //(up,qp,np,ib,ui,p,param,time)
     */
    virtual void inviscidBoundaryFlux(const int ndm, const int ndf, real dt, real measInterface, real* internal_new, const real* concentration_L, const real* concentration_L0, const real* Normal, const real* Coord, real* fDotN, real* dFDotNdC) const;

    /**
     * @brief Base implementation of inviscid boundary flux
     *
     * The base implementation provides the capability to do surface integrals
     * this function cannot depend on the gradient
     * TODO improve documentation //(up,qp,np,ib,ui,p,param,time)
     */
    virtual void surfaceReactions(const int ndm, const int ndf, real dt, real measInterface, real* internal_new, const real* concentration_L, const real* concentration_L0, const real* Normal, const real* Coord, real* S, real* dSdC) const;

    /**
     * @brief returns the DG parameter for a single component of a multi-component problem
     * 
     * @param component which component of the problem are we talking about 
     * @return real 
     */
    virtual void getInterfaceDamage(const real* internalVariables, real* myDamage) const;

    /**
     * @brief returns the DG parameter for a single component of a multi-component problem
     * 
     * @param component which component of the problem are we talking about 
     * @return real 
     */
    virtual void getInterfaceTypes(const real* internalVariables, int* myTypes) const;

    /**
     * @brief returns the DG parameter for a single component of a multi-component problem
     * 
     * @param component which component of the problem are we talking about 
     * @return real 
     */
    virtual void getInterfaceValues(const real* internalVariables, real* myValues) const;

    /**
     * @param[in] internalVariables internal variables
     * @return the "interface damage", i.e., delta_max/delta_c
     */
    real getConductance(const real* internalVariables) const;

    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);

    #ifdef WITH_YAML_CPP
    /**
     * Method to load material properties from a YAML node
     * @param[in] node the YAML node for this specific material
     */
    virtual void Load(const YAML::Node &node);
    #endif

    /**
     * @brief Method to update the history variables of the cohesive law (if any)
     *  This method is called by the region after computing the constitutive update
     *   for a converged configuration
     *
     * @param internalVariables the material internal variables
     */
    void Update(real* internalVariables) const override;

    /**
     * Unpack the information of the buffer for communication in parallel
     * @param[in] ndm spatial dimension of the computational domain
     * @param[out] numberTimesFailureCriterionIsSatisfied number of times that failure criterion
     *             is satisfied. The three possible values are:
     *             - 0:  The criterion is not satisfied neither by the left element nor the right
     *                   element
     *             - 1:  The criterion is satisfied either by the left element or the right element
     *             - 2:  The criterion is satisfied by left and right elements
     * @param[out] tractionLeft traction vector for the left element
     * @param[out] normalDeformedLeft normal vector to the left element in the deformed
     *             configuration
     * @param[in] interfaceMaterial_bufferForComm buffer for communication
     */
    void UnpackBufferForCommunication(const int ndm,
                                      int* numberTimesFailureCriterionIsSatisfied,
                                      real* tractionLeft,
                                      real* normalDeformedLeft,
                                      const real* interfaceMaterial_bufferForComm) const;

    /**
     * @return the size of the buffer to store the information needed to evaluate the fracture
     *         criterion
     */
    virtual int materialBufferSizeForComm(const int dim) const;

    /**
     * Method to write for restart
     * @param[in] checkpoint object associated with restart
     * @param[in] name the name of the group within the checkpoint object
     * @param[in] tag the tag
     */
    virtual void WriteForRestart(Checkpoint* checkpoint, const char* name, const char* tag) const;

  protected:

    int _numberComponents;
    /**
     * DG stab Factors
     */
    std::vector<real> _betas;

    /**
     * kind of interface failure
     * 0 nothing, 1 neumann, 2 dirichlet
     */
    std::vector<int> _interface_types;

    /**
     * kind of interface failure
     * value of interface condition (neumann or dirichlet)
     */
    std::vector<int> _interface_values;

    /**
     * Type of criterion that must be satisfied at quadrature (evaluation) points to initiate the
     * fracture
     * There are three possible values:
     * - 0: All the points must satisfy the failure criterion (for both left and right elements)
     *      The fracture is initiated at the same time for all the points
     * - 1: At least half of the points must satisfy the failure criterion (for both left and right
     *      elements)
     *      The fracture is initiated at the same time for all the points
     * - 2: The fracture is initiated only at the points that satisfy the failure criterion (for
     *      both left and right elements)
     *      The fracture initiation does not depend on the number of points that satisfied the
     *      failure criterion
     * - 3: At least one of the points must satisfy the failure criterion (for both left and right
     *      elements)
     *      The fracture is initiated at the same time for all the points
     */
    int _typeCriterionFractureInitiation;

  protected:
    /**
     * Method to fill the map from internal variable name to location in the internal variable
     * table
     */
    void SetInternalVariableMap();

    /**
     * Pack the information of the buffer for communication in parallel
     * @param[in] ndm spatial dimension of the computational domain
     * @param[in] numberTimesFailureCriterionIsSatisfied number of times that failure criterion is
     *            satisfied
     *            The three possible values are:
     *            - 0:  The criterion is not satisfied neither by the left element nor the right
     *                  element
     *            - 1:  The criterion is satisfied either by the left element or the right element
     *            - 2:  The criterion is satisfied by left and right elements
     * @param[in] tractionLeft traction vector for the left element
     * @param[in] normalDeformedLeft normal vector to the left element in the deformed configuration
     * @param[out] interfaceMaterial_bufferForComm buffer for communication
     */
    void packBufferForCommunication(const int ndm,
                                    real numberTimesFailureCriterionIsSatisfied,
                                    real* tractionLeft,
                                    real* normalDeformedLeft,
                                    real* interfaceMaterial_bufferForComm) const;

    /**
     * Set the "interface damage", i.e., delta_max/delta_c
     * @param[in] internalVariables internal variables
     * @param[in] interfaceDamage the measure of the interface damage
     */
    virtual void setInterfaceDamage(real* internalVariables, real interfaceDamage) const;

};
}  // namespace summit

#endif

// end of file
