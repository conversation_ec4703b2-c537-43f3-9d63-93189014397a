// -*- C++ -*-
//
// rrgroup
// summit development team
// massachusetts institute of technology
// (c) 2012-2013 all rights reserved
//

#include "lax_stabilizer.h"
#include <iostream>
#include <cstdlib>
#include <cmath>
#include <pyre/journal.h>
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif
#include "../utils/util.h"
#include "../mathlib/mathmat.h"
#include "../mathlib/mathlib.h"
#include "../mathlib/mathvec.h"
#include "../io/summit_message.h"
#include "../restart/checkpoint.h"
#define NTIMESFRAC 2

summit::LaxStabilizer::LaxStabilizer(Checkpoint* checkpoint, const char* name)
  : UpwindInterfaceDG(checkpoint, name)
{

    DataSet* ds = checkpoint->OpenDataSet("numberOfComponents");
    ds->read(&_numberComponents);

    this->SetInternalVariableMap();

    return;
}


summit::LaxStabilizer::LaxStabilizer() : UpwindInterfaceDG("LaxStabilizer", LAX_STABILIZER_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    this->SetInternalVariableMap();
}

summit::LaxStabilizer::LaxStabilizer(const std::string& name)
  : UpwindInterfaceDG(name, LAX_STABILIZER_NUMBER_INTERNAL_VARIABLES)
{
    // fill the map
    this->SetInternalVariableMap();
}

summit::LaxStabilizer::LaxStabilizer(const std::string& name, const int nInt) : UpwindInterfaceDG(name, nInt) {}

summit::LaxStabilizer::LaxStabilizer(const std::string& name,
                               const int leftMatLabel,
                               int rightMatLabel,
                               const real beta,
                               const int numberComponents,
                               const int nInt)
  : UpwindInterfaceDG(name, leftMatLabel, rightMatLabel, beta, numberComponents, nInt)
{
    // fill the map
    this->SetInternalVariableMap();
}

summit::LaxStabilizer::~LaxStabilizer() {}

void summit::LaxStabilizer::Display()
{
    // display
    Message::Info(" Material Characteristics : (Lax Stabilizer Flux)");
    Message::Info("\t Label of left material ............................................... =\t%d",
                  _leftMatLabel);
    Message::Info("\t Label of right material .............................................. =\t%d",
                  _rightMatLabel);

    Message::Info("\t Betas .......................");
    for (size_t i = 0; i < _betas.size(); i++) {
        Message::Info("\t beta %d : %e", i, _betas[i]);
    }
    Message::Info("\t Interface Types .......................");
    for (size_t i = 0; i < _interface_types.size(); i++) {
        Message::Info("\t Interface %d : %d", i, _interface_types[i]);
    }
    Message::Info("\t Interface Values .......................");
    for (size_t i = 0; i < _interface_values.size(); i++) {
        Message::Info("\t Interface %d : %d", i, _interface_values[i]);
    }
    Message::Info("\t Ux .............................. =\t%e",_ux);
    Message::Info("\t Uy .............................. =\t%e",_uy);
}

/**
 * @brief Load material parameters from a file line
 *
 * This method parses a line from a material input file to extract and set
 * the material parameters for the Lax-Friedrichs stabilizer.
 *
 * @param[in] filename Name of the file being parsed (for error reporting)
 * @param[in] line String containing the material parameters
 *
 * @pre filename must be a valid file path
 * @pre line must contain properly formatted material parameters
 *
 * @note The line format should contain the required parameters for Lax stabilization
 * @throws std::exception if parameter parsing fails
 */
void summit::LaxStabilizer::Load(const std::string& filename, const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    /** properties in input file
     *
     *  left material label
     *  right material label
     *  number of components
     *  (beta, type)
     *
     */
    _leftMatLabel = (int)values[0];
    _rightMatLabel = (int)values[1];
    _numberComponents=1;
    _betas.resize(_numberComponents);
    _interface_types.resize(_numberComponents);
    _interface_values.resize(_numberComponents);
    // fill g attribute
    
    _betas[0] = values[2];
    _interface_types[0] = values[3];
    _interface_values[0] = values[4];
    
    _beta = _betas[0];
    _ux = values[5];
    _uy = values[6];
    
    // end of method
    return;
}

void summit::LaxStabilizer::getInterfaceDamage(const real* internalVariables, real* myDamage) const
{
    return;
}

void summit::LaxStabilizer::SetInternalVariableMap()
{
    return;
}

/**
 * @brief Base implementation of upwind stabilization (no stabilization)
 *
 * The base implementation provides no stabilization by setting the stabilization
 * term to zero. Derived classes should override this method to implement
 * appropriate upwinding schemes for their specific physics.
 *
 * This default implementation is suitable for diffusion-dominated problems
 * where numerical stabilization is not required. For advection-dominated problems,
 * derived classes should implement more sophisticated upwinding schemes.
 *
 * @param[in] concentration_L Current concentration values at the left state
 * @param[in] concentration_L0 Previous time step concentration values at the left state
 * @param[in] concentration_R Current concentration values at the right state
 * @param[in] concentration_R0 Previous time step concentration values at the right state
 * @param[in] Normal Normal vector at the interface, pointing from left to right
 * @param[out] C Stabilization terms (array of length number_unknowns())
 * @param[out] dC Derivatives of stabilization terms (for implicit schemes)
 */
/**
 * @brief Compute Lax-Friedrichs upwind stabilization
 *
 * This method computes the Lax-Friedrichs stabilization terms for the interface.
 * The stabilization helps ensure numerical stability in hyperbolic systems by
 * adding appropriate dissipation based on the jump in solution values.
 *
 * @param[in] concentration_L Left-side concentration values
 * @param[in] concentration_L0 Previous left-side concentration values
 * @param[in] concentration_R Right-side concentration values
 * @param[in] concentration_R0 Previous right-side concentration values
 * @param[in] Normal Interface normal vector
 * @param[out] C Computed stabilization terms
 * @param[out] dC Derivatives of stabilization terms (for implicit schemes)
 *
 * @pre All input arrays must be properly sized and contain valid data
 * @pre Normal must be a unit vector
 * @pre Output arrays C and dC must be properly allocated
 *
 * @note The stabilization is based on the Lax-Friedrichs flux formulation
 */
void summit::LaxStabilizer::upwindStabilization(const real* concentration_L, const real* concentration_L0, const real* concentration_R, const real* concentration_R0, const real* Normal, real* C, real* dC) const
{
    int number_unknowns = 1;// this can be generalized to multiple different convection problems.
                            // we removed this capability because it was too general, and we want basic stuff to work first
    for (int c = 0;c<number_unknowns;c++){
        // This is the lax friedrichs stabilizer!
        C[c] =  0.5 * sqrt(_ux*_ux+_uy+_uy) * (concentration_R0[c] - concentration_L0[c]);
    }
    
    return;
}

// Register SMA as option for class to instantiate
REGISTER(Material, LaxStabilizer);
// end of file
