// -*- C++ -*-
//
// <PERSON><PERSON><PERSON>, <PERSON>
// summit development team
// massachusetts institute of technology
// (c) 2011-2023 all rights reserved
//

#if !defined(summit_materials_material_library_icc)
#error This header file contains implementation details of class MaterialLibrary
#else
#include "interface_dg.h"
#include "../utils/math-util.h"
#include <iostream>
#include <pyre/journal.h>
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

const summit::Material* summit::MaterialLibrary::material(const int i) const {
    // end of method
    return _materials[i];
}

std::string summit::MaterialLibrary::getMaterialName(const int i) const {
    // end of method
    return _materialNames[i];
}

const summit::Material* summit::MaterialLibrary::material(const std::string name) const {
    pyre::journal::firewall_t firewall("summit.materialLibrary.material");
    int label = -1;
    for (size_t i(0); i < _materialNames.size(); ++i)
        if (_materialNames[i] == name) {
            label = i;
            break;
        }
    if (label == -1)
        firewall << "The material " << name << " is not in the material library" 
                 << pyre::journal::endl(__HERE__);
    return _materials[label]; // Done!
}

const summit::Material* summit::MaterialLibrary::interfaceMaterial(const int leftMatLabel,
                                                                   const int rightMatLabel,
                                                                   int& matLabelInter) const {
    // GB: Loop over all laws to get the one that are interface law.  This assumes that all
    // interface material law derive from interfaceDG Assume that it is only used for
    // initialisation --> dynamic_cast_or_continue can be tolerated
    for (size_t i(0); i < _materials.size(); ++i) {
        auto *interlaw = dynamic_cast_or_continue<const InterfaceDG*>(_materials[i],__HERE__);
        if (interlaw != nullptr) {// otherwise it is not an interface law
            if (interlaw->interKey(leftMatLabel, rightMatLabel) ||
                interlaw->interKey(rightMatLabel, leftMatLabel)) {
                matLabelInter = i + 1;
                return _materials[i];
            }
        }
    }
    // If loop is over and the interface has not been identified
    pyre::journal::error_t error("summit.materialLibrary.interfaceMaterial");

    if (_materialNames.empty())
        error << "There is not an interface material law corresponding to the materials "
              << leftMatLabel + 1 << " and " << rightMatLabel + 1 << pyre::journal::endl(__HERE__);
    else
        error << "There is not an interface material law corresponding to the materials "
              << _materialNames[leftMatLabel] << " and " << _materialNames[rightMatLabel]
              << pyre::journal::endl(__HERE__);
    return NULL;
}

const summit::Material* summit::MaterialLibrary::interfaceMaterial(const std::string leftMat,
                                                                   const std::string rightMat,
                                                                   int& matLabelInter) const {

    int leftMatLabel = -1, rightMatLabel = -1; matLabelInter = -1;
    for (size_t i(0); i < _materialNames.size(); ++i) {
        if (_materialNames[i] == leftMat) {
            leftMatLabel = i;
            break;
        }
    }
    for (size_t i(0); i < _materialNames.size(); ++i) {
        if (_materialNames[i] == rightMat) {
            rightMatLabel = i;
            break;
        }
    }
    return this->interfaceMaterial(leftMatLabel, rightMatLabel, matLabelInter);
}

size_t summit::MaterialLibrary::numberOfBulkMaterials() const
{
    int count = 0;

    // Loop over all laws to get those that are not interface laws.
    for (size_t j(0); j < _materials.size(); ++j) {
        auto * interlaw = dynamic_cast_or_continue<const InterfaceDG*>(_materials[j],__HERE__);
        if (interlaw == nullptr) { // it is not an interface law
            count++;
        }
    }
    return count;
}

std::size_t summit::MaterialLibrary::materialIndex(const Material* mat) const
{
    for (size_t i = 0; i < _materials.size(); ++i) {
        if (_materials[i] == mat) return i;
    }
    return -1;
}

std::size_t summit::MaterialLibrary::size() const
{
    // end of method
    return _materials.size();
}

std::size_t summit::MaterialLibrary::_orderedIndexOfInterfaceMaterial(
  std::size_t bulkIndexA, std::size_t bulkIndexB, std::size_t numberBulkMaterials) const
{
    std::size_t l = std::min(bulkIndexA, bulkIndexB) + 1;
    std::size_t L = std::max(bulkIndexA, bulkIndexB) + 1;
    return (numberBulkMaterials + 1) * (l - 1) - l * (l - 1) / 2 + L - l;
}

bool summit::MaterialLibrary::maxStabilizationParameter(real& beta) const
{
    beta = 0.0;
    bool dg = false;

    for (size_t i = 0; i < _materials.size(); ++i) {
	auto * matDG = dynamic_cast_or_continue<InterfaceDG const*>(_materials[i],__HERE__);
        if (matDG != nullptr) {
            dg = true;
            beta = MathUtil<real>::max(beta, matDG->StabilityParameter(InterfaceDG::MAX));
        }
    }

    return dg;
}

void summit::MaterialLibrary::addMaterial(const size_t matnum, Material* mat)
{
    pyre::journal::firewall_t firewall("summit.materialLibrary.interfaceMaterial");

    // test that the material is unique
    if (matnum < _materials.size()) {
        if (_materials[matnum - 1] != nullptr)
            firewall << "The material number " << matnum << " already exists in the material library"
                     << " so your material cannot be added..." << pyre::journal::endl(__HERE__);
        _materials[matnum - 1] = mat;
        return;
    }
    // the material as to be inserted in position greater the the vector size
    // --> "resize" the vector
    std::vector<Material*> newmat(matnum, nullptr);
    std::vector<bool> newmatown(matnum, false);
    newmat[matnum - 1] = mat;  // insert the new material
    std::copy(_materials.begin(), _materials.end(), newmat.begin());
    std::copy(_matowner.begin(), _matowner.end(), newmatown.begin());
    _materials.swap(newmat);
    _matowner.swap(newmatown);
    return;
}

#endif

// end of file
