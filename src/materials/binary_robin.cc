// -*- C++ -*-
//
// rrgroup
// summit development team
// massachusetts institute of technology
// (c) 2012-2023 all rights reserved
//

#include "binary_robin.h"
#include <iostream>
#include <cstdlib>
#include <cmath>
#include <pyre/journal.h>
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif
#include "../utils/util.h"
#include "../io/summit_message.h"
#include "../restart/checkpoint.h"

summit::BinaryRobin::BinaryRobin() : UpwindInterfaceDG("BinaryRobin", 1)
{
}

summit::BinaryRobin::BinaryRobin(const std::string& name) : UpwindInterfaceDG(name, 1)
{
}

summit::BinaryRobin::BinaryRobin(const std::string& name, const int nInt) : UpwindInterfaceDG(name, nInt)
{
}

summit::BinaryRobin::BinaryRobin(const std::string& name,
                       const int leftMatLabel,
                       const int rightMatLabel,
                       const real beta,
                       const int numberComponents,
                       const int nInt)
  : UpwindInterfaceDG(name, leftMatLabel, rightMatLabel, beta, numberComponents, nInt)
{
}

summit::BinaryRobin::BinaryRobin(Checkpoint* checkpoint, const char* name) : UpwindInterfaceDG(checkpoint, name)
{
}

summit::BinaryRobin::~BinaryRobin()
{
}

/**
 * @brief Load material parameters from a file line
 *
 * This method parses a line from a material input file to extract and set
 * the material parameters for the BinaryRobin interface material model.
 *
 * @param[in] filename Name of the file being parsed (for error reporting)
 * @param[in] line String containing the material parameters
 *
 * @pre filename must be a valid file path
 * @pre line must contain properly formatted material parameters
 *
 * @note The line format should contain the required parameters for the binary Robin model
 * @throws std::exception if parameter parsing fails
 */
void summit::BinaryRobin::Load(const std::string& filename, const std::string& line)
{
    // get material parameters
    std::vector<real> values = GetMaterialParameters(filename, line);

    /** properties in input file
     *
     *  left material label
     *  right material label
     *  number of components
     *  (beta, type)
     *
     */
    _leftMatLabel = (int)values[0];
    _rightMatLabel = _leftMatLabel;
    _numberComponents = 2;

    _betas.resize(_numberComponents);
    _interface_types.resize(_numberComponents);
    _interface_values.resize(_numberComponents);
    _betas[0] = values[1];
    _interface_types[0] = 0;
    _interface_values[0] = 0;
    _beta = _betas[0];
    _theta = values[2];
    _a = values[3];
    _b = values[4];
    _c = values[5];
    _d = values[6];
    _e = values[7];
    _f = values[8];
    if(values.size()>9){
        _g = values[9];
    }else{
        _g = 0.0;
    }
    
    // end of method
    return;
}

#ifdef WITH_YAML_CPP
void summit::BinaryRobin::Load(const YAML::Node &node) {
    pyre::journal::error_t error("summit.materials.BinaryRobin");

    // Load from YAML file
    std::vector<YAML::Node> components;
    try {
        _leftMatLabel  = node["adjacent"][0].as<int>();
        _rightMatLabel = _leftMatLabel;
        components = node["components"].as<std::vector<YAML::Node>>();
    } catch (...) {
        error << "UpwindInterfaceDG::Load: A required key could not be found in the YAML tree."
              << pyre::journal::endl(__HERE__);
    }

    // Check number of components and resize storage appropriately
    _numberComponents = components.size();
    if (_numberComponents == 0)
        error << "UpwindInterfaceDG::Load: At least one component expected" << pyre::journal::endl(__HERE__);
    _betas.resize(_numberComponents);
    _interface_types.resize(_numberComponents);
    _interface_values.resize(_numberComponents);

    // Read component-wise information
    try {
        for (int i = 0; i < _numberComponents; i++) {
            YAML::Node component = components[i];
            _betas[i] = component["beta"].as<real>();
            _interface_values[i] = component["interface-value"].as<real>();
            std::string type = component["interface-type"].as<std::string>();
            if (type == "neumann" || type == "Neumann") 
                _interface_types[i] = 0;
            else if (type == "dirichlet" || type == "Dirichlet") 
                _interface_types[i] = 1;
            else
                error << "UpwindInterfaceDG::Load: Unknown interface type " << type << pyre::journal::endl(__HERE__);
        }
    } catch (...) {
        error << "UpwindInterfaceDG::Load: Error reading component beta, interface_value, or interface_type keys from YAML tree."
              << pyre::journal::endl(__HERE__);
    }
    _beta = _betas[0];
    _theta = node["temperature"][0].as<real>();

    _a = node["a"][0].as<real>();
    _b = node["b"][0].as<real>();
    _c = node["c"][0].as<real>();
    _d = node["d"][0].as<real>();
    _e = node["e"][0].as<real>();
    _f = node["f"][0].as<real>();
    _g = node["g"][0].as<real>();

    return; // Done!
}
#endif

void summit::BinaryRobin::Display()
{
    // display
    Message::Info(" Material Characteristics : (BinaryRobin)");
    Message::Info("\t Label of left material ............................................... =\t%d", _leftMatLabel);
    Message::Info("\t Scaling factor of DG method (Dummy Field) .............................. =\t%e", _beta);
    Message::Info("\t Prescribed Temperature (Dummy Field) .............................. =\t%e", _theta);
    Message::Info("\t Parameter a .............................. =\t%e", _a);
    Message::Info("\t Parameter b .............................. =\t%e", _b);
    Message::Info("\t Parameter c .............................. =\t%e", _c);
    Message::Info("\t Parameter d .............................. =\t%e", _d);
    Message::Info("\t Parameter e .............................. =\t%e", _e);
    Message::Info("\t Parameter f .............................. =\t%e", _f);
    Message::Info("\t Parameter g .............................. =\t%e", _g);
}

/**
 * @brief Get interface damage values from internal variables
 *
 * This method extracts damage values from the internal variable state for
 * the binary Robin interface model. The damage values are used to track
 * interface degradation or failure.
 *
 * @param[in] internalVariables Array of internal variable values
 * @param[out] myDamage Array to store the computed damage values
 *
 * @pre internalVariables must contain valid internal variable data
 * @pre myDamage must be properly allocated to store damage values
 *
 * @note Currently uses default behavior from base class
 */
void summit::BinaryRobin::getInterfaceDamage(const real* internalVariables, real* myDamage) const
{
    // this should be the default behavior
    return;
}

void summit::BinaryRobin::getInterfaceTypes(const real* internalVariables, int* myTypes) const
{
    // this should be the default behavior
    return;
}

void summit::BinaryRobin::getInterfaceValues(const real* internalVariables, real* myValues) const
{
    // this should be the default behavior
    return;
}
void summit::BinaryRobin::SetInternalVariableMap()
{
    this->SetLocationInInternalTable("Current Time", 0, 1);
    return;
}

/**
 * @brief Base implementation of viscous boundary flux
 *
 * The base implementation provides the capability to do surface integrals
 * this function can depend on the gradient
 */
/**
 * @brief Compute viscous boundary flux for binary Robin interface
 *
 * This method computes the viscous flux contribution at the interface boundary
 * for the binary Robin model. The flux depends on the concentration gradient
 * and interface properties.
 *
 * @param[in] ndm Number of spatial dimensions
 * @param[in] ndf Number of degrees of freedom per node
 * @param[in] dt Time step size
 * @param[in] measInterface Interface measure (area/length)
 * @param[in,out] internal_new Updated internal variables
 * @param[in] concentration_L Left-side concentration values
 * @param[in] concentration_L0 Previous left-side concentration values
 * @param[in] Dconcentration_L Left-side concentration gradients
 * @param[in] Dconcentration_L0 Previous left-side concentration gradients
 * @param[in] Normal Interface normal vector
 * @param[in] Coord Interface coordinates
 * @param[out] fDotN Normal flux component
 * @param[out] dFDotNdC Flux derivative with respect to concentration
 * @param[out] dFDotNdgradC Flux derivative with respect to concentration gradient
 *
 * @pre All input arrays must be properly sized and contain valid data
 * @pre ndm and ndf must be positive
 */
void summit::BinaryRobin::viscousBoundaryFlux(const int ndm, const int ndf, real dt, real measInterface, real* internal_new, const real* concentration_L, const real* concentration_L0, const real* Dconcentration_L, const real* Dconcentration_L0, const real* Normal, const real* Coord, real* fDotN, real* dFDotNdC, real* dFDotNdgradC) const
{
    

    // this second field is the dummy field. we set it to be some temperate using the weak dirichlet capability
    fDotN[1] = _beta/measInterface * (concentration_L[1] - _theta);
    dFDotNdC[3] = _beta/measInterface;

    // here we construct a ridiculous nonlinear heat flux obtained from the test suite of Georgios and Theo
    //fDotN[0] = _a * std::exp(_b * concentration_L[1]) + _c * std::sin(_d * concentration_L[0]) + _e * concentration_L[0]*concentration_L[0] * concentration_L[1] + _f;
    //dFDotNdC[0] = _c * _d * std::cos(_d * concentration_L[0]) + 2. * _e * concentration_L[1] * concentration_L[0];
    //dFDotNdC[1] = _a * _b * std::exp(_b * concentration_L[1]) + _e * concentration_L[0]*concentration_L[0];

    // Ok, lets do something way simpler and more reasonable
    fDotN[0] = _a + _b * concentration_L[0] + _c * concentration_L[1]
        + _d * concentration_L[0] * concentration_L[0]
        + _e * concentration_L[0] * concentration_L[1]
        + _f * concentration_L[1] * concentration_L[1];
    dFDotNdC[0] = _b + 2 * _d * concentration_L[0] + _e * concentration_L[1];
    dFDotNdC[0 + 1 * 2 ] = _c + 2 * _f * concentration_L[1] + _e * concentration_L[0]; // derivative of first part with second component!
    
    // here we add the new capability, which is gradient-dependent nonlinear BCs
    int c = 1;
    for (int dimension =0; dimension< ndm; dimension++){
        // Gradient along the normal direction alters the flux
        fDotN[0] += Dconcentration_L[dimension + c * ndm] * Normal[dimension] * _g;
        dFDotNdgradC[0 + c * (ndf*ndm) + dimension ] +=  Normal[dimension] * _g;
    }
    // Update internal variables
    internal_new[0] += dt;
    return;
}

// Register BinaryRobin as option for class to instantiate
REGISTER(Material, BinaryRobin);

// end of file