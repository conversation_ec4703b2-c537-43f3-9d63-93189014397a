/**
 * @file surface_heating.h
 * @brief Surface heating boundary condition material for thermal simulations
 * <AUTHOR> Development Team
 * @date 2012-2023
 *
 * This file contains the SurfaceHeating class which implements surface heating
 * boundary conditions for thermal and coupled thermal-mechanical simulations.
 * This material handles prescribed heat flux, convective heating, and radiative
 * heating at domain boundaries.
 */

#ifndef SUMMIT_SURFACE_HEATING_H
#define SUMMIT_SURFACE_HEATING_H

#include "upwind_interface_dg.h"
#ifdef WITH_YAML_CPP
#include <yaml-cpp/yaml.h>
#endif

namespace summit {

class Checkpoint;

/**
 * @brief Surface heating boundary condition material for thermal simulations
 *
 * The SurfaceHeating class implements various types of surface heating boundary
 * conditions commonly encountered in thermal and coupled thermal-mechanical
 * simulations. This material is essential for modeling heat transfer at domain
 * boundaries where thermal energy is added to or removed from the system.
 *
 * ## Supported Heating Types
 *
 * ### 1. Prescribed Heat Flux (Neumann Boundary Condition)
 * Applies a specified heat flux normal to the surface:
 * - **Constant flux**: q̇ = constant [W/m²]
 * - **Time-dependent flux**: q̇ = f(t) [W/m²]
 * - **Spatially varying flux**: q̇ = f(x,y,z) [W/m²]
 * - **Combined**: q̇ = f(x,y,z,t) [W/m²]
 *
 * ### 2. Convective Heating (Robin Boundary Condition)
 * Models convective heat transfer with an external fluid:
 * - **Newton's law of cooling**: q̇ = h(T_∞ - T_surface)
 * - **h**: Convective heat transfer coefficient [W/(m²·K)]
 * - **T_∞**: External fluid temperature [K]
 * - **T_surface**: Surface temperature [K]
 *
 * ### 3. Radiative Heating
 * Handles thermal radiation exchange:
 * - **Stefan-Boltzmann law**: q̇ = εσ(T_∞⁴ - T_surface⁴)
 * - **ε**: Surface emissivity [dimensionless, 0-1]
 * - **σ**: Stefan-Boltzmann constant [5.67×10⁻⁸ W/(m²·K⁴)]
 * - **T_∞**: Environment temperature [K]
 *
 * ### 4. Combined Heating
 * Superposition of multiple heating mechanisms:
 * - **Total flux**: q̇_total = q̇_prescribed + q̇_convective + q̇_radiative
 * - **Coupled effects**: Temperature-dependent properties
 * - **Nonlinear interactions**: Radiation-convection coupling
 *
 * ## Physical Applications
 *
 * ### Aerospace Engineering
 * - **Atmospheric reentry**: Aerodynamic heating of spacecraft
 * - **Jet engine components**: Combustion chamber and turbine heating
 * - **Thermal protection systems**: Heat shield design and analysis
 * - **Solar panel heating**: Space environment thermal loads
 *
 * ### Manufacturing Processes
 * - **Welding simulations**: Arc and laser heating
 * - **Heat treatment**: Furnace and induction heating
 * - **Additive manufacturing**: Laser powder bed fusion
 * - **Casting processes**: Mold heating and cooling
 *
 * ### Energy Systems
 * - **Nuclear reactors**: Fuel rod and vessel heating
 * - **Solar collectors**: Concentrated solar power systems
 * - **Heat exchangers**: Thermal boundary conditions
 * - **Geothermal systems**: Ground-coupled heat transfer
 *
 * ### Electronics Cooling
 * - **Chip packaging**: Heat sink interfaces
 * - **Power electronics**: Thermal management
 * - **LED systems**: Junction heating effects
 * - **Battery thermal management**: Heating and cooling
 *
 * ## Mathematical Formulation
 *
 * ### Heat Conduction Equation
 * The governing equation for heat transfer:
 * ρcp ∂T/∂t = ∇·(k∇T) + Q̇
 *
 * Where:
 * - **ρ**: Density [kg/m³]
 * - **cp**: Specific heat capacity [J/(kg·K)]
 * - **k**: Thermal conductivity [W/(m·K)]
 * - **Q̇**: Volumetric heat generation [W/m³]
 *
 * ### Boundary Condition Implementation
 * At the heated surface:
 * -k ∂T/∂n = q̇_applied
 *
 * Where:
 * - **n**: Outward normal direction
 * - **q̇_applied**: Applied heat flux from SurfaceHeating material
 *
 * ## Numerical Implementation
 *
 * ### Discontinuous Galerkin Integration
 * The surface heating is implemented through interface flux terms:
 * - **Weak form**: ∫_Γ q̇ · v dΓ
 * - **Numerical flux**: Proper upwinding for stability
 * - **Interface treatment**: Consistent with DG formulation
 *
 * ### Time Integration
 * Supports various time integration schemes:
 * - **Explicit methods**: Forward Euler, Runge-Kutta
 * - **Implicit methods**: Backward Euler, BDF
 * - **IMEX schemes**: Implicit-explicit combinations
 *
 * ### Nonlinear Solver Integration
 * For temperature-dependent heating:
 * - **Newton-Raphson**: Jacobian contributions
 * - **Picard iteration**: Fixed-point methods
 * - **Continuation methods**: Parameter sweeping
 *
 * ## Key Features
 *
 * ### Temperature Dependence
 * - **Variable properties**: h(T), ε(T), k(T)
 * - **Nonlinear effects**: Radiation T⁴ dependence
 * - **Phase change**: Latent heat effects
 *
 * ### Spatial Variation
 * - **Heterogeneous heating**: Non-uniform flux distribution
 * - **Geometric effects**: Surface curvature and orientation
 * - **Multi-scale modeling**: Local and global heat transfer
 *
 * ### Coupling Capabilities
 * - **Thermal-mechanical**: Thermal stress analysis
 * - **Thermal-chemical**: Reaction kinetics coupling
 * - **Thermal-fluid**: Conjugate heat transfer
 *
 * @note This class inherits from UpwindInterfaceDG for DG interface handling
 * @note Supports both 2D and 3D thermal simulations
 * @note Can be combined with other boundary condition materials
 * @note Includes provisions for restart capability and checkpointing
 *
 * @warning Radiative heating requires careful handling of T⁴ nonlinearity
 * @warning High heat flux values may require small time steps for stability
 * @warning Temperature-dependent properties need convergence monitoring
 *
 * @see UpwindInterfaceDG for base interface functionality
 * @see WeakTemperature for alternative thermal boundary conditions
 * @see Wall for adiabatic boundary conditions
 */
class SurfaceHeating : public UpwindInterfaceDG {
  public:
    /**
     * @brief Default constructor for SurfaceHeating material
     *
     * Creates a SurfaceHeating object with default parameters suitable for
     * basic thermal boundary condition applications. Default settings provide
     * a foundation that can be customized through parameter loading.
     *
     * @note Prefer named constructors for better code clarity
     * @see SurfaceHeating(const std::string&) for named construction
     */
    SurfaceHeating();

    /**
     * @brief Named constructor for SurfaceHeating material
     *
     * Creates a SurfaceHeating object with a specified name identifier.
     * This constructor is suitable for basic surface heating applications
     * where default internal variable allocation is sufficient.
     *
     * @param[in] name Descriptive name for the heating material (e.g., "WallHeating", "ConvectiveBC")
     *
     * @pre name must be a non-empty string
     * @note Uses default internal variable count for standard heating applications
     * @see SurfaceHeating(const std::string&, const int) for custom internal variables
     */
    SurfaceHeating(const std::string& name);

    /**
     * @brief Constructor with custom internal variable allocation
     *
     * Creates a SurfaceHeating object with specified name and number of internal
     * variables. This constructor is useful for complex heating models that require
     * additional state variables for history-dependent or nonlinear behavior.
     *
     * @param[in] name Descriptive name for the heating material
     * @param[in] nInt Number of internal variables for state tracking
     *
     * @pre name must be a non-empty string
     * @pre nInt must be non-negative
     *
     * @note Internal variables can store:
     *       - Temperature history for time-dependent properties
     *       - Heat flux history for thermal fatigue analysis
     *       - Phase change state variables
     *       - Damage or degradation parameters
     */
    SurfaceHeating(const std::string& name, const int nInt);

    /**
     * @brief Full constructor with interface coupling parameters
     *
     * Creates a SurfaceHeating object with complete specification of interface
     * coupling parameters for multi-material thermal simulations. This constructor
     * provides full control over the interface behavior and stability parameters.
     *
     * @param[in] name Descriptive name for the heating interface material
     * @param[in] leftMatLabel Material label for the left-side bulk material (1-based index)
     * @param[in] rightMatLabel Material label for the right-side bulk material (1-based index)
     * @param[in] beta Stability parameter for interface flux stabilization [dimensionless]
     * @param[in] numberComponents Number of thermal solution components (typically 1 for temperature)
     * @param[in] nInt Number of internal variables (default: 0)
     *
     * @pre name must be a non-empty string
     * @pre leftMatLabel and rightMatLabel must be valid material indices (≥ 1)
     * @pre beta must be non-negative (typically 0.0 to 2.0)
     * @pre numberComponents must be positive (typically 1 for scalar temperature)
     * @pre nInt must be non-negative
     *
     * @note The stability parameter beta controls numerical dissipation:
     *       - beta = 0.0: Minimal dissipation (may require small time steps)
     *       - beta = 1.0: Standard stabilization for most applications
     *       - beta > 1.0: Enhanced stability for high heat flux or steep gradients
     * @note Material labels must correspond to valid entries in the material library
     * @note For pure thermal problems, numberComponents is typically 1
     * @note For coupled thermal-mechanical problems, numberComponents may be higher
     */
    SurfaceHeating(const std::string& name,
           const int leftMatLabel,
           const int rightMatLabel,
           const real beta,
           const int numberComponents,
           const int nInt = 0);

    /**
     * Constructor
     * @param[in] checkpoint object associated with restart
     * @param[in] name the name of the group within the checkpoint object
     */
    SurfaceHeating(Checkpoint* checkpoint, const char* name);

    /**
     * Destructor
     */
    virtual ~SurfaceHeating();

    /**
     * Method to display the material parameters to the output chanel
     */
    void Display() override;

    virtual MaterialType GetMaterialType() const { return MATERIAL_SurfaceHeating; }

    /**
     * Method to know if the interface law is for boundary conditions
     * @return true if this model is for external surfaces
     */
    bool IsBoundary() const { return true; }
    
    /**
     * Method to load a material file
     * @param[in] filename a string
     * @param[in] line a string
     */
    virtual void Load(const std::string& filename, const std::string& line);
    
    #ifdef WITH_YAML_CPP
    /**
     * Method to load material properties from a YAML node
     * @param[in] node the YAML node for this specific material
     */
    virtual void Load(const YAML::Node &node);
    #endif

    /**
     * @brief returns the DG parameter for a single component of a multi-component problem
     * 
     * @param component which component of the problem are we talking about 
     * @return real 
     */
    void getInterfaceDamage(const real* internalVariables, real* myDamage) const;

    /**
     * @brief returns the DG parameter for a single component of a multi-component problem
     * 
     * @param component which component of the problem are we talking about 
     * @return real 
     */
    void getInterfaceTypes(const real* internalVariables, int* myTypes) const;

    /**
     * @brief returns the DG parameter for a single component of a multi-component problem
     * 
     * @param component which component of the problem are we talking about 
     * @return real 
     */
    void getInterfaceValues(const real* internalVariables, real* myValues) const;
    
        /**
     * @brief Base implementation of viscous boundary flux
     *
     * The base implementation provides the capability to do surface integrals
     * this function can depend on the gradient
     * TODO improve documentation //(up,qp,np,ib,ui,p,param,time)
     */
    virtual void viscousBoundaryFlux(const int ndm, const int ndf, real dt, real measInterface, real* internal_new, const real* concentration_L, const real* concentration_L0, const real* Dconcentration_L, const real* Dconcentration_L0, const real* Normal, const real* Coord, real* fDotN, real* dFDotNdC, real* dFDotNdgradC) const;
  private:
    /**
     * Copy Constructor.
     * Declared private not implemented
     */
    SurfaceHeating(const SurfaceHeating&);

    static Register<Material, SurfaceHeating> reg;
    
    /**
     * Overloaded operator =.
     * Declared private and not implemented
     */
    SurfaceHeating& operator=(const SurfaceHeating&);

    real _heatFlux;
};

}  // namespace summit

#endif // SUMMIT_SURFACE_HEATING_H

// end of file