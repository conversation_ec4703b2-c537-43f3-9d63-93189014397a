#include <cassert>
#include <vtkVersion.h>
#include <vtkDoubleArray.h>
#include <vtkFloatArray.h>
#include <vtkPointData.h>
#include <vtkCellData.h>
#include <vtkUnstructuredGrid.h>
#include <vtkHexahedron.h>
#include <vtkQuadraticHexahedron.h>
#include <vtkTriQuadraticHexahedron.h>
#include <vtkQuadraticTetra.h>
#include <vtkLine.h>
#include <vtkTetra.h>
#include <vtkTriangle.h>
#include <vtkQuadraticTriangle.h>
#include <vtkPyramid.h>
#include <vtkWedge.h>
#include <vtkQuad.h>
#include <vtkQuadraticQuad.h>
#include <vtkBiQuadraticQuad.h>
#include <vtkCellType.h>
#include <vtkXMLUnstructuredGridWriter.h>

#include <iostream>

#include "mesh_writer_vtk.h"

#include "../../fem/function_space.h"

#include "../../parallel/comm_map.h"
#include "../../parallel/mpi_summit.h"
#include "../../parallel/communicator.h"
#include "../../parallel/communication_manager.h"

/// needed to handle DG elements that use lifting operators
/// they index nodes and elements differently
#include "../../elements/variational_dg_element.h"

/**
 * @brief Constructor for VTK mesh writer
 *
 * Creates a VTK mesh writer for outputting finite element simulation results
 * in VTK format. The writer can handle both static mesh data and dynamic
 * mesh data that changes at each time step.
 *
 * @param[in] filename Base filename for VTK output files
 * @param[in] fs Function space containing the mesh and element data
 * @param[in] commManager Communication manager for parallel I/O
 * @param[in] generateMeshDataInEachTimeStep Whether to regenerate mesh data each time step
 *
 * @pre filename must be a valid file path
 * @pre fs must be a properly initialized function space
 * @pre commManager must be initialized for the parallel environment
 *
 * @note If generateMeshDataInEachTimeStep is true, mesh topology can change between time steps
 */
summit::MeshWriterVTK::MeshWriterVTK(const std::string& filename,
                                     const FunctionSpace& fs,
                                     const CommunicationManager& commManager,
                                     bool generateMeshDataInEachTimeStep)
  : MeshWriter(filename, commManager, generateMeshDataInEachTimeStep),
    _fs(fs),
    _generateMeshDataInEachTimeStep(generateMeshDataInEachTimeStep),
    _firstTimeCallLoadMeshDataEachTimeStep(false)
{
    // set the WriterType
    _mytype = MESH_WRITER_VTK;
    // create a VTK mesh
    _mesh = vtkUnstructuredGrid::New();
    if (_generateMeshDataInEachTimeStep == false) {
        // transfer Mesh data from FunctionSpace to VTK
        LoadMeshData(fs);
    }
    else {
        _firstTimeCallLoadMeshDataEachTimeStep = true;
        // std::cout << "\n\nsummit::MeshWriterVTK::MeshWriterVTK\n"
        // << "    ---> Mesh data is generated in each time step\n" << std::endl;
    }
}

summit::MeshWriterVTK::~MeshWriterVTK() { _mesh->Delete(); }

/**
 * @brief Insert a real-valued nodal field for VTK output
 *
 * This method adds a real-valued nodal field to the VTK mesh for visualization.
 * The field values are associated with mesh nodes and will be written to the
 * VTK output file.
 *
 * @param[in] nodal_field NodalField containing real values at mesh nodes
 * @param[in] view_name Name for the field in the VTK output file
 *
 * @pre nodal_field must contain valid data for all mesh nodes
 * @pre view_name must be a valid string identifier
 *
 * @note The field will be available for visualization in VTK-compatible software
 */
void summit::MeshWriterVTK::InsertNodalField(const NodalField<real>& nodal_field,
                                             const std::string& view_name)
{
    return _insertNodalField(nodal_field, view_name);
}

void summit::MeshWriterVTK::InsertNodalField(const NodalField<int>& nodal_field,
                                             const std::string& view_name)
{
    return _insertNodalField(nodal_field, view_name);
}

void summit::MeshWriterVTK::InsertNodalField(const NodalField<bool>& nodal_field,
                                             const std::string& view_name)
{
    return _insertNodalField(nodal_field, view_name);
}

template <typename T>
void summit::MeshWriterVTK::_insertNodalField(const NodalField<T>& nodal_field,
                                              const std::string& view_name)
{
    int dim = nodal_field.dim();
    int nodes = nodal_field.nodes();

    // initialize a vtk array
    vtkDoubleArray* vtk_array = vtkDoubleArray::New();
    vtk_array->SetName(view_name.c_str());
    // vtk_array->SetName(nodal_field.name().c_str());
    vtk_array->SetNumberOfComponents(dim);
    vtk_array->SetNumberOfTuples(nodes);

    // If the nodal field has component names, set them in the VTK array
    std::vector<std::string> component_names = nodal_field.component_names();
    if (component_names.size() != 0)
        for (int i = 0; i < dim; i++)
            vtk_array->SetComponentName(i, component_names[i].c_str());

    // copy data into array, we don't use SetArray because it does not allow
    // us to keep the nodal_field const
    double* tmp = new double[dim];
    // loop over the nodes of the mesh
    for (int i = 0; i < nodes; ++i) {
        // use a temp buffer to handle:
        //    1) float->double conversion
        //    2) be blind to the storage type of NodalField
        for (int j = 0; j < dim; ++j) {
            tmp[j] = 0.0;
        }
        for (int j = 0; j < dim; ++j) {
            tmp[j] = nodal_field(i, j);
        }
        vtk_array->SetTuple(i, tmp);
    }
    delete[] tmp;

    // insert array into output mesh
    _mesh->GetPointData()->AddArray(vtk_array);

    // clean up array
    vtk_array->Delete();
}

void summit::MeshWriterVTK::InsertElementField(const std::vector<real>& element_field,
                                               const std::string& view_name,
                                               const size_t components)
{
    size_t elements = element_field.size() / components;

    // initialize a vtk array
    vtkDoubleArray* vtk_array = vtkDoubleArray::New();
    vtk_array->SetName(view_name.c_str());
    // vtk_array->SetName(nodal_field.name().c_str());
    vtk_array->SetNumberOfComponents(components);
    vtk_array->SetNumberOfTuples(elements * components);

    // copy data into array
    for (size_t e = 0; e < elements; ++e)
        vtk_array->SetTuple(e, &(element_field[e * components]));

    // insert array into output mesh
    _mesh->GetCellData()->AddArray(vtk_array);

    // clean up array
    vtk_array->Delete();
    return;
}

/**
 * @brief Write VTK output file for a given time step
 *
 * This method writes the mesh data and all associated field data to a VTK
 * unstructured grid file. The output can be in either ASCII or binary format.
 *
 * @param[in] step Time step number for file naming
 * @param[in] ascii Whether to write in ASCII format (true) or binary format (false)
 *
 * @pre All nodal and element fields must be properly inserted before calling
 * @pre step must be a valid time step identifier
 *
 * @note In parallel runs, this creates both local .vtu files and a .pvtu master file
 * @note ASCII format is human-readable but larger; binary format is more compact
 */
void summit::MeshWriterVTK::Write(int step, bool ascii)
{
    if (_generateMeshDataInEachTimeStep == true) {
        // transfer Mesh data from FunctionSpace to VTK
        LoadMeshDataEachTimeStep(_fs);
    }

    // setup meshwriter struct
    vtkXMLUnstructuredGridWriter* meshWriter = vtkXMLUnstructuredGridWriter::New();
#if VTK_MAJOR_VERSION >= 6
    meshWriter->SetInputData(_mesh);
#else
    meshWriter->SetInput(_mesh);
#endif
    // setup filename
    char buf[32];
    sprintf(buf, "-%08d", step);
    meshWriter->SetFileName(
      (_filename + buf + "." + meshWriter->GetDefaultFileExtension()).c_str());

    // if output format is ascii
    if (ascii == true) {
        // set vtk file format into ascii
        meshWriter->SetDataModeToAscii();
    }
    // then write
    meshWriter->Write();
    // ...and clean
    meshWriter->Delete();

    // processor 0 and more than one processor writes out the pvtu file
    if (_commManager.rank() == 0 && _commManager.size() > 1) {
        writePVTU(step);
    }
}

void summit::MeshWriterVTK::Write(int step, real time)
{
    // output the data
    Write(step);

    // processor 0, writes out timestamp
    if (_commManager.rank() == 0) {
        // update the pvd file
        WritePVD(step, time);
    }
}

/**
 * @brief Load mesh topology and connectivity data from function space
 *
 * This method extracts mesh topology, node coordinates, and element connectivity
 * from the function space and converts it to VTK format. This is typically called
 * once for static meshes or repeatedly for adaptive/moving meshes.
 *
 * @param[in] fs Function space containing the mesh and element data
 *
 * @pre fs must be a properly discretized function space
 * @pre All element sets in fs must have valid connectivity data
 *
 * @note This method creates VTK cells for all element types in the function space
 * @note Coordinates are extracted and stored in VTK point data structure
 */
void summit::MeshWriterVTK::LoadMeshData(const FunctionSpace& fs)
{
    const std::vector<ElementSetBody*>& element_sets = fs.element_sets();

    // insert nodes
    const NodalField<real>& coordinates = fs.coordinates();
    vtkPoints* points = vtkPoints::New();
    real point[3] = { 0, 0, 0 };
    for (int i = 0; i < fs.nodes(); ++i) {
        for (int j = 0; j < coordinates.dim(); ++j) {
            point[j] = coordinates(i, j);
        }
        points->InsertPoint(i, point);
    }
    _mesh->SetPoints(points);
    points->Delete();

    // insert materials and elements
    // although it seems like a good idea to have the material array as an integer, the threshold
    // filter from Paraview does not work well with integers
    vtkFloatArray* vtk_array = vtkFloatArray::New();
    vtk_array->SetName("Materials");
    vtk_array->SetNumberOfComponents(1);

    for (unsigned int es = 0; es < element_sets.size(); ++es) {
        // build a vtk cell for the element type
        _loadElementSetData(element_sets[es], vtk_array);
    }

    // insert array into output mesh
    _mesh->GetCellData()->AddArray(vtk_array);

    // get dimension of vtk_array
    int tuplesNumber = vtk_array->GetNumberOfTuples();

    // clean up array
    vtk_array->Delete();

    if (_generateMeshDataInEachTimeStep == false) {
        // create array for processors id
        vtkFloatArray* vtk_array_procs = vtkFloatArray::New();
        vtk_array_procs->SetName("ProcessorId");
        vtk_array_procs->SetNumberOfComponents(1);

        // Fill in processors id
        for (int i = 0; i < tuplesNumber; i++) {
            float procId = (double)summit::MPISummit::Rank();
#if (VTK_MAJOR_VERSION < 7) || (VTK_MAJOR_VERSION==7 && VTK_MINOR_VERSION==0)
            vtk_array_procs->InsertNextTupleValue(&procId);
#else
            vtk_array_procs->InsertNextTypedTuple(&procId);
#endif
        }

        // insert array into output mesh
        _mesh->GetCellData()->AddArray(vtk_array_procs);

        // clean up array
        vtk_array_procs->Delete();
    }
    // all done
    return;
}


void summit::MeshWriterVTK::LoadMeshDataEachTimeStep(const FunctionSpace& fs)
{
    // create all the nodes
    if (_firstTimeCallLoadMeshDataEachTimeStep) {
        LoadMeshData(fs);

        // set the variable to false
        _firstTimeCallLoadMeshDataEachTimeStep = false;
    }
    else {
        const std::vector<ElementSetBody*>& element_sets = fs.element_sets();

        // no new materials are added
        vtkFloatArray* mat_array =
          vtkFloatArray::SafeDownCast(_mesh->GetCellData()->GetAbstractArray("Materials"));

        for (unsigned int es = 0; es < element_sets.size(); ++es) {
            // build a vtk cell for the element type
            _loadElementSetData(element_sets[es], mat_array);
        }
    }

    return;
}


//#ifdef PARALLEL
void summit::MeshWriterVTK::writePVTU(int step) const
{
    // chop off PID from filename
    std::string base_filename = _filename.substr(0, _filename.size() - 5);
    char stepBuf[256];
    std::sprintf(stepBuf, "-%08d", step);
    std::string pvtu_filename = base_filename + stepBuf + ".pvtu";
    std::ofstream outfile;

    // chop off directory from filename
    size_t pos;
    while ((pos = base_filename.find('/')) != std::string::npos)
        base_filename = base_filename.substr(pos + 1, base_filename.size());

    outfile.open(pvtu_filename.c_str());

    // output header information
    outfile << "<?xml version=\"1.0\"?>" << std::endl;
    outfile << "<VTKFile type=\"PUnstructuredGrid\">" << std::endl;
    outfile << "<PUnstructuredGrid GhostLevel=\"0\">" << std::endl;

    // output point data fields
    outfile << "<PPointData>" << std::endl;
    vtkFieldData* fieldData = _mesh->GetPointData();
    for (int i = 0; fieldData->GetArray(i) != NULL; i++) {
        vtkDataArray* array = fieldData->GetArray(i);

        outfile << "<PDataArray type=\"Float64\" Name=\"" << array->GetName()
                << "\" NumberOfComponents=\"" << array->GetNumberOfComponents() << "\"";

        // Get component names if applicable and add them to the output
        if (array->HasAComponentName())
            for (int j = 0; j < array->GetNumberOfComponents(); ++j)
                outfile << " ComponentName" << j << "=\""
                        << array->GetComponentName(j) << "\"";

        outfile << "/>" << std::endl;
    }
    outfile << "</PPointData>" << std::endl;

    outfile << "<PCellData>" << std::endl;
    fieldData = _mesh->GetCellData();
    for (int i = 0; fieldData->GetArray(i) != NULL; i++) {
        vtkDataArray* array = fieldData->GetArray(i);

        std::string fieldName(array->GetName());
        if (fieldName.compare(0, 9, "Materials") == 0 ||
            fieldName.compare(0, 11, "ProcessorId") == 0) {
            outfile << "<PDataArray type=\"Float32\" Name=\"" << array->GetName()
                    << "\" NumberOfComponents=\"" << array->GetNumberOfComponents() << "\"/>"
                    << std::endl;
        }
        else {
            outfile << "<PDataArray type=\"Float64\" Name=\"" << array->GetName()
                    << "\" NumberOfComponents=\"" << array->GetNumberOfComponents() << "\"/>"
                    << std::endl;
        }
    }
    outfile << "</PCellData>" << std::endl;

    // output points (coordinates)
    outfile << "<PPoints>" << std::endl;
    outfile << "<PDataArray type=\"Float32\" NumberOfComponents=\"3\"/>" << std::endl;
    outfile << "</PPoints>" << std::endl;

    // write out piece files
    std::string piece_filename;
    int nProcs = _commManager.size();
    char procBuf[32];
    for (int i = 0; i < nProcs; i++) {
        // setup filename
        sprintf(procBuf, "-%04d", i);
        piece_filename = base_filename + procBuf + stepBuf + ".vtu";
        outfile << "<Piece Source=\"" + piece_filename + "\"/>" << std::endl;
    }

    // output footer
    outfile << "</PUnstructuredGrid>" << std::endl;
    outfile << "</VTKFile>" << std::endl;

    outfile.close();
}
//#endif


void summit::MeshWriterVTK::WritePVD(int step, real time) const
{
    // construct relevantfilenames
    std::string base_filename;
    char stepBuf[256];
    std::sprintf(stepBuf, "-%08d", step);

    // more than one processor
    if (_commManager.size() > 1) {
        // chop off PID from filename
        base_filename = _filename.substr(0, _filename.size() - 5) + stepBuf + ".pvtu";
    }
    else {
        base_filename = _filename + stepBuf + ".vtu";
    }

    std::string pvd_filename = _filename + ".pvd";

    // check if it exists
    std::ifstream test;
    std::fstream outfile;
    test.open(pvd_filename.c_str(), std::ios::in);
    test.close();

    // if doesn't exist, write out header and footer
    if (test.fail()) {
        test.clear(ios::failbit);
        outfile.open(pvd_filename.c_str(), std::ios::out | std::ios::trunc);
        outfile << "<?xml version=\"1.0\"?>" << std::endl;
        outfile << "<VTKFile type=\"Collection\" version=\"0.1\">" << std::endl;
        outfile << "<Collection>" << std::endl;
        outfile << "</Collection>" << std::endl;
        outfile << "</VTKFile>" << std::endl;
        outfile.close();
    }

    // open file and seek to just before footer
    outfile.open(pvd_filename.c_str(), std::ios::out | std::ios::in);
    outfile.seekp(-25, std::ios_base::end);

    // output current timestep and associated file
    outfile << "<DataSet timestep=\"" << time << "\" file=\"";

    // chop off directory from filename
    size_t pos;
    while ((pos = base_filename.find('/')) != std::string::npos)
        base_filename = base_filename.substr(pos + 1, base_filename.size());

    outfile << base_filename;

    outfile << "\"/>" << std::endl;

    // reconstruct footer
    outfile << "</Collection>" << std::endl;
    outfile << "</VTKFile>" << std::endl;

    outfile.close();
}

void summit::MeshWriterVTK::extend(ElementSetBody const* eset,
                                   NodalField<real> const& coordinates,
                                   const size_t first_elem_index)
{
    // insert the new coordinates into the grid
    vtkPoints* points = _mesh->GetPoints();
    real point[3] = { 0, 0, 0 };

    // Get the last coordinates previously inserted into the grid
    int first_inserted_node = _mesh->GetNumberOfPoints();
    // loop over the new nodes
    for (int i = first_inserted_node; i < coordinates.nodes(); ++i) {
        for (int j = 0; j < coordinates.dim(); ++j) {
            point[j] = coordinates(i, j);
        }
        points->InsertPoint(i, point);
    }

// get the material array
#if 0
    // although it seems like a good idea to have the material array as an integer, the threshold
    // filter from Paraview does not work well with integers
    vtkIntArray* mat_array = 
        vtkIntArray::SafeDownCast(_mesh->GetCellData()->GetAbstractArray("Materials"));
#endif
    vtkFloatArray* mat_array =
      vtkFloatArray::SafeDownCast(_mesh->GetCellData()->GetAbstractArray("Materials"));

    // call the filling method
    _loadElementSetData(eset, mat_array, first_elem_index);
    return;
}


// ------------------------------------------------------------------
// This is the list og VTK elements available in this routine
// ------------------------------------------------------------------
// VTK_EMPTY_CELL = 0
// VTK_LINE = 3,
// VTK_TRIANGLE = 5
// VTK_QUAD = 9
// VTK_TETRA = 10
// VTK_HEXAHEDRON = 12
// VTK_WEDGE = 13
// VTK_PYRAMID = 14
// VTK_QUADRATIC_TRIANGLE = 22
// VTK_QUADRATIC_QUAD = 23
// VTK_QUADRATIC_TETRA = 24
// VTK_QUADRATIC_HEXAHEDRON = 25
// VTK_BIQUADRATIC_QUAD = 28
// VTK_TRIQUADRATIC_HEXAHEDRON = 29
// ------------------------------------------------------------------

void summit::MeshWriterVTK::_loadElementSetData(ElementSetBody const* eset,
                                                vtkFloatArray* mat_array,
                                                const size_t first_elem_index)
{
    int pOrder = eset->pOrder();
    float label = (double)eset->materialLabel();
    bool UseSubconnectivities = (pOrder == 1) ? false : true;

    vtkCell* cell;

    /// THIS ENSURES SKIP OF INTERFACE ELEMENT
    if (eset->type() == summit::INTERFACE || eset->type() == summit::HALF_BUTTERFLY || eset->type() == summit::DG_BOUNDARY) return;

    switch (eset->vtkType()) {
        case VTK_EMPTY_CELL:
            return;
        case VTK_LINE:
            cell = vtkLine::New();
            break;
        case VTK_TRIANGLE:
            cell = vtkTriangle::New();
            break;
        case VTK_QUADRATIC_TRIANGLE:
            cell = vtkQuadraticTriangle::New();
            UseSubconnectivities = false;
            break;
        case VTK_HEXAHEDRON:
            cell = vtkHexahedron::New();
            break;
        case VTK_QUADRATIC_HEXAHEDRON:
            cell = vtkQuadraticHexahedron::New();
            UseSubconnectivities = false;
            break;
        case VTK_TRIQUADRATIC_HEXAHEDRON:
            cell = vtkTriQuadraticHexahedron::New();
            UseSubconnectivities = false;
            break;
        case VTK_PYRAMID:
            cell = vtkPyramid::New();
            break;
        case VTK_TETRA:
            cell = vtkTetra::New();
            break;
        case VTK_QUADRATIC_TETRA:
            cell = vtkQuadraticTetra::New();
            UseSubconnectivities = false;
            break;
        case VTK_WEDGE:
            cell = vtkWedge::New();
            break;
        case VTK_QUAD:
            cell = vtkQuad::New();
            break;
        case VTK_QUADRATIC_QUAD:
            cell = vtkQuadraticQuad::New();
            UseSubconnectivities = false;
            break;
        case VTK_BIQUADRATIC_QUAD:
            cell = vtkBiQuadraticQuad::New();
            UseSubconnectivities = false;
            break;
        default:
            printf("VTK Writer cannot output element of type %d\n", eset->vtkType());
            exit(1);
    }
    if (!UseSubconnectivities) {
        // now add the elements and materials

        // BT: The variational DG elements index the DOFs
        // differently than all the other elements.
        // Test if the current element set can be cast
        // to this type.
        VariationalDGElement const* variational_dg_elements =
          dynamic_cast<VariationalDGElement const*>(eset);

        // If this cast was successful, set up the VTK
        // grid using variational DG elements indexing.
        // Otherwise, these are standard summit elements,
        // and we handle them in the regular way.

        if (variational_dg_elements)  // DG type element-based connectivity
        {
            for (size_t e = 0, n = 0; e < eset->elements(); ++e) {
                int npel = eset->nodes_element();

                // Set the connectivity ids for the the cell.
                // Do this even if the cell is a ghost cell,
                // because we still need to skip over the
                // points (the ++n statement)
                for (int j = 0; j < npel; ++j, ++n) {
                    cell->GetPointIds()->SetId(j, n);
                }

                // Skip over element if it's a ghost,
                // don't add it to the vtk grid.
                if (variational_dg_elements->GhostElement(e)) continue;

                // add the cell to the vtk array
                _mesh->InsertNextCell(cell->GetCellType(), cell->GetPointIds());
#if (VTK_MAJOR_VERSION < 7) || (VTK_MAJOR_VERSION==7 && VTK_MINOR_VERSION==0)
                mat_array->InsertNextTupleValue(&label);
#else
                mat_array->InsertNextTypedTuple(&label);
#endif
            }
        }
        else if (cell->GetCellType() == VTK_QUADRATIC_TETRA)
        // node 8 and 9 should be inverted for second order tetra as it comes from Gmsh
        // that defines these two nodes in the opposite way
        {
            std::vector<int> conn_cell(10);
            for (size_t e = first_elem_index; e < eset->elements(); ++e) {
                int npel = eset->dof_map()->NodesElement(e);
                const int* lconn = eset->dof_map()->Connectivity(e);
                std::copy(lconn, lconn + 10, conn_cell.begin());
                std::swap(conn_cell[8], conn_cell[9]);
                for (int j = 0; j < npel; ++j) {
                    cell->GetPointIds()->SetId(j, conn_cell[j]);
                }
                _mesh->InsertNextCell(cell->GetCellType(), cell->GetPointIds());

#if (VTK_MAJOR_VERSION < 7) || (VTK_MAJOR_VERSION==7 && VTK_MINOR_VERSION==0)
                mat_array->InsertNextTupleValue(&label);
#else
                mat_array->InsertNextTypedTuple(&label);
#endif
            }
        }
        else if (cell->GetCellType() == VTK_QUADRATIC_HEXAHEDRON) {
            std::vector<int> conn_cell(20);
            for (size_t e = first_elem_index; e < eset->elements(); ++e) {
                int npel = eset->dof_map()->NodesElement(e);
                const int* lconn = eset->dof_map()->Connectivity(e);
                std::copy(lconn, lconn + 8, conn_cell.begin());
                conn_cell[8] = lconn[8];
                conn_cell[9] = lconn[11];
                conn_cell[10] = lconn[13];
                conn_cell[11] = lconn[9];
                conn_cell[12] = lconn[16];
                conn_cell[13] = lconn[18];
                conn_cell[14] = lconn[19];
                conn_cell[15] = lconn[17];
                conn_cell[16] = lconn[10];
                conn_cell[17] = lconn[12];
                conn_cell[18] = lconn[14];
                conn_cell[19] = lconn[15];
                for (int j = 0; j < npel; ++j) {
                    cell->GetPointIds()->SetId(j, conn_cell[j]);
                }
                _mesh->InsertNextCell(cell->GetCellType(), cell->GetPointIds());

#if (VTK_MAJOR_VERSION < 7) || (VTK_MAJOR_VERSION==7 && VTK_MINOR_VERSION==0)
                mat_array->InsertNextTupleValue(&label);
#else
                mat_array->InsertNextTypedTuple(&label);
#endif
            }
        }
        else if (cell->GetCellType() == VTK_TRIQUADRATIC_HEXAHEDRON) {
            std::vector<int> conn_cell(27);
            for (size_t e = first_elem_index; e < eset->elements(); ++e) {
                int npel = eset->dof_map()->NodesElement(e);
                const int* lconn = eset->dof_map()->Connectivity(e);
                std::copy(lconn, lconn + 8, conn_cell.begin());
                conn_cell[8] = lconn[8];
                conn_cell[9] = lconn[11];
                conn_cell[10] = lconn[13];
                conn_cell[11] = lconn[9];
                conn_cell[12] = lconn[16];
                conn_cell[13] = lconn[18];
                conn_cell[14] = lconn[19];
                conn_cell[15] = lconn[17];
                conn_cell[16] = lconn[10];
                conn_cell[17] = lconn[12];
                conn_cell[18] = lconn[14];
                conn_cell[19] = lconn[15];
                conn_cell[20] = lconn[22];
                conn_cell[21] = lconn[23];
                conn_cell[22] = lconn[21];
                conn_cell[23] = lconn[24];
                conn_cell[24] = lconn[20];
                conn_cell[25] = lconn[25];
                conn_cell[26] = lconn[26];

                for (int j = 0; j < npel; ++j) {
                    cell->GetPointIds()->SetId(j, conn_cell[j]);
                }
                _mesh->InsertNextCell(cell->GetCellType(), cell->GetPointIds());

#if (VTK_MAJOR_VERSION < 7) || (VTK_MAJOR_VERSION==7 && VTK_MINOR_VERSION==0)
                mat_array->InsertNextTupleValue(&label);
#else
                mat_array->InsertNextTypedTuple(&label);
#endif
            }
        }
        else {
            // to decide if we have to write the element e (true) or not (false)
            bool statusWriteElement;

            // now add the elements and the materials
            for (size_t e = first_elem_index; e < eset->elements(); ++e) {
                // get the status to write the element e (true or false)
                statusWriteElement = eset->getStatusWriteElementInOutputFiles(e);

                // if the status is true, the element is added
                // (the case by default is status true, but in some problems not all
                //   the elements are stored)
                if (statusWriteElement) {
                    int npel = eset->dof_map()->NodesElement(e);
                    const int* lconn = eset->dof_map()->Connectivity(e);
                    for (int j = 0; j < npel; ++j) {
                        cell->GetPointIds()->SetId(j, lconn[j]);
                    }
                    _mesh->InsertNextCell(cell->GetCellType(), cell->GetPointIds());

#if (VTK_MAJOR_VERSION < 7) || (VTK_MAJOR_VERSION==7 && VTK_MINOR_VERSION==0)
                    mat_array->InsertNextTupleValue(&label);
#else 
                    mat_array->InsertNextTypedTuple(&label);
#endif
                }
            }
        }
    }
    else {
        // number of the nodes of the simplex
        size_t npel = eset->nodes_simplex();
        // number of connectivities after the subdivision of the element
        int nSubConn = eset->numberSubdivisionConnectivities();
        // connectivities of the sub-elements (they are needed to subdivide the element)
        const std::vector<int>& subdivisionConnectivities =
          eset->elementSubdivisionConnectivities();
        // to decide if we have to write the element e (true) or not (false)
        bool statusWriteElement;

        // now add the elements and the materials
        for (size_t e = first_elem_index; e < eset->elements(); ++e) {
            // get the status to write the element e (true or false)
            statusWriteElement = eset->getStatusWriteElementInOutputFiles(e);

            // if the status is true, the element is added
            // (the case by default is status true, but in some problems not all
            //   the elements are stored)
            if (statusWriteElement) {
                // connectivity of the element e
                const int* lconn = eset->dof_map()->Connectivity(e);
                // loop over the sub-elements
                for (int i = 0; i < nSubConn; i++) {
                    // loop over the nodes of a specific sub-element
                    for (size_t j = 0; j < npel; j++) {
                        cell->GetPointIds()->SetId(j,
                                                   lconn[subdivisionConnectivities[i * npel + j]]);
                    }
                    // the new connectivity is added
                    _mesh->InsertNextCell(cell->GetCellType(), cell->GetPointIds());

#if (VTK_MAJOR_VERSION < 7) || (VTK_MAJOR_VERSION==7 && VTK_MINOR_VERSION==0)
                    mat_array->InsertNextTupleValue(&label);
#else
                    mat_array->InsertNextTypedTuple(&label);
#endif
                }
            }
        }
    }
    cell->Delete();

    return;
}

// end fo file
