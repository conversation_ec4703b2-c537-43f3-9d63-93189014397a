#ifndef SUMMIT_FUNCTION_SPACE_H
#define SUMMIT_FUNCTION_SPACE_H

#include <vector>

#include "../elements/element_set_body.h"
#include "../elements/element_set_boundary.h"
#include "../mesh/mesh.h"
#include "../summit_enum.h"
#include "../mesh/vertex.h"
#include "../mesh/internal_boundary.h"
#include "../boundaryconditions/BCfilter.h"
#include "../mesh/boundary.h"
#include "../materials/material_library.h"

#include "dof_map.h"
#include "nodal_field.h"

#include "../utils/JaggedArray.h"
#include "../utils/base_factories.h"

#include <numeric>

// get the header of utils
#define summit_fem_function_space_utils_h
#include "function_space_utils.h"
#undef summit_fem_function_space_utils_h

#ifdef WITH_GMSH
#include <gmsh/MElement.h>
#endif  // WITH_GMSH

namespace summit {
/**
 * Forward declaration
 */
class ReferencePElement;
class Boundary;
class ColoringBoundary;
class ElementSetInterfaceOneSided;
class ElementSetInterfaceTwoSided;
class Face;
class Edge;
class Checkpoint;
class ElementSetMonolithic;

/**
 * Class FunctionSpace.
 * Class build on the top of one or several summit::Mesh objects able to build
 * an interpolation (possibly of various element and formulation types) in
 * order to perform calculus (integration, differentiation, ...) on a
 * geometric domain defined by the summit::Mesh object(s)
 */
class FunctionSpace {
  public:
    /**
     * Typedef
     */
    typedef JaggedArray<int> DiscrNodesPerFace_t;
    typedef JaggedArray<int> DiscrNodesPerEdge_t;
    typedef JaggedArray<int> DiscrEdgesPerFace_t;

    /**
     * Constructor.
     * @param[in] mesh a summit::Mesh object
     * @param[in] over_integration give the value of the over integration
     */
    explicit FunctionSpace(const Mesh& mesh, const int over_integration = 0);

    /**
     * Constructor to build the object from a restart binary file.
     * @param[in] checkpoint the object used to managed restarts
     * @param[in] name the name of the group the object will be written into
     */
    FunctionSpace(Checkpoint* checkpoint, const char* name);

    /**
     * Destructor.
     */
    virtual ~FunctionSpace();

    /**
     * Overloaded operator==
     */
    bool operator==(const FunctionSpace&) const;

    /**
     * Overloaded operator==
     */
    bool operator!=(const FunctionSpace&) const;

    /**
     * Copy constructor.
     */
    FunctionSpace(const FunctionSpace& fs);

    /**
     * Overloaded operator=.
     * declared private and not implemented.
     */
    FunctionSpace& operator=(const FunctionSpace&);

    /**
     * element_set_compare
     */
    bool compare_element_sets(const std::vector<summit::ElementSetBody *> rhs) const;

  protected:
    /**
     * Default constructor.
     * declared public and not implemented.
     */
FunctionSpace();

  //private:


    ////////////////////////////////////////////////////////////////////////////////////////////////////
    //                       PUBLIC METHODS
    ////////////////////////////////////////////////////////////////////////////////////////////////////
  public:
    /**
     * Method in charge of computing an interpolation for the FunctionSpace.
     * @param[in] materialLib The MaterialLibrary needed for shell (to know the thickness)
     *            and to know the interface law number
     */
    virtual void DiscretizeBoundaryMaterialWiseDG(const MaterialLibrary& matlib,
                                                  ColoringBoundary const& boundary);

    virtual void Discretize(const MaterialLibrary& matLib, const int pid = 0);

    void DiscretizePeriGhost(double delta, int pid = 0);

    /**
     * Method in charge of performing a monolithic discretization of a bulk and an interface problem
     * @param[in] matLibBulk The MaterialLibrary of the bulk problem
     * @param[in] matLibInterface The MaterialLibrary of the interface problem
     * @param[in] interfaceBoundary The InternalBoundary defining the interface
     */
    void DiscretizeMonolithicBulkInterface(const MaterialLibrary& matLibBulk,
                                           const MaterialLibrary& matLibInterface,
                                           const InternalBoundary& interfaceBoundary);

    /**
     * Method in charge of computing an interpolation for the FunctionSpace.
     * Applied to a multimaterial library, it allows to perform a CG discretization inside
     * each material and a DG discretization only on the interfaces between different materials
     * @param[in] materialLib The MaterialLibrary needed for shell (to know the thickness)
     *            and to know the interface law number
     */
    void DiscretizeMaterialWiseDG(const MaterialLibrary& matLib,
                                  const bool ADD_INTERFACE_ELEM_SET = 0);


    /**
     * Method to get all the labels that are affected to elements of all the
     * element sets
     * @return a list of different labels
     */
    /*
    virtual ElementSet::labelSet_t
    GetLabels() const;*/

    /**
     * Method in charge of computing an interpolation for the FunctionSpace.
     * @param[in] The Material library
     * @param[in] partBoundary const pointer to a partitionBoundary object
     */
    void DiscretizeBoundary(const MaterialLibrary& matlib, Boundary const& boundaryElm);

    /**
     * Method in charge of computing an interpolation for the FunctionSpace.
     * @param[in] The Material library
     * @param[in] partBoundary const pointer to a partitionBoundary object
     * @return a vector of the neighbor index for each ElementSet
     */
    std::vector<size_t> DiscretizeBoundary(const MaterialLibrary& matlib,
                                           ColoringBoundary const& boundaryElm);

    /**
     * Method in charge of computing an interpolation for the FunctionSpace on interprocessor
     * boundaries.
     * This function generates the element sets responsible of holding all the interprocessor
     * boundary lower dimensional elements needed for the computation of the integrals involved
     * in a monolithic solver for hydraulic fracture. The interprocessor boundary elements involved
     * in this kind of problem may be of two kind:
     *  - rock interfaces (these interfaces derive from the DG discretization of the rock
     * displacement)
     *  - fluid interfaces (these interfaces are where the hydraulic fracture is, that is where the
     * fluid is allowed to flow)
     * @param[in] The Material library
     * @param[in] boundaryElm const pointer to a ColoringBoundary object
     * @param[in] filter const a reference to a filter used to distinguish the fluid interfaces from
     * the rock interfaces
     * @param[in] rankIdsFluid a vector of the neighbor index for each ElementSet on the fluid
     * interfaces
     * @return a vector of the neighbor index for each ElementSet on the rock interfaces
     */
    template <int Ndim>
    std::vector<size_t> DiscretizeBoundary(const MaterialLibrary& matlib,
                                           ColoringBoundary const& boundaryElm,
                                           BCFilter<Ndim> const& filter,
                                           std::vector<size_t>& rankIdsFluid);

    /**
     * Method in charge of computing an interpolation for the FunctionSpace.
     * @param[in] partBoundary const pointer to a partitionBoundary object
     * @param[in,out] A vector with the created element sets (one per bulk set
     *                as they can be of different polynomial order
     */
    void DiscretizeNeumannBC(Boundary const& boundaryElm, std::vector<ElementSetBoundary*>& vset);

    /**
     * Method in charge of computing an interpolation for the FunctionSpace on a boundary of the
     * interface domain (this method applies to monolithic problems, in case one wanted to apply a
     * Neumann bc on the interface variable). Notice that this method makes sense only in 3D when
     * one wants to set a Neumann bc on a 1D portion of the external boundary. Indeed, in 2D, the
     * boundary of the interface domain is 0D (i.e. one point) and the Neumann bc can be set as a
     * force in the NodalField, without the need to do any integrations.
     * @param[in] partBoundary const pointer to a partitionBoundary object
     * @param[in,out] A vector with the created element sets (one per bulk set
     *                as they can be of different polynomial order
     */
    void DiscretizeNeumannBCMonolithicInterface(Boundary const& boundaryElm,
                                                std::vector<ElementSetBoundary*>& vset);

    /**
     * Method in charge of computing an interpolation for the FunctionSpace.
     * @param[in] partBoundary const pointer to a partitionBoundary object
     * @param[in,out] A vector with the created element sets (one per bulk set
     *                as they can be of different polynomial order
     */
    void DiscretizePressureBC(Boundary const& boundaryElm, std::vector<ElementSetBoundary*>& vset);

    void Extrapolate(CommunicationManager const& commManager,
                     const ElementQuadratureField<real>& quad_field,
                     NodalField<real>& nodal_field) const;

    /**
     * Method in charge of calculating the H1 norm of a field given the nodal values:
     * sqrt(||u||^2 + ||grad(u)||^2) where ||.|| is the L2 norm
     * @param[in] nodal_field NodalField containing the field evaluated at the discretization nodes
     * @return a real number with the H1 norm of the nodal field in input
     */
    real computeH1norm(const NodalField<real>& nodal_field,
                       const summit::CommunicationManager& comMan);

    /**
     * Method in charge of calculating the L2 norm of a field given the nodal values:
     * @param[in] nodal_field NodalField containing the field evaluated at the discretization nodes
     * @return a real number with the L2 norm of the nodal field in input
     */
    real computeL2norm(const NodalField<real>& nodal_field,
                       const summit::CommunicationManager& comMan);

    /**
     * Method to offset all the initial nodal coordinates
     * by dx in direction dir if x[dir]>x_crit
     */
    void OffsetCoordinatesByX(size_t dir, real x_crit, real dx);

    /**
     * Method to write the object to a binary file.
     * @param[in] checkpoint the object used to managed restarts
     * @param[in] name the name of the group the object will be written into
     * @param[in] tag a tag applied to the main group of the class
     */
    virtual void WriteForRestart(Checkpoint* checkpoint,
                                 const char* name,
                                 const char* tag = nullptr) const;

    void ComputeSparsityPattern(const int * eqmap, int n_dof_node,    
        const CommunicationManager& commManager, std::map<int, std::set<int> >& sparsity) const;

    void ComputeSparsityPatternMonolithic(const int * eqmap, int n_nodes_bulk, int n_dof_node_bulk, 
        int n_dof_node_interface, const CommunicationManager& commManager, 
        std::map<int, std::set<int> >& sparsity) const;

    /**
     * Method to modify coordinates using a recession displacement field
     * @param[in] recessionDispField Nodal field of displacement vectors of each node
     */
    void EvolveCoordinates(const summit::NodalField<summit::real>& evolutionDispField);

    void EvolveCoordinates(const summit::NodalField<summit::real>& evolutionDispField, summit::NodalField<summit::real>& oldCoordinates);

    void ReplaceCoordinates(const summit::NodalField<summit::real>& newCoordinates);

    void ReplaceCoordinates(const summit::NodalField<summit::real>& newCoordinates, summit::NodalField<summit::real>& oldCoordinates);

    // void ReplaceCoordinates2(const summit::NodalField<summit::real>& newCoordinates);

    // void ReplaceCoordinatesAndShapeFunctions(summit::FunctionSpace& fs);

    // void ReDiscretize();
    void ReDiscretizeTetP();
    void ReDiscretizeTetP2();
    void ReDiscretizeTriP();
    void ReDiscretizeInterfaceTriP(summit::NodalField<real>& oldCoordinates);
    void ReDiscretizeInterfaceTetP(summit::NodalField<real>& oldCoordinates);

    /**
     * For C0/DG shell we need continuous interface elements.
     * Create a method to avoid code duplication between _discretizeTriPDG and _discretizeTriP
     */
    void _rediscretizeInterfaceTriP(const int tnum,
                                  Mesh::topology_t topo,
                                  const int pOrder,
                                  ElemType eType,
                                  const int nNodes_elem,
                                  const std::vector<int>& connectivity,
                                  ReferencePElement& reference_element,
                                  std::vector<int>& connecInterface,
                                  std::vector<int>& indicesVerticesInterface,
                                  std::vector<int>& indicesEdge,
                                  std::vector<int>& idElementsInterface,
                                  std::vector<int>& tailEndNodes,
                                  summit::NodalField<real>& oldCoordinates);


    void PrintVectors(std::vector<real>& vec);//, bool toFile, std::string fileName);
    void PrintVectors(std::vector<int>& vec);//, bool toFile, std::string fileName);

    /**
     * Method to find index to the element set that contains the input argument coordinates, and find the index to the element 
     * set that contains the coordinate within the element set. This method also contains the barycentric coordinates 
     * @param[in] coordinates coordinate of material point
     * @param[in] localNum index of node number in coordinates input
     * @param[out] output (index of element set, index of element, vector or barycentric coordinates)
     */
    std::tuple<int,int,std::vector<real>> FindElementIndicesAndBarycentricCoordinates(const summit::NodalField<summit::real>& coordinates, int localNum);

    /**
     * Method to interpolate at a location within an element specified by barycentric coordinates using values from a given nodal 
     * @param[in] elsetNum index of element set
     * @param[in] elNum index of element within the element set
     * @param[in] barycentricCoordinates vector of baricentric coordinates
     * @param[in] nodalField nodal field to interpolate from
     */
    std::vector<real> InterpolateFromNodalField(int elsetNum, int elNum, std::vector<real>& baryCoord, summit::NodalField<summit::real>& nodalField);

    /**
     * Method to find index to the element set that contains the input argument coordinates, and find the index to the element 
     * set that contains the coordinate within the element set. This method also contains the barycentric coordinates 
     * @param[in] coordinates coordinate of material point
     * @param[in] nodeNum index of global node number in coordinates input
     * @param[in] nodalFieldVector a vector of all nodal fields to be interpolated
     * @param[out] output a vector of all interpolated nodal fields
     */
    std::vector<summit::NodalField<summit::real>> InterpolateFromNodalField(const summit::NodalField<summit::real>& coordinates, std::vector<int>& nodeNum, std::vector<summit::NodalField<summit::real>>& nodalFieldVector);

    /**
     * Method in charge of averaging a field (strain and stress) over a given set of elements
     * identified by a label in the mesh file. By defautl all the elements
     * in the entire mesh are considered.
     */
    /*std::vector<real>
    AverageField(QuadratureField<real>& quad_field);*/

    /**
     * "pseudo-mutator" to the DoFMaps of each element set needed for band
     * optimization.
     */
    DoFMap** dof_maps();

    /**
     * Mutator to the coordinates needed for band optimization.
     */
    NodalField<real>& coordinates() { return _coordinates; }

    /**
     * Accessor to the dimension of the space
     */
    inline int dim() const;

    /**
     * Accessor to the number of nodes
     */
    inline int nodes() const;

    /**
     * Accessor to the discretized Node container per edge
     */
    inline const DiscrNodesPerEdge_t DiscrNodesPerEdge() const;

    /**
     * Accessor to the discretized Node container for an edge
     */
    inline const std::vector<int>* DiscrNodesForEdge(const int edgeIndex) const;

    /**
     * Accessor to the discretized Node container per face
     */
    inline const DiscrNodesPerFace_t DiscrNodesPerFace() const;

    /**
     * Accessor to the discretized Node container for a face
     */
    inline const std::vector<int>* DiscrNodesForFace(const int faceIndex) const;

    /**
     * Accessor to the Edge container per face
     *
     * IMPORTANT: the variable _DiscrEdgesPerFace is only initialized and sized
     *            in method FunctionSpaceInternal::Discretize()
     *      This method can FAIL if is called from FunctionSpace::Discretize()
     */
    inline const DiscrEdgesPerFace_t DiscrEdgesPerFace() const;

    /**
     * Accessor to the discretized Node container for a vertex
     */
    inline const int* DiscrNodesForVertex(const int vertexIndex) const;

    /**
     * Accessor to the multiplicity of the edge with local index localEdgeIndex
     *
     * IMPORTANT: the variable _MultiplicityEdge is only initialized and sized
     *            in method FunctionSpaceInternal::Discretize()
     *      This method can FAIL if is called from FunctionSpace::Discretize()
     */
    inline int MultiplicityEdge(const int localEdgeIndex) const;

    /**
     * Accessor to the Edge container for a face
     * @param[in] faceIndex global face Id
     *
     * IMPORTANT: the variable _DiscrEdgesPerFace is only initialized and sized
     *            in method FunctionSpaceInternal::Discretize()
     *      This method can FAIL if is called from FunctionSpace::Discretize()
     */
    inline const std::vector<int>* DiscrEdgesForFace(const int faceIndex) const;

    /**
     * Accessor to the coordinates of the nodes
     */
    inline const NodalField<real>& coordinates() const;

    /**
     * Accessor to the interface coordinates (for Monolithic solver)
     */
    inline const NodalField<real>& coordinatesInterface() const;

    /**
     * Accessor to the number of element sets
     */
    inline int nElementSets() const;

    /**
     * Accessor to the element sets
     */
    inline std::vector<ElementSetBody*> const& element_sets() const;

    /**
     * Accessor to the element sets of type T
     */
    template<typename T>
      std::vector<T *> element_sets();

    /**
     * Accessor to the monolithic element sets
     */
    inline std::vector<ElementSetMonolithic*> const& monolithic_element_sets() const;

    /**
     * Accessor to the mesh
     */
    inline const Mesh* mesh() const;

    /**
     * Accessor to the type, formulation, and order
     */
    inline Mesh::typeFormulationOrder_t typeFormulationOrder(size_t tfo) const;

    /**
     * Method that returns the nodal connectivity of a bulk element from its global id
     * @param[in] globalId global index of the element in the mesh object
     * @param[out] conn nodal connectivity of the element
     * @return a boolean true if the connectivity is returned, false otherwise
     */
    bool connectivity(int globalId, std::vector<int>& conn) const;

    /**
     * Method that allow us to store the element with globalId in the output files
     * @param[in] globalId global index of the element in the mesh object
     * @param[in] status boolean true if the element will we written in output files, false
     * otherwise
     */
    void setStatusWriteElementInOutputFiles(int globalId, bool status) const;

    /**
     * Method to get the local edge index from its global (in a mesh object) index
     * @param[in] globEdgeId global (0-based) index of an edge in a mesh object
     * @return local edge index in the function space, return -1 if global edge is not found
     *
     * IMPORTANT: the variable _localEdgeIndices is only initialized and sized
     *             in method FunctionSpaceInternal::Discretize()
     *    This method can FAIL if is called from FunctionSpace::Discretize()
     */
    int localEdgeIndex(int globEdgeId) const;

    /**
     * Accessor to global element indices of ghost elements
     */
    inline std::vector<size_t> const& ghost_elements_global_id() const;


  private:
    /**
     * Method to discretize the interface between 3D and shell
     */
    void _DiscretizeShell3D(const MaterialLibrary& matlib);

    /**
     * Method to discretize a given 3D mesh of simplicial elements with first order tetrahedra
     * following a continuous Galerkin formulation
     * @param[in] tnum index of the topology
     * @paran[in] topo topology of the input mesh
     */
    void _discretizeTet1(int tnum, Mesh::topology_t topo);

    /**
     * Method to discretize a given 3D mesh of simplicial elements with any p interpolation order
     * tetrahedra following a continuous Galerkin formulation
     * @param[in] tnum index of the topology
     * @paran[in] topo topology of the input mesh
     * @param[in] pOrder interpolation order
     */
    void _discretizeTetP(int tnum, Mesh::topology_t topo, const int pOrder);

    /**
     * Method to discretize a given 2D mesh of simplicial elements with first order triangles
     * following a continuous Galerkin formulation
     * @param[in] tnum index of the topology
     * @paran[in] topo topology of the input mesh
     */
    void _discretizeTri1(int tnum, Mesh::topology_t topo);

    /**
     * Method to discretize a given 2D mesh of simplicial elements with any p interpolation order
     * triangles following a continuous Galerkin formulation
     * @param[in] tnum index of the topology
     * @paran[in] topo topology of the input mesh
     * @param[in] pOrder interpolation order
     */
    void _discretizeTriP(int tnum,
                         Mesh::topology_t topo,
                         const int pOrder,
                         ElemType eType,
                         const MaterialLibrary& materialLib);

    /**
     * Method to discretize a given 3D mesh of simplicial elements with any p interpolation order
     * tetrahedra following a discontinuous Galerkin formulation
     * @param[in] tnum index of the topology
     * @paran[in] topo topology of the input mesh
     * @param[in] pOrder interpolation order
     * @param[in] the Material library
     */
    void _discretizeTetPDG(int tnum,
                           Mesh::topology_t topo,
                           const int pOrder,
                           const MaterialLibrary& matlib);

    /**
     * Discretizes bulk elements with DG connectivities
     * (method to avoid code duplication between _discretizeTetPDG and
     * _discretizeTetPDGMonolithicBulkInterface
     */
    void _discretizeBulkTetPDG(int tnum,
                               Mesh::topology_t topo,
                               const int pOrder,
                               const int nNodes_elem,
                               std::vector<int>& connectivity,
                               int* localConnVert,
                               ReferencePElement& reference_element,
                               const MaterialLibrary& materialLib);

    /**
     * Method to discretize a given 2D mesh of simplicial elements with first order triangles
     * following a discontinuous Galerkin formulation
     * @param[in] tnum index of the topology
     * @paran[in] topo topology of the input mesh
     * @param[in] the Material library
     */
    void _discretizeTri1DG(int tnum, Mesh::topology_t topo, const MaterialLibrary& matlib);

    /**
     * Method to discretize a given 2D mesh of simplicial elements with any p interpolation order
     * triangles following a discontinuous Galerkin formulation
     * @param[in] tnum index of the topology
     * @paran[in] topo topology of the input mesh
     * @param[in] pOrder interpolation order
     */
    void _discretizeTriPDG(int tnum,
                           Mesh::topology_t topo,
                           const int pOrder,
                           ElemType eType,
                           const MaterialLibrary& materialLib);

    /**
     * Method to discretize a given 3D mesh of simplicial elements with any p interpolation order
     * tetrahedra following a continuous Galerkin formulation for each material and a DG formulation
     * on the interfaces between different materials
     * @param[in] tnum index of the topology
     * @paran[in] topo topology of the input mesh
     * @param[in] pOrder interpolation order
     */
    void _discretizeTetPDGbyMaterials(int tnum,
                                      Mesh::topology_t topo,
                                      const int pOrder,
                                      const MaterialLibrary& matlib,
                                      const bool ADD_INTERFACE_ELEM_SET);


    /**
     * Method to discretize a given 2D mesh of simplicial elements with any p interpolation order
     * triangles following a discontinuous Galerkin formulation in the bulk and a consistent
     * continuous
     * Galerkin formulation for another problem to be solved on selected interfaces
     * @param[in] tnum index of the topology
     * @paran[in] topo topology of the input mesh
     * @param[in] pOrder interpolation order
     * @param[in] eType element type
     * @param[in] matLibBulk the Material library for the bulk problem
     * @param[in] matLibInterface the Material library for the interface problem
     * @param[in] interfaceBoundary the InternalBoundary defining the interface where to insert the
     * monolithic
     *              element. In all other internal interfaces, the standard interface elements are
     * added.
     */

    void _discretizeTriPDGMonolithicBulkInterface(int tnum,
                                                  Mesh::topology_t topo,
                                                  const int pOrder,
                                                  ElemType eType,
                                                  const MaterialLibrary& matLibBulk,
                                                  const MaterialLibrary& matLibInterface,
                                                  const InternalBoundary& interfaceBoundary);

    /**
     * Method to discretize a given 2D mesh of simplicial elements with any p interpolation order
     * triangles following a discontinuous Galerkin formulation in the bulk and a consistent
     * continuous
     * Galerkin formulation for another problem to be solved on selected interfaces
     * @param[in] tnum index of the topology
     * @paran[in] topo topology of the input mesh
     * @param[in] pOrder interpolation order
     * @param[in] eType element type
     * @param[in] matLibBulk the Material library for the bulk problem
     * @param[in] matLibInterface the Material library for the interface problem
     * @param[in] interfaceBoundary the InternalBoundary defining the interface where to insert the
     * monolithic
     *              element. In all other internal interfaces, the standard interface elements are
     * added.
     */

    void _discretizeTetPDGMonolithicBulkInterface(int tnum,
                                                  Mesh::topology_t topo,
                                                  const int pOrder,
                                                  ElemType eType,
                                                  const MaterialLibrary& matLibBulk,
                                                  const MaterialLibrary& matLibInterface,
                                                  const InternalBoundary& interfaceBoundary);

    /**
     * Method to split connectivity of a set of interface elements based on the material of
     * their neighbor bulk elements
     * @param[in] geometric dimension of the element (so 2 even for shell)
     * @param[in] polynomial order of the element
     * @param[in] connectivity initial connectivity of a set of interface elements
     * @param[in] globalElementId list of interface element id in the initial set
     * @param[in] nen number of nodes per element
     * @param[in] tnum index of the topology in the mesh
     * @param[out] connByMaterialPairs list of connectivities per material pairs
     * @param[out] globElmByMaterialPairs list of element id per material pairs
     * @param[out] connByMaterialPairs list of connectivities per material pairs
     * @param[in] indices of the left elements if !=NULL  (P order)
     * @param[out] indices of the left elements per material pairs if !=NULL  (P order)
     * @param[in] indices of the interface edge if !=NULL  (shell)
     * @param[out] indices of the interface edge per material pairs if !=NULL  (shell)
     */
    void SplitInterfacesByMaterials(
      const int dim,
      const int pOrder,
      std::vector<int> const& connectivity,
      std::vector<int> const& globalElementId,
      int nen,
      int tnum,
      std::vector<std::vector<int> >& connByMaterials,
      std::vector<std::vector<int> >& globElmByMaterials,
      std::vector<std::pair<int, int> >& MaterialPairs,
      std::vector<int> const* indicesVerticesLeft = NULL,
      std::vector<std::vector<int> >* indiceVerticesLeftByMaterials = NULL,
      std::vector<int> const* indicesEdges = NULL,
      std::vector<std::vector<int> >* interfaceEdgeByMaterials = NULL,
      std::vector<int> const* tailEndNodes = NULL,
      std::vector<std::vector<int> >* tailEndNodesByMaterials = NULL);

    void SplitMonolithicInterfacesByMaterials(
      const int dim,
      const int pOrder,
      std::vector<int> const& connectivity,
      std::vector<int> const& connectivityCG,
      std::vector<int> const& globalElementId,
      int nen,
      int nenInterface,
      int tnum,
      std::vector<std::vector<int> >& connByMaterials,
      std::vector<std::vector<int> >& connCGByMaterials,
      std::vector<std::vector<int> >& globElmByMaterials,
      std::vector<std::pair<int, int> >& MaterialPairs,
      std::vector<int> const* indicesVerticesLeft = NULL,
      std::vector<std::vector<int> >* indiceVerticesLeftByMaterials = NULL,
      std::vector<int> const* indicesEdges = NULL,
      std::vector<std::vector<int> >* interfaceEdgeByMaterials = NULL,
      std::vector<int> const* tailEndNodes = NULL,
      std::vector<std::vector<int> >* tailEndNodesByMaterials = NULL);

    /**
     * For C0/DG shell we need continuous interface elements.
     * Create a method to avoid code duplication between _discretizeTriPDG and _discretizeTriP
     */
    void _discretizeInterfaceTriP(const int tnum,
                                  Mesh::topology_t topo,
                                  const int pOrder,
                                  ElemType eType,
                                  const int nNodes_elem,
                                  const std::vector<int>& connectivity,
                                  ReferencePElement& reference_element,
                                  std::vector<int>& connecInterface,
                                  std::vector<int>& indicesVerticesInterface,
                                  std::vector<int>& indicesEdge,
                                  std::vector<int>& idElementsInterface,
                                  std::vector<int>& tailEndNodes);

    /**
     * Discretizes bulk elements with DG connectivities
     * (method to avoid code duplication between _discretizeTriPDG and
     * _discretizeTriPDGMonolithicBulkInterface
     */
    void _discretizeBulkTriPDG(int tnum,
                               Mesh::topology_t topo,
                               const int pOrder,
                               ElemType eType,
                               const int nNodes_elem,
                               std::vector<int>& connectivity,
                               ReferencePElement& reference_element,
                               const MaterialLibrary& materialLib);

    /**
     * This method does the very same operations of the method above but only on the subset
     * InternalBoundary of the whole internal boundary. This method is needed, e.g., if one
     * plans to insert the interface elements only on selected interfaces.
     * This method may actually substitute the method above, even though it may not be faster
     * in case the InternalBoundary coincides with the whole internal boundary (to be checked!)
     * BG: For the reasons above, I leave the two implementations of the same method.
     *     The proper working of this method is shown under the flag DISCRETIZE_SELECTED_INTERFACES.
     */
    void _discretizeInterfaceTriP(const int tnum,
                                  Mesh::topology_t topo,
                                  const int pOrder,
                                  ElemType eType,
                                  const int nNodes_elem,
                                  const std::vector<int>& connectivity,
                                  ReferencePElement& reference_element,
                                  std::vector<int>& connecInterface,
                                  std::vector<int>& indicesVerticesInterface,
                                  std::vector<int>& indicesEdge,
                                  std::vector<int>& idElementsInterface,
                                  std::vector<int>& tailEndNodes,
                                  const InternalBoundary* interfaceBoundary);

    void _discretizeInterfaceTetP(const int tnum,
                                  Mesh::topology_t topo,
                                  const int nNodes_elem,
                                  const std::vector<int>& connectivity,
                                  std::vector<int>& connecInterface,
                                  std::vector<int>& indicesVerticesInterface,
                                  std::vector<int>& globalFaceId,
                                  const summit::InternalBoundary* interfaceBoundary);

    void _discretizeTriTetDG(const summit::ElementSetBody* set1,
                             const summit::ElementSetBody* set2,
                             const MaterialLibrary& matlib);

    void _discretizeTriPERI(int tnum, Mesh::topology_t topo, const int pOrder, ElemType eType);
    void _discretizeTetPERI(int tnum, Mesh::topology_t topo, const int pOrder);

    void _discretizeQuadPERI(int tnum, int pid = 0);
    void _discretizeQuadGHOST(int tnum, double delta, int pid = 0);
    void _discretizeTetPERI(int tfo, int pid = 0);
    void _discretizeTetGHOST(int tfo, double delta, int pid = 0);
    void _discretizeHexPERI(int tnum, int pid = 0);
    void _discretizeHexGHOST(int tnum, double delta, int pid = 0);

    /**
     * Method for discretizing with beam elements
     * @param[in] tnum index of topology
     * @param[in] topo topology of the input mesh
     * @param[in] pOrder interpolation order (Note that only 3, 5, 7, ..., are possible since a
     * Hermitian interpolation is used.)
     * @param[in] eType the element type (ELEM_BTF, ELEM_BKL, ELEM_BSR)
     * @param[in] materialLib the material library
     */
    void _discretizeBeam(int const tnum,
                         Mesh::topology_t topo,
                         const int pOrder,
                         ElemType eType,
                         const MaterialLibrary& materialLib);

    void _discretizeBeamDGMaterialWise(int const tnum,
                                       Mesh::topology_t topo,
                                       const int pOrder,
                                       ElemType eType,
                                       const MaterialLibrary& materialLib);

    bool _checkBeamStraightness(std::vector<int> connectivity_vertices);

//-----------------------------------------------------------------------------------------------------------
//                       ifdef DOXYGEN_NO_GMSH
#ifndef DOXYGEN_NO_GMSH
    /**
     * Method to discretize a gmsh mesh of shell elements
     * @param[in] tnum index of the topology
     * @param[in] dg bool true  if DG
     * @param[in] eType enum to give the element type
     * @param[in] the Material library
     */
    void _discretizeShlGmsh(const int tnum,
                            const bool dg,
                            ElemType eType,
                            const MaterialLibrary& materialLib);

    /**
     * Method to discretize a gmsh mesh of shell elements
     * @param[in] tnum index of the topology
     * @param[in] dg bool true  if DG
     * @param[in] the material library
     */
    void _discretize3DGmsh(const int tnum, const bool dg, const MaterialLibrary& materialLib);


    /**
     * discretize gmsh mesh of DG mindlin plate elements
     * @param[in] tnum index of topology
     * @param[in] the material library
     */
    void _discretizePlateRM(int const tnum, MaterialLibrary const& materialLib);


    /**
     * discretize gmsh mesh of DG mindlin shell elements
     * @param[in] tnum index of topology
     * @param[in] the material library
     */
    void _discretizeShellRM(int const tnum,
                            MaterialLibrary const& materialLib,
                            bool nonlinear_kinematics = false);


    void _discretizeTriTetDGGmsh(const summit::ElementSetBody* set1,
                                 const summit::ElementSetBody* set2,
                                 const MaterialLibrary& matlib);

    //-----------------------------------------------------------------------------------------------------------
    //                       ifdef WITH_GMSH

#ifdef WITH_GMSH
  public:
    void discretizeBoundaryGmsh(const MaterialLibrary& matlib,
                                const std::vector<MElement*>& vInterface,
                                const std::vector<MElement*>& vBulkElm,
                                const std::vector<int>& vEdgeNum,
                                BC_TYPE bcType);

  private:
    std::vector<size_t> _DiscretizeBoundaryGmsh(const MaterialLibrary& matlib,
                                                ColoringBoundary const& boundary);


  protected:
    /**
     * Method in charge of computing an interpolation for the FunctionSpace.
     * @param[in] partBoundary const pointer to a partitionBoundary object
     * @param[in,out] A vector with the created element sets (one per bulk set
     *                as they can be of different polynomial order
     */
    void _DiscretizeNeumannBCGmsh(const ElementSetBody* eleset,
                                  const Boundary& boundary,
                                  Boundary::MeshEntityContainer_t& scratchBoundaryElm,
                                  std::vector<ElementSetBoundary*>& vset);


    /**
     * Method in charge of computing an interpolation for the FunctionSpace.
     * @param[in] partBoundary const pointer to a partitionBoundary object
     * @param[in,out] A vector with the created element sets (one per bulk set
     *                as they can be of different polynomial order
     */
    void _DiscretizePressureBCGmsh(const ElementSetBody* eleset,
                                   const size_t dimBoundary,
                                   Boundary::MeshEntityContainer_t& scratchBoundaryElm,
                                   std::vector<ElementSetBoundary*>& vset);


    /**
     * Gmsh Version of the previous method
     */
    void _DiscretizeLubricationGmsh(const size_t topo,
                                    const int materialLabel,
                                    const MaterialLibrary& matlib,
                                    const std::vector<int>& mechanicalElementId,
                                    const ElementSetInterfaceTwoSided* mech_set,
                                    const std::vector<int>& conn_mech_inter);

    /**
     * Method to add lubrication element to their set
     * @param[in] The lubrication bulk element set to which the element should be added
     * @param[in] The material Label of this lubrication set (1-based index)
     * @param[in] The material library
     * @param[in] A vector with the mechanical element id of linked to each lubrication element
     * @param[in] The mechanical element set with the interface where the lubrication is computed
     * @param[in] A vector with all the lubrication 2D elements
     * @param[in] The mechanical connectivities of each interface
     * @param[in] The mechanical coordinates
     */

    void _AddLubricationElementGmsh(ElementSetBody* fluid_set,
                                    const MaterialLibrary& matlib,
                                    const std::vector<int>& mechanicalElementId,
                                    const ElementSetInterfaceOneSided* mech_set,
                                    const NodalField<real>& mechanical_coordinates);

#endif  // WITH_GMSH
    //                       endif WITH_GMSH
    //-----------------------------------------------------------------------------------------------------------

#endif  // DOXYGEN_NO_GMSH
        //                       endif DOXYGEN_NO_GMSH
        //-----------------------------------------------------------------------------------------------------------


    //////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //                       PUBLIC METHODS
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////

  public:
    /**
     * Method to create the lubrication discretization from the mechanical one
     * @param[in] topo The topology number of the mechanical element set
     * @param[in] materialLabel The material Label of this lubrication set (1-based index)
     * @param[in] matlib The material library
     * @param[in] mechanicalElemetId vector containing the index in the element set mech_set of the
     *            interface mechanics element that are cracked
     * @param[in] mech_Set The mechanical element set with the interface where the lubrication is
     *            computed
     * @param[in] conn_mech_inter The mechanical connectivities of each interface
     *            you already have this information when you have the full mechanical element set
     *            and the list of cracked elements
     * @param[in] mechanical_coordinates mechanical coordinates of all the nodes in the 3D mesh
     */
    void DiscretizeLubrication(const size_t topo,
                               const int materialLabel,
                               const MaterialLibrary& matlib,
                               const std::vector<int>& mechanicalElementId,
                               const ElementSetInterfaceTwoSided* mech_set,
                               const std::vector<int>& conn_mech_inter,
                               NodalField<real> const& mechanical_coordinates);

    /**
     * Method to add lubrication element to their set
     * @param[in] The lubrication bulk element set to which the element should be added
     * @param[in] The material Label of this lubrication set (1-based index)
     * @param[in] The material library
     * @param[in] A vector with the mechanical element id of linked to each lubrication element
     * @param[in] The mechanical element set with the interface where the lubrication is computed
     * @param[in] A vector with all the lubrication 2D elements
     * @param[in] The mechanical connectivities of each interface
     * @param[in] The mechanical coordinates
     */
    void AddLubricationElement(ElementSetBody* fluid_set,
                               const MaterialLibrary& matlib,
                               const std::vector<int>& mechanicalElementId,
                               const ElementSetInterfaceOneSided* mech_set,
                               const NodalField<real>& mechanical_coordinates);

    void InteriorPoints(const Edge& edge, std::vector<SpatialSort::coord_t>& nodes) const;

    void InteriorPoints(const Face& face, std::vector<SpatialSort::coord_t>& nodes) const;

    inline void SetLabel(std::string label)
    {
        _label = label;
        return;
    }

    inline std::string GetLabel() const { return _label; }

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //                       PRIVATE METHODS
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////

  private:
    /**
     * Template function on Tdim (dimension of element) and Sdim (dimension of space) which might
     * be different in the case of a triangle in 3D setting, to identify the connectivity indices
     * of high ordre triangle
     * @param[in]  vector with the mesh coordinates
     * @param[in]  number of nodes in the element
     * @param[in]  vector with the corner vertices of the first order element
     * @param[in]  array with the connectivity of the triangle
     * @param[out] vector with the indices corresponding to the vertices
     * @param[out] vector with the indices corresponding to the vertices in the reference element
     */
    template <int Tdim, int Sdim>
    void identifyCornerIndicesHighOrder(NodalField<real> const& coordinates,
                                        const int nen,
                                        std::vector<const real*> const& ver,
                                        const int* connectivity,
                                        std::vector<int>& ind,
                                        std::vector<int>& refind) const;

    /**
     * Same as identifyCornerIndicesHighOrder in the special case where Tdim=Sdim
     */
    template <int Tdim>
    void identifyCornerIndicesHighOrder(NodalField<real> const& coordinates,
                                        const int nen,
                                        std::vector<const real*> const& ver,
                                        const int* connectivity,
                                        std::vector<int>& ind,
                                        std::vector<int>& refind) const;

    /**
     * Method to compute the coordinates of the points belonging to the interior of a bulk
     * @param dim spatial dimension
     * @param nNodesIntBulk_elem number of nodes in the interior of the bulk
     * @param v1 first vertex of the bulk (physical space)
     * @param v2 second vertex of the bulk (physical space)
     * @param v3 third vertex of the bulk (physical space)
     * @param v4 fourth vertex of the bulk (physical space)
     * @param coord_IntBulk_ref_elem coordinates of the interior points in the isoparametric space
     *        (reference element)
     * @param coordNodes_IntBulk coordinates of the interior points in the physical space (OUTPUT)
     */
    void _CalculateInteriorPointsBulk(const int dim,
                                      const int nNodesIntBulk_elem,
                                      Vertex v1,
                                      Vertex v2,
                                      Vertex v3,
                                      Vertex v4,
                                      const std::vector<double> coord_IntBulk_ref_elem,
                                      std::vector<double>& coordNodes_IntBulk);

    /**
     * Method to reorder the indices of the nodes belonging to the edges of a face
     *    (in a tetrahedron defined by the vertices 1234, the six ordered edges are 12, 13, 14, 23,
     *     24 and 34)
     * @param nNodesEdge_elem number of nodes in an edge
     * @param verticesConnectivityBulk oriented connectivity of the vertices of a bulk (tetrahedron)
     * @param indicesEdgeBoundaryBulk container of the indices of the nodes of the edges (INPUT)
     *                                ordered container of the indices of nodes of the edges
     *                                (OUTPUT)
     */
    void _OrientedEdgeConnectivityForGivenBulk(const int nNodesEdge_elem,
                                               std::vector<int> verticesConnectivityBulk,
                                               std::vector<int>& indicesEdgeBoundaryBulk);

    /**
     * Method to reorder the indices of the nodes belonging to the faces of a bulk
     *    (in a tetrahedron defined by the vertices 1234, the four ordered faces are 123, 124, 134,
     *     234)
     * @param pOrder polynomial order of the interpolant
     * @param nNodesFace_elem number of nodes in a face
     * @param verticesConnectivityBulk oriented connectivity of the vertices of a bulk (tetrahedron)
     * @param indicesFaceBoundaryBulk container of the indices of the nodes of the faces (INPUT)
     *                                ordered container of the indices of nodes of the faces
     *                                (OUTPUT)
     */
    void _OrientedFaceConnectivityForGivenBulk(const int pOrder,
                                               const int nNodesFace_elem,
                                               std::vector<int> verticesConnectivityBulk,
                                               std::vector<int>& indicesFaceBoundaryBulk);
    /**
     * Method to identify the 3D elements linked to a shell onr
     * in case of a hybrid interface
     * @param[in] tg1 tangent vector of the shell interface edge
     * @param[in] nor normal director of the shell interface edge
     * @param[in] c0 starting nodes of the shell interface edge
     * @param[in] thickness Shell thickness of the element
     * @param[in] boundaryFaces vector with the data for all boundary face of 3D elements
     * @param[out] vrightTetra return the index and the face number of all the tetra
     *             linked to the shell
     */
    void _identify3DelementLinkedShell(const real* tg1,
                                       const real* nor,
                                       const real* c0,
                                       const real thickness,
                                       const std::vector<faceStructData>& boundaryFaces,
                                       std::vector<std::pair<int, int> >& vrightTetra) const;

    /**
     * Method to set the data needed for 1 hybrid shell/3D interface element
     * @param[in] eleset2D the shell element set
     * @param[in] eleset3D the 3D element set
     * @param[in] thickness the shell thickness
     * @param[in] the reference element (if Gmsh based element_set NULL has to be given for this
     *                                   argument)
     * @param[in] vrightTetra the index and the face number of all the tetra linked to the shell
     * @param[in] boundaryFaces vector with the data for all boundary face of 3D elements
     * @param[in] elem2Dnum number of the shell element
     * @param[in] nor normal director of the shell interface edge
     * @param[in] vver three pointer each one corresponds to the coordinates (x,y,z) of the
     *            3 first order nodes triangular shell element (Can be empty for Gmsh based
     *            element set)
     * @param[in,out] connecInterfaceShell the interface connectivity for shell side element set
     * @param[in,out] connecInterface3D the interface connectivity for 3D side element set
     * @param[in,out] a vthickShell vector with the shell thickness for each shell element
     * @param[in,out] a vthick3D vector with the shell thickness for each 3D element
     * @param[in,out] vnconnByInter the number of connectivity for each hybrid interface
     * @param[in,out] vtetraByInter the number of 3D element included in the hybrid interface
     * @param[in,out] vNormal3DFace normal to the 3D face (once for all 3D elements as
     *                              it is a planar face)
     * @param[in,out] interElmShell the current interface element number shell side. It is
     *            incremented by the method!
     * @param[in,out] interElm3D the current interface element number 3D side
     * @param[in,out] indicesVerticesLeftTriangleShell the index of the vertice of the shell
     *                element
     * @param[in,out] indicesVerticesInterfaceEdge indices of the vertices of the interface edge
     * @param[in,out] the tail and end first order node local indices of each interface edge
     * THE FOLLOWING ARGUMENTS ARE USELESS FOR GMSH BASED ELEMENT SET
     */

    void _setOneElementHybridInterface(const ElementSet* eleset2D,
                                       const ElementSet* eleset3D,
                                       const real thickness,
                                       const ReferencePElement* ref_ele,
                                       const std::vector<std::pair<int, int> >& vrightTetra,
                                       const std::vector<faceStructData>& boundaryFaces,
                                       const int elem2Dnum,
                                       const real* nor,
                                       const std::vector<const real*>& vver,
                                       std::vector<int>& connecInterfaceShell,
                                       std::vector<int>& connecInterface3D,
                                       std::vector<real>& vthickShell,
                                       std::vector<real>& vthick3D,
                                       std::vector<int>& vnconnByInter,
                                       std::vector<std::vector<int> >& vtetraByInter,
                                       std::vector<real>& vNormal3DFace,
                                       int& interElmShell,
                                       int& interElm3D,
                                       std::vector<int>& indicesVerticesLeftTriangleShell,
                                       std::vector<int>& indicesVerticesInterface3D,
                                       std::vector<int>& indicesVerticesInterfaceEdge,
                                       std::vector<int>& tailEndNodes) const;

  private:
    static Register<FunctionSpace, FunctionSpace> reg;

    //////////////////////////////////////////////////////////////////////////////////////////////////////////////
    //                       PROTECTED METHODS
    //////////////////////////////////////////////////////////////////////////////////////////////////////////////

  protected:
    /**
     * Method to compute the coordinates of the points belonging to the interior of an edge
     * @param dim spatial dimension
     * @param nNodesIntEdge_elem number of nodes in the interior of the edge
     * @param v1 first vertex of the edge (physical space)
     * @param v2 second vertex of the edge (physical space)
     * @param coord_IntEdge_ref_elem coordinates of the interior points in the isoparametric space
     *        (reference element)
     * @param coordNodes_IntEdge coordinates of the interior points in the physical space (OUTPUT)
     */
    void _CalculateInteriorPointsEdge(const int dim,
                                      const int nNodesIntEdge_elem,
                                      Vertex v1,
                                      Vertex v2,
                                      const std::vector<double> coord_IntEdge_ref_elem,
                                      std::vector<double>& coordNodes_IntEdge) const;

    /**
     * Method to compute the coordinates of the points belonging to the interior of a face
     * @param dim spatial dimension
     * @param nNodesIntFace_elem number of nodes in the interior of the face
     * @param v1 first vertex of the face (physical space)
     * @param v2 second vertex of the face (physical space)
     * @param v2 third vertex of the face (physical space)
     * @param coord_IntFace_ref_elem coordinates of the interior points in the isoparametric space
     *        (reference element)
     * @param coordNodes_IntFace coordinates of the interior points in the physical space (OUTPUT)
     */
    void _CalculateInteriorPointsFace(const int dim,
                                      const int nNodesIntFace_elem,
                                      Vertex v1,
                                      Vertex v2,
                                      Vertex v3,
                                      const std::vector<double> coord_IntFace_ref_elem,
                                      std::vector<double>& coordNodes_IntFace) const;

    /**
     * Method to reorder the indices of the nodes belonging to the edges of a face
     *    (in a triangle defined by vertices 123, the ordered edges are 12, 13, and 23)
     * @param nNodesEdge_elem number of nodes in an edge
     * @param verticesConnectivityFace oriented connectivity of the vertices of a face (triangle)
     * @param indicesEdgeBoundaryFace container of the indices of the nodes of the edges (INPUT)
     *                                ordered container of the indices of nodes of the edges
     *                                (OUTPUT)
     */
    void _OrientedEdgeConnectivityForGivenFace(const int nNodesEdge_elem,
                                               std::vector<int> verticesConnectivityFace,
                                               std::vector<int>& indicesEdgeBoundaryFace);

    /**
     * Method to split connectivity of a set of elements based on their material
     * @param[in] connectivity initial connectivity of a set of elements
     * @param[in] globalElementId list of element id in the initial set
     * @param[in] nen number of nodes per element
     * @param[in] tnum index of the topology in the mesh
     * @param[out] connByMaterials list of connectivities per materials
     * @param[out] globElmByMaterials list of element id per materials
     * @param[out] globIndexOfMaterials list material indices
     */
    void SplitByMaterials(std::vector<int> const& connectivity,
                          std::vector<int> const& globalElementId,
                          int nen,
                          int tnum,
                          std::vector<std::vector<int> >& connByMaterials,
                          std::vector<std::vector<int> >& globElmByMaterials,
                          std::vector<int>& globIndexOfMaterials);

    /**
     * Method to add a new ElementSet* to _element_sets.
     * It is a simple push_back(). Hence, this method could be removed.
     */
    void _addElementSet(ElementSetBody* element_set);

    /**
     * Method to add a new ElementSet* to _monolithic_element_sets.
     * It is a simple push_back(). Hence, this method could be removed.
     */
    void _addMonolithicElementSet(ElementSetMonolithic* element_set);

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    //                       PROTECTED DATA MEMBERS
    ////////////////////////////////////////////////////////////////////////////////////////////////////
  protected:
    /**
     * Pointer to a summit::Mesh object
     */
    const Mesh* _mesh;

    /**
     * Material coordinates of problem mesh
     */
    NodalField<real> _coordinates;

    /**
     * Material coordinates of interfaces (for Monolithic problems)
     */
    NodalField<real> _coordinatesInterface;

    /**
     * Container of the nodes belonging to the discretization per edge
     */
    DiscrNodesPerEdge_t _DiscrNodesPerEdge;

    /**
     * Container of the nodes belonging to the discretization per vertex
     * This is actually a vector that associates to the vertex index the corresponding global index
     * of the node
     * NOTE: this container is going to be needed when filling the Communication Maps of the
     * NodalCommunicationManager
     *       in case of a parallel implementation of the monolithic solver for hydraulic fracture.
     *       Notice that, in the regular case in which we need a NodalCommunicationManager
     *       when using CG finite elements, we don't need this container, as in that case the
     * connectivity of a vertex coincides
     *       with its own index.
     */
    std::vector<int> _DiscrNodesPerVertex;

    /**
     * Container of the nodes belonging to the discretization per edge
     *
     * IMPORTANT: the variable _DiscrEdgesPerFace is only initialized and sized
     *            in method FunctionSpaceInternal::Discretize()
     *            It can FAIL if is called from FunctionSpace::Discretize()
     */
    DiscrEdgesPerFace_t _DiscrEdgesPerFace;

    /**
     * Container of the edge multiplicity
     *
     * IMPORTANT: the variable _MultiplicityEdge is only initialized and sized
     *            in method FunctionSpaceInternal::Discretize()
     *            It can FAIL if is called from FunctionSpace::Discretize()
     */
    std::vector<int> _MultiplicityEdge;

    /**
     * Vector from global edge index (as found in a mesh object) to local edge index (only the
     * one that are actually discretized in the function space internal). If a global edge
     * index is not mapped to any local edge index, then its entry in the vector is -1.
     *
     * IMPORTANT: the variable _localEdgeIndices is only initialized and sized
     *            in method FunctionSpaceInternal::Discretize()
     *            It can FAIL if is called from FunctionSpace::Discretize()
     */
    std::vector<int> _localEdgeIndices;

    /**
     * Vector from global node index (as found in a mesh object) to local node index (only the
     * one that are actually discretized in the function space internal). If a global node
     * index is not mapped to any local node index, then its entry in the vector is -1.
     *
     * IMPORTANT: the variable _localNodeIndices is only initialized and sized
     *            in method FunctionSpaceInternal::Discretize()
     *            It can FAIL if is called from FunctionSpace::Discretize()
     */
    std::vector<int> _localNodeIndices;

    /**
     * Attribute to store the type, formulation, and order of the discretization
     */
    std::vector<Mesh::typeFormulationOrder_t> _meshTypeFormulationOrder;

    ////////////////////////////////////////////////////////////////////////////////////////////////////
    //                       PRIVATE DATA MEMBERS
    ////////////////////////////////////////////////////////////////////////////////////////////////////
  private:
    /**
     * Spatial dimension of the mesh coordinates
     */
    int _dim;

    /**
     * Over integration constant, increases quadrature integration order
     */
    int _over_integration;

    /**
     * Container of the nodes belonging to the discretization per face
     */
    DiscrNodesPerFace_t _DiscrNodesPerFace;

    /**
     * List of all element sets
     */
    std::vector<ElementSetBody*> _element_sets;
    std::vector<ElementSetMonolithic*> _monolithic_element_sets;
    // std::vector<ElementSetBoundary*> _boundary_sets;

    /**
     * Map between an element set and its boundary interface elements
     * Stored in order to be able to further extend the domain
     * FILLED ONLY by DiscretizeLubrication
     */
    std::map<ElementSet*, std::map<size_t, size_t> > _mapSet2BoundaryInterfaces;

    /**
     * Global element indices of ghost elements, if any.
     */
    std::vector<size_t> _ghost_elements_global_id;

    /*
     * label of function space in checkpoint file
     */
    std::string _label;
};

}  // namespace summit

// get the inline definitions
#define summit_fem_function_space_icc
#include "function_space.icc"
#undef summit_fem_function_space_icc

#endif  // SUMMIT_FUNCTION_SPACE_H

// end of file
