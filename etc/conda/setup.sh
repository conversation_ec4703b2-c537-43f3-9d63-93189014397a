#!/bin/bash

# Bootstrap SUMMIT conda environment

ENV_NAME="summit-dev"
ENV_FILE="etc/conda/environment.yml"

echo "[INFO] Creating or updating conda environment: $ENV_NAME"
conda env create -f $ENV_FILE || conda env update -f $ENV_FILE

echo "[INFO] Activating environment: $ENV_NAME"
source "$(conda info --base)/etc/profile.d/conda.sh"
conda activate $ENV_NAME

echo "[DONE] Environment '$ENV_NAME' is ready."
