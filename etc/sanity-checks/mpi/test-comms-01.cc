#include <mpi.h>
#include <iostream>
#include <string>

int main(int argc, char** argv) {
  // Initialize MPI
  MPI_Init(&argc, &argv);

  // Get the number of processors and the rank of this processor
  int num_procs, rank;
  MPI_Comm_size(MPI_COMM_WORLD, &num_procs);
  MPI_Comm_rank(MPI_COMM_WORLD, &rank);

  // Get the hostname of this processor
  char processor_name[MPI_MAX_PROCESSOR_NAME];
  int name_len;
  MPI_Get_processor_name(processor_name, &name_len);

  // Print the hostname and rank of this processor to stdout
  std::cout
    << "Number of processors: " << num_procs
    << ", Rank: " << rank << ", Processor name: " << processor_name << std::endl;

  // Finalize MPI
  MPI_Finalize();
  return 0;
}
