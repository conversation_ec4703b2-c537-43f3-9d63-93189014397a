CXX = mpicxx
CXXFLAGS = -std=c++11 -Wall -Wextra -O2
LDFLAGS =

SRCS = $(wildcard *.cc)
OBJS = $(SRCS:.cc=.o)
EXES = $(SRCS:.cc=)

.PHONY: all clean

all: $(EXES)

%.o: %.cc
	$(CXX) $(CXXFLAGS) -c $< -o $@

%: %.o
	$(CXX) $(CXXFLAGS) $< $(LDFLAGS) -o $@

clean:
	rm -f $(OBJS) $(EXES)

run: all
	@echo "Running all tests..."
	$(foreach exe,$(EXES),echo "Running $(exe)..."; mpirun -np 2 $(exe); echo "\n";)

.PHONY: run
