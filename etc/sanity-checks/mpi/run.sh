#!/bin/bash

#SBATCH --job-name=mpi_tests
#SBATCH --nodes=2
#SBATCH --ntasks-per-node=1
#SBATCH --output=mpi_tests_%j.out
#SBATCH --error=mpi_tests_%j.err

# Load MPI module
module load openmpi/4.1.5-gcc-11.3.1

# Compile all executables
make

# Run each executable with 2 processes
echo "Running MPI tests..."
for exe in test-comms-01 test-comms-02 test-comms-03 test-comms-04; do
    echo "Running $exe..."
    mpirun -np 2 ./$exe
    echo "Finished $exe"
    echo "-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-""-"
done

# Create notification message
jsonData='{"text": "MPI tests completed!\nJob ID: $SLURM_JOB_ID\nNode List: $SLURM_JOB_NODELIST\nDuration: $SECONDS seconds"}'

echo "Job completed. Details:"
echo "Job ID: $SLURM_JOB_ID"
echo "Job Name: $SLURM_JOB_NAME"
echo "Node List: $SLURM_JOB_NODELIST"
echo "Number of Nodes: $SLURM_NNODES"
echo "CPUs per Node: $SLURM_CPUS_ON_NODE"
echo "Tasks per Node: $SLURM_TASKS_PER_NODE"
echo "Process ID: $SLURM_PROCID"
echo "Array Task ID: $SLURM_ARRAY_TASK_ID"
echo "Submit Directory: $SLURM_SUBMIT_DIR"
echo "CPUs per Node (user specified): $SLURM_JOB_CPUS_PER_NODE"

