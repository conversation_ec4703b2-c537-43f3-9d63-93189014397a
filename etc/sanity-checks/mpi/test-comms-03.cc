#include <iostream>
#include <mpi.h>

int main(int argc, char** argv) {
    MPI_Init(&argc, &argv);

    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);

    int recv_data, send_data = rank;

    if (rank == 0) {
        MPI_Recv(&recv_data, 1, MPI_INT, size - 1, 0, MPI_COMM_WORLD, MPI_STATUS_IGNORE);
        std::cout << "Rank " << rank << " received message from Rank " << size - 1 << ": " << recv_data << std::endl;
    }

    MPI_Send(&send_data, 1, MPI_INT, (rank + 1) % size, 0, MPI_COMM_WORLD);

    if (rank == size - 1) {
        MPI_Recv(&recv_data, 1, MPI_INT, 0, 0, MPI_COMM_WORLD, MPI_STATUS_IGNORE);
        std::cout << "Rank " << rank << " received message from Rank 0: " << recv_data << std::endl;
    }

    MPI_Finalize();
    return 0;
}
