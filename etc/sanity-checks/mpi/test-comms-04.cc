#include <iostream>
#include <mpi.h>

int main(int argc, char** argv) {
    MPI_Init(&argc, &argv);

    int worldSize, rank;
    MPI_Comm_size(MPI_COMM_WORLD, &worldSize);
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);

    int sendData = rank + 1;
    int* recvData = new int[worldSize];

    MPI_Allgather(&sendData, 1, MPI_INT, recvData, 1, MPI_INT, MPI_COMM_WORLD);

    int sum = 0;
    for (int i = 0; i < worldSize; ++i) {
        sum += recvData[i];
    }

    // add an assertion to check the sum
    int expectedSum = (worldSize * (worldSize + 1)) / 2; // Sum of first n natural numbers
    if (sum != expectedSum) {
        std::cerr << "Error: Expected sum " << expectedSum << ", but got " << sum << std::endl;
        MPI_Abort(MPI_COMM_WORLD, 1);
    }

    std::cout << "Process " << rank << " sum: " << sum << std::endl;

    delete[] recvData;

    MPI_Finalize();

    return 0;
}
