#include <mpi.h>
#include <iostream>
#include <vector>

int main(int argc, char** argv) {
  // Initialize MPI
  MPI_Init(&argc, &argv);

  // Get the number of processors and the rank of this processor
  int num_procs, rank;
  MPI_Comm_size(MPI_COMM_WORLD, &num_procs);
  MPI_Comm_rank(MPI_COMM_WORLD, &rank);

  // Create a large vector
  const int vector_size = 1000000;
  std::vector<double> my_vector(vector_size);
  for (int i = 0; i < vector_size; ++i) {
    my_vector[i] = i + rank * vector_size;
  }

  // Compute the local sum of the vector components
  double local_sum = 0.0;
  for (int i = 0; i < vector_size; ++i) {
    local_sum += my_vector[i];
  }

  // Compute the global sum of the vector components using MPI_Reduce
  double global_sum;
  MPI_Reduce(&local_sum, &global_sum, 1, MPI_DOUBLE, MPI_SUM, 0, MPI_COMM_WORLD);

  // Print the global sum on processor 0
  if (rank == 0) {
    std::cout << "Global sum: " << global_sum << std::endl;
  }

  // Finalize MPI
  MPI_Finalize();
  return 0;
}
