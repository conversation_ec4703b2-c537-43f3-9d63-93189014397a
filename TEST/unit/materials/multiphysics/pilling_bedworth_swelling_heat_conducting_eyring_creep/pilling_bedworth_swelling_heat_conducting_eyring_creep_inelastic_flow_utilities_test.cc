// -*- C++ -*-
//
// Summit Development Team
// (c) 2023 All Rights Reserved
//

#include <cstdio>
#include <cmath>
#include <random>
#include <fstream>
#include <string>
#include <gtest/gtest.h>

#include <summit/materials/material_library.h>
#include <summit/materials/multiphysics/pilling_bedworth_swelling_heat_conducting_eyring_creep/PillingBedworthSwellingHeatConductingEyringCreep.h>

/**
 * @brief Utilities test for PillingBedworthSwellingHeatConductingEyringCreep material model
 * 
 * This test verifies that the helper functions implemented in the
 * PillingBedworthSwellingHeatConductingEyringCreep material model are correct
 */
TEST(basic_test, PillingBedworthSwellingHeatConductingEyringCreep)
{
    // Create PillingBedworthSwellingHeatConductingEyringCreep material
    summit::PillingBedworthSwellingHeatConductingEyringCreep material("PillingBedworthSwellingHeatConductingEyringCreep");
    
    // Material file
    std::string filename = "material_pilling_bedworth_swelling_heat_conducting_eyring_creep.dat";
    std::ifstream source(filename.c_str());
    
    // Extract the line of the material file
    std::string line;
    std::getline(source, line);
    
    // Load the material
    material.Load(filename, line);

    // Display material properties
    material.Display();

    real eta = 0.0;
    real theta = 1.0e3;
    material.getEtaFromTheta(theta, eta);

    real f = 0.1;
    real ratio = 0.0001;
    real constants = 1.0;
    real error = 0;
    real Derror = 0;    
    material.creepStrengthRelation(f, ratio, constants, error, Derror);

    // Check that eta is positive
    ASSERT_GT(eta, -1e-16) ;
    // Check that flow rule error is correct
    ASSERT_GT(error, 0.89) ;
    ASSERT_LT(error, 0.91) ;
    // All done
    return;
}

/**
 * @brief Main function to run the tests
 */
int main(int argc, char** argv)
{
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}