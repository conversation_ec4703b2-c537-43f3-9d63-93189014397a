#include <cstdio>
#include <cmath>
#include <gtest/gtest.h>

#include <summit/materials/material_library.h>
#include <summit/materials/material_consistency_test.h>
#include <summit/materials/multiphysics/armero_simo/ArmeroSimoThermal.h>

// helper function for error calculation
real error(real A, real A_num)
{
    // if A is too small
    if (fabs(A) < 1.e-12) {
        // use absolute error
        return fabs(A - A_num);
    }
    else {
        // use relative error
        return fabs((A - A_num) / A);
    }
}

TEST(source_consistency_test, ArmeroSimo)
{
    // all arguments in this model default to T = 1000 K, and ZERO deformation
    // due to thermal expansion, this produces compressive hydrostatic stress on the order of 35 MPa
    // at start up time, alternative boundary conditions can be imposed on the primal fields, and this default initial condition
    // can be removed by a sufficient number of data transfers and solves

    // material density
    summit::ArmeroSimoThermal material("ArmeroSimo");
    // material file
    std::string filename = "./material_armero_simo.dat";
    std::ifstream source(filename.c_str());
    // extract the line of the material file
    std::string line;
    std::getline(source, line);
    // load the material
    material.Load(filename, line);

    // display material properties
    material.Display();
    // spatial dimension
    const int ndm = 3;
    // tolerance for consistency test
    const real tol = 1e-6;
    // perturbation for consistency test
    const real pert = 0.1;
    real phi = 0.0;// this implies the reference phi of: 2.834627987995586e5
    real phi0 = phi;
    // no gradients
    real dphi[ndm] = {0. };
    real dphi0[ndm] = {0. };

    // set interal variable array for constitutive call
    int nInt = material.nInt();
    int devStrainSquared = 3;
    int Jacobian = 2;
    int tempIndex = 1;
    int entropyIndex = 0;
    real q[nInt] = {0. };
    // these are the standard conditions
    q[tempIndex] = 0.0;//this implies a reference temp of 1e3
    q[entropyIndex] = 0.0;//ref entropy of 2.049154072357840e7;
    q[Jacobian] = 0.0;// ref Jacobian of 1
    q[devStrainSquared] = 0;//1e-8; ref 

    // Calculate the analytical stress
    real heatFlux[3] = { 0. };
    material.Constitutive(&phi0, &phi, dphi0, dphi, heatFlux, q, NULL, NULL, 1.0, ndm, ndm, false);// do not compute the tangents here

    //const real* concentration0, const real* concentration, real* q, real* dt, real* f, real* df, size_t ndf
    real* f = new real[1];
    
    real* fP = new real[1];
    real* fM = new real[1];
    real* df = new real[1];
    real* dfdGrad = new real[ndm];
    f[0] = 0;
    fP[0] = 0;
    fM[0] = 0;
    df[0] = 0;
    real dt = 1.0e-4;
    
    real phiP = phi+pert;
    real phiM = phi-pert;

    material.Source(&phi0, &phiM, dphi0, dphi, q, &dt, fM, df,dfdGrad, ndm, 1);
    q[entropyIndex] = 0.0;

    material.Source(&phi0, &phiP, dphi0, dphi, q, &dt, fP, df,dfdGrad, ndm, 1);
    q[entropyIndex] = 0.0;

    material.Source(&phi0, &phi, dphi0, dphi, q, &dt, f, df,dfdGrad, ndm, 1);// do not compute the tangents here
    q[entropyIndex] = 0.0;
    
    real num_df = (fP[0] - fM[0])/(2*pert);

    // std::cout << "num_df: " << num_df << std::endl;
    // std::cout << "df: " << df[0] << std::endl;
    // std::cout << "abs_error: " << df[0]-num_df << std::endl;
    // std::cout << "rel_error: " << (df[0]-num_df)/df[0] << std::endl;
    ASSERT_NEAR(error(df[0], num_df), 0.0, tol);
    delete[] f;
    delete[] df;
    delete[] fP;
    delete[] fM;

    return;

}  // end of consistency test

int main(int argc, char** argv)
{
    ::testing::InitGoogleTest(&argc, argv);
    return RUN_ALL_TESTS();
}
