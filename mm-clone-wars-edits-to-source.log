/home/<USER>/dv/mm/.mm/pyre-boot.zip/pyre/config/pfg/Scanner.py:24: SyntaxWarning: invalid escape sequence '\s'
/home/<USER>/dv/mm/.mm/pyre-boot.zip/pyre/config/pfg/Scanner.py:25: SyntaxWarning: invalid escape sequence '\s'
/home/<USER>/dv/mm/.mm/pyre-boot.zip/pyre/config/pfg/Scanner.py:24: SyntaxWarning: invalid escape sequence '\s'
/home/<USER>/dv/mm/.mm/pyre-boot.zip/pyre/config/pfg/Scanner.py:25: SyntaxWarning: invalid escape sequence '\s'
[38;2;70;130;180m  [pkg][0m summit.pkg
[38;2;70;130;180m  [lib][0m tetra.lib
[38;2;70;130;180m  [lib][0m summit.lib
[38;2;70;130;180m  [lib][0m summit.ext.lib
[38;2;70;130;180m  [ext][0m summit.ext
[38;2;70;130;180m  [project][0m summit
[38;2;192;176;224m  [c++][0m TEST/unit/restart/test-restart-explicit-dynamics.cc
g++ /home/<USER>/dv/sumMIT/TEST/unit/restart/test-restart-explicit-dynamics.cc -o /home/<USER>/dv/sumMIT/TEST/unit/restart/test-restart-explicit-dynamics -fno-diagnostics-color -pipe -MD -std=c++20 -I/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/include/ -I/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/include/ -DWITH_MPI -DWITH_OPENMPI -I/usr/lib/x86_64-linux-gnu/openmpi/include -DWITH_PYRE -DWITH_JOURNAL -I/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/include -DWITH_EIGEN3 -I/usr/include/eigen3 -DWITH_GSL -DHAVE_INLINE -I/usr/include -DWITH_METIS -I/usr/include -DWITH_PARMETIS -I/usr/include -DWITH_SLEPC -DSLEPC_USE_EXTERN_CXX -I/usr/include/slepc -DWITH_PETSC -DPETSC_USE_EXTERN_CXX -I/usr/include/petsc -DWITH_HDF5 -I/usr/include/hdf5/openmpi -DWITH_VTK -I/usr//include/vtk-9.1 -DWITH_PYTHON -I/usr/include/python3.12 -DWITH_GMSH -I/usr/include -DWITH_EIGEN3 -I/usr/include/eigen3 -DWITH_YAML_CPP -I/usr/include -DWITH_GTEST -I/usr/include -I/home/<USER>/dv/mm -I/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/include -DMM_PLATFORM_linux_x86_64 -DMM_COMPILER_gcc -O3 -fPIC -L/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/lib/ -lsummit -L/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/lib/ -ltetra -L/usr/lib/x86_64-linux-gnu/openmpi/lib -lmpi_cxx -lmpi -L/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/lib -lpyre -ljournal -L/usr/lib -lgsl -lgslcblas -L/usr/lib -lmetis -L/usr/lib -lparmetis -L/usr/lib/x86_64-linux-gnu -lslepc -L/usr/lib/x86_64-linux-gnu -lpetsc -L/usr/lib/x86_64-linux-gnu/hdf5/openmpi -lhdf5_cpp -lhdf5 -L/usr//lib -lvtkCommonCore-9.1 -lvtkIOXML-9.1 -lvtkCommonDataModel-9.1 -lgfortran -lgfortran -L/usr/lib/x86_64-linux-gnu -lpython3.12 -lpthread -ldl -lutil -L/usr/lib -lgmsh -L/usr/lib -lyaml-cpp -L/usr/lib/x86_64-linux-gnu -lgtest -lpthread -L/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/lib -fPIC
[38;2;192;176;224m  [test][0m restart/test-restart-explicit-dynamics ./
[==========] Running 4 tests from 1 test suite.
[----------] Global test environment set-up.
[----------] 4 tests from ExplicitDynamicsRestart
[ RUN      ] ExplicitDynamicsRestart.TetCG
journal: Warning in summit::MaterialLibrary: .dat material files are deprecated. Please consider using YAML materials instead.
journal:  Material Model: linear elastic isotropic
	 State of Stress/Strain (mode): 	General 3D
	 density ....................... = 	8930
	 Youngs Modulus ................ = 	1.17e+11
	 Poisson's ratio................ = 	0.35
	 First Lame constant............ = 	1.01111e+11
	 Second Lame constant........... = 	4.33333e+10
	 Bulk modulus................... = 	1.3e+11
journal: CommMaps is empty!
------------------------------
Written Status to binary file!
------------------------------
Warning in MatrixFreeSystem::MatrixFreeSystem: The nodal field are called 'displacement' and 'displacementPrev' no matter what the region type is! To be fixed in the future.
------------------------------
Read Status from binary file! 
------------------------------
journal:  Material Model: linear elastic isotropic
	 State of Stress/Strain (mode): 	General 3D
	 density ....................... = 	8930
	 Youngs Modulus ................ = 	1.17e+11
	 Poisson's ratio................ = 	0.35
	 First Lame constant............ = 	1.01111e+11
	 Second Lame constant........... = 	4.33333e+10
	 Bulk modulus................... = 	1.3e+11
[       OK ] ExplicitDynamicsRestart.TetCG (48 ms)
[ RUN      ] ExplicitDynamicsRestart.TriCG
journal: Warning in summit::MaterialLibrary: .dat material files are deprecated. Please consider using YAML materials instead.
journal:  Material Model: linear elastic isotropic
	 State of Stress/Strain (mode): 	General 3D
	 density ....................... = 	8930
	 Youngs Modulus ................ = 	1.17e+11
	 Poisson's ratio................ = 	0.35
	 First Lame constant............ = 	1.01111e+11
	 Second Lame constant........... = 	4.33333e+10
	 Bulk modulus................... = 	1.3e+11
journal: CommMaps is empty!
------------------------------
Written Status to binary file!
------------------------------
Warning in MatrixFreeSystem::MatrixFreeSystem: The nodal field are called 'displacement' and 'displacementPrev' no matter what the region type is! To be fixed in the future.
------------------------------
Read Status from binary file! 
------------------------------
journal:  Material Model: linear elastic isotropic
	 State of Stress/Strain (mode): 	General 3D
	 density ....................... = 	8930
	 Youngs Modulus ................ = 	1.17e+11
	 Poisson's ratio................ = 	0.35
	 First Lame constant............ = 	1.01111e+11
	 Second Lame constant........... = 	4.33333e+10
	 Bulk modulus................... = 	1.3e+11
[       OK ] ExplicitDynamicsRestart.TriCG (11 ms)
[ RUN      ] ExplicitDynamicsRestart.TetDG
journal: Warning in summit::MaterialLibrary: .dat material files are deprecated. Please consider using YAML materials instead.
journal:  Material Model: linear elastic isotropic
	 State of Stress/Strain (mode): 	Plane Stress
	 density ....................... = 	8930
	 Youngs Modulus ................ = 	1.17e+11
	 Poisson's ratio................ = 	0.35
	 First Lame constant............ = 	1.01111e+11
	 Second Lame constant........... = 	4.33333e+10
	 Bulk modulus................... = 	1.3e+11
 Material Characteristics : (interface_DG)
	  label of left material ..... = 	1
	  label of right material .... = 	1
	  beta ....................... = 	1.000000e+02
------------------------------
Written Status to binary file!
------------------------------
Warning in MatrixFreeSystem::MatrixFreeSystem: The nodal field are called 'displacement' and 'displacementPrev' no matter what the region type is! To be fixed in the future.
------------------------------
Read Status from binary file! 
------------------------------
journal:  Material Model: linear elastic isotropic
	 State of Stress/Strain (mode): 	Plane Stress
	 density ....................... = 	8930
	 Youngs Modulus ................ = 	1.17e+11
	 Poisson's ratio................ = 	0.35
	 First Lame constant............ = 	1.01111e+11
	 Second Lame constant........... = 	4.33333e+10
	 Bulk modulus................... = 	1.3e+11
 Material Characteristics : (interface_DG)
	  label of left material ..... = 	1
	  label of right material .... = 	1
	  beta ....................... = 	1.000000e+02
[       OK ] ExplicitDynamicsRestart.TetDG (223 ms)
[ RUN      ] ExplicitDynamicsRestart.TriDG
journal: Warning in summit::MaterialLibrary: .dat material files are deprecated. Please consider using YAML materials instead.
journal:  Material Model: linear elastic isotropic
	 State of Stress/Strain (mode): 	Plane Stress
	 density ....................... = 	8930
	 Youngs Modulus ................ = 	1.17e+11
	 Poisson's ratio................ = 	0.35
	 First Lame constant............ = 	1.01111e+11
	 Second Lame constant........... = 	4.33333e+10
	 Bulk modulus................... = 	1.3e+11
 Material Characteristics : (interface_DG)
	  label of left material ..... = 	1
	  label of right material .... = 	1
	  beta ....................... = 	1.000000e+02
------------------------------
Written Status to binary file!
------------------------------
Warning in MatrixFreeSystem::MatrixFreeSystem: The nodal field are called 'displacement' and 'displacementPrev' no matter what the region type is! To be fixed in the future.
------------------------------
Read Status from binary file! 
------------------------------
journal:  Material Model: linear elastic isotropic
	 State of Stress/Strain (mode): 	Plane Stress
	 density ....................... = 	8930
	 Youngs Modulus ................ = 	1.17e+11
	 Poisson's ratio................ = 	0.35
	 First Lame constant............ = 	1.01111e+11
	 Second Lame constant........... = 	4.33333e+10
	 Bulk modulus................... = 	1.3e+11
 Material Characteristics : (interface_DG)
	  label of left material ..... = 	1
	  label of right material .... = 	1
	  beta ....................... = 	1.000000e+02
[       OK ] ExplicitDynamicsRestart.TriDG (45 ms)
[----------] 4 tests from ExplicitDynamicsRestart (328 ms total)

[----------] Global test environment tear-down
[==========] 4 tests from 1 test suite ran. (328 ms total)
[  PASSED  ] 4 tests.
[38;2;192;176;224m  [c++][0m TEST/unit/boundaryconditions/element_boundary_conditions.cc
g++ /home/<USER>/dv/sumMIT/TEST/unit/boundaryconditions/element_boundary_conditions.cc -o /home/<USER>/dv/sumMIT/TEST/unit/boundaryconditions/element_boundary_conditions -fno-diagnostics-color -pipe -MD -std=c++20 -I/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/include/ -I/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/include/ -DWITH_MPI -DWITH_OPENMPI -I/usr/lib/x86_64-linux-gnu/openmpi/include -DWITH_PYRE -DWITH_JOURNAL -I/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/include -DWITH_EIGEN3 -I/usr/include/eigen3 -DWITH_GSL -DHAVE_INLINE -I/usr/include -DWITH_METIS -I/usr/include -DWITH_PARMETIS -I/usr/include -DWITH_SLEPC -DSLEPC_USE_EXTERN_CXX -I/usr/include/slepc -DWITH_PETSC -DPETSC_USE_EXTERN_CXX -I/usr/include/petsc -DWITH_HDF5 -I/usr/include/hdf5/openmpi -DWITH_VTK -I/usr//include/vtk-9.1 -DWITH_PYTHON -I/usr/include/python3.12 -DWITH_GMSH -I/usr/include -DWITH_EIGEN3 -I/usr/include/eigen3 -DWITH_YAML_CPP -I/usr/include -DWITH_GTEST -I/usr/include -I/home/<USER>/dv/mm -I/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/include -DMM_PLATFORM_linux_x86_64 -DMM_COMPILER_gcc -O3 -fPIC -L/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/lib/ -lsummit -L/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/lib/ -ltetra -L/usr/lib/x86_64-linux-gnu/openmpi/lib -lmpi_cxx -lmpi -L/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/lib -lpyre -ljournal -L/usr/lib -lgsl -lgslcblas -L/usr/lib -lmetis -L/usr/lib -lparmetis -L/usr/lib/x86_64-linux-gnu -lslepc -L/usr/lib/x86_64-linux-gnu -lpetsc -L/usr/lib/x86_64-linux-gnu/hdf5/openmpi -lhdf5_cpp -lhdf5 -L/usr//lib -lvtkCommonCore-9.1 -lvtkIOXML-9.1 -lvtkCommonDataModel-9.1 -lgfortran -lgfortran -L/usr/lib/x86_64-linux-gnu -lpython3.12 -lpthread -ldl -lutil -L/usr/lib -lgmsh -L/usr/lib -lyaml-cpp -L/usr/lib/x86_64-linux-gnu -lgtest -lpthread -L/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/lib -fPIC
[38;2;192;176;224m  [test][0m boundaryconditions/element_boundary_conditions ./
[==========] Running 2 tests from 1 test suite.
[----------] Global test environment set-up.
[----------] 2 tests from ElementBoundaryConditions
[ RUN      ] ElementBoundaryConditions.Serial
journal: Warning in summit::MaterialLibrary: .dat material files are deprecated. Please consider using YAML materials instead.
max value = 0.00244342 & min value = -0.10018
[       OK ] ElementBoundaryConditions.Serial (5 ms)
[ RUN      ] ElementBoundaryConditions.Quadratic
journal: Warning in summit::MaterialLibrary: .dat material files are deprecated. Please consider using YAML materials instead.
corner value = -3.1225e-17 ?= -0.0333333
corner value = -1.23165e-16 ?= -0.0333333
corner value = 5.20417e-18 ?= -0.0333333
corner value = 5.37764e-17 ?= -0.0333333
cross value = 4.85723e-17 ?= -0.0666667
square value = -0.166667 ?= -0.133333
square value = -0.166667 ?= -0.133333
cross value = -0.0833333 ?= -0.0666667
square value = -0.166667 ?= -0.133333
cross value = -0.0833333 ?= -0.0666667
square value = -0.166667 ?= -0.133333
cross value = -0.0833333 ?= -0.0666667
cross value = -0.0833333 ?= -0.0666667
[       OK ] ElementBoundaryConditions.Quadratic (1 ms)
[----------] 2 tests from ElementBoundaryConditions (6 ms total)

[----------] Global test environment tear-down
[==========] 2 tests from 1 test suite ran. (6 ms total)
[  PASSED  ] 2 tests.
[38;2;192;176;224m  [c++][0m TEST/unit/boundaryconditions/element_boundary_conditions_mpi.cc
g++ /home/<USER>/dv/sumMIT/TEST/unit/boundaryconditions/element_boundary_conditions_mpi.cc -o /home/<USER>/dv/sumMIT/TEST/unit/boundaryconditions/element_boundary_conditions_mpi -fno-diagnostics-color -pipe -MD -std=c++20 -I/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/include/ -I/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/include/ -DWITH_MPI -DWITH_OPENMPI -I/usr/lib/x86_64-linux-gnu/openmpi/include -DWITH_PYRE -DWITH_JOURNAL -I/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/include -DWITH_EIGEN3 -I/usr/include/eigen3 -DWITH_GSL -DHAVE_INLINE -I/usr/include -DWITH_METIS -I/usr/include -DWITH_PARMETIS -I/usr/include -DWITH_SLEPC -DSLEPC_USE_EXTERN_CXX -I/usr/include/slepc -DWITH_PETSC -DPETSC_USE_EXTERN_CXX -I/usr/include/petsc -DWITH_HDF5 -I/usr/include/hdf5/openmpi -DWITH_VTK -I/usr//include/vtk-9.1 -DWITH_PYTHON -I/usr/include/python3.12 -DWITH_GMSH -I/usr/include -DWITH_EIGEN3 -I/usr/include/eigen3 -DWITH_YAML_CPP -I/usr/include -DWITH_GTEST -I/usr/include -I/home/<USER>/dv/mm -I/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/include -DMM_PLATFORM_linux_x86_64 -DMM_COMPILER_gcc -O3 -fPIC -L/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/lib/ -lsummit -L/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/lib/ -ltetra -L/usr/lib/x86_64-linux-gnu/openmpi/lib -lmpi_cxx -lmpi -L/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/lib -lpyre -ljournal -L/usr/lib -lgsl -lgslcblas -L/usr/lib -lmetis -L/usr/lib -lparmetis -L/usr/lib/x86_64-linux-gnu -lslepc -L/usr/lib/x86_64-linux-gnu -lpetsc -L/usr/lib/x86_64-linux-gnu/hdf5/openmpi -lhdf5_cpp -lhdf5 -L/usr//lib -lvtkCommonCore-9.1 -lvtkIOXML-9.1 -lvtkCommonDataModel-9.1 -lgfortran -lgfortran -L/usr/lib/x86_64-linux-gnu -lpython3.12 -lpthread -ldl -lutil -L/usr/lib -lgmsh -L/usr/lib -lyaml-cpp -L/usr/lib/x86_64-linux-gnu -lgtest -lpthread -L/home/<USER>/tools/mm/gcc/opt-shared-linux-x86_64/lib -fPIC
