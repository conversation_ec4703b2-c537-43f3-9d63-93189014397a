# buld the summit libraries
function(summit_summitLib)
  # buld the libsummit version file
  configure_file(
    src/version.h.in src/version.h
    @ONLY
    )
  configure_file(
    src/version.cc.in src/version.cc
    @ONLY
    )
  # copy the summit headers over to the staging area
  file(GLOB_RECURSE files
    RELATIVE ${CMAKE_CURRENT_SOURCE_DIR}/src
    CONFIGURE_DEPENDS
    src/*.h src/*.icc
    )
  foreach(file ${files})
    #message("file: ${file}")
    configure_file(src/${file} ${PROJECT_BINARY_DIR}/include/summit/${file} COPYONLY)
  endforeach()

  file(GLOB_RECURSE files_tools
    RELATIVE ${CMAKE_CURRENT_SOURCE_DIR}/tools/tetra
    CONFIGURE_DEPENDS
    tools/tetra/*.h tools/tetra/*.icc
    )
  foreach(file ${files_tools})
    #message("file: ${file}")
    configure_file(tools/tetra/${file} ${PROJECT_BINARY_DIR}/include/tetra/${file} COPYONLY)
  endforeach()

  # copy cmake app file to the staging area
  # message("copy cmake app file to staging area: ")
  # configure_file(.cmake/summit_apps.cmake ${PROJECT_BINARY_DIR}/cmake/summit_apps.cmake COPYONLY)

  # the libsummit target
  add_library(summit SHARED)

  # set the include directories
  target_include_directories(
    summit PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_BINARY_DIR}/include>
    $<INSTALL_INTERFACE:${SUMMIT_DEST_INCLUDE}>
    )

  # add the sources
  set(srcdir ${CMAKE_CURRENT_SOURCE_DIR}/src)
  target_sources(summit
    PRIVATE
    ${srcdir}/boundaryconditions/BCfunction.cc
    ${srcdir}/boundaryconditions/BCtagger.cc
    ${srcdir}/boundaryconditions/BlastBCfunction.cc
    ${srcdir}/boundaryconditions/boundaryConditions.cc
    ${srcdir}/boundaryconditions/initialConditions.cc
    ${srcdir}/boundaryconditions/plateRM_dirichlet_boundaryConditions.cc
    ${srcdir}/boundaryconditions/shellRM_boundaryConditions.cc
    ${srcdir}/boundaryconditions/shellRM_clamped_bc.cc
    ${srcdir}/boundaryconditions/shellRM_freeze_bc.cc
    ${srcdir}/boundaryconditions/shellRM_initialConditions.cc
    ${srcdir}/boundaryconditions/weakRotationClampShellGenerator.cc
    ${srcdir}/elements/element_gmsh_util.cc
    ${srcdir}/elements/element_set.cc
    ${srcdir}/elements/element_set_3D_bulk_gmsh.cc
    ${srcdir}/elements/element_set_3D_inter_gmsh.cc
    ${srcdir}/elements/element_set_3D_shell_gmsh.cc
    ${srcdir}/elements/element_set_beam_KL_TF.cc
    ${srcdir}/elements/element_set_body.cc
    ${srcdir}/elements/element_set_boundary.cc
    ${srcdir}/elements/element_set_boundary_line_axi.cc
    ${srcdir}/elements/element_set_boundary_plateRM.cc
    ${srcdir}/elements/element_set_boundary_shellRM.cc
    ${srcdir}/elements/element_set_bulk.cc
    ${srcdir}/elements/element_set_interface_lubrication.cc
    ${srcdir}/elements/element_set_interface_lubrication_gmsh.cc
    ${srcdir}/elements/element_set_interface_one_sided.cc
    ${srcdir}/elements/element_set_interface_one_sided_gmsh.cc
    ${srcdir}/elements/element_set_interface_one_sided_shell.cc
    ${srcdir}/elements/element_set_interface_one_sided_shell_gmsh.cc
    ${srcdir}/elements/element_set_interface_plateRM.cc
    ${srcdir}/elements/element_set_interface_shellRM.cc
    ${srcdir}/elements/element_set_interface_shellRM_nonlinear.cc
    ${srcdir}/elements/element_set_interface_tetP.cc
    ${srcdir}/elements/element_set_interface_triP.cc
    ${srcdir}/elements/element_set_interface_two_sided.cc
    ${srcdir}/elements/element_set_interface_two_sided_gmsh.cc
    ${srcdir}/elements/element_set_interface_two_sided_shell.cc
    ${srcdir}/elements/element_set_lubrication.cc
    ${srcdir}/elements/element_set_lubrication_gmsh.cc
    ${srcdir}/elements/element_set_manifold_SegP.cc
    ${srcdir}/elements/element_set_manifold_TriP.cc
    ${srcdir}/elements/element_set_monolithic.cc
    ${srcdir}/elements/element_set_monolithic_interface_tetP.cc
    ${srcdir}/elements/element_set_monolithic_interface_triP.cc
    ${srcdir}/elements/element_set_monolithic_tetP_tetP.cc
    ${srcdir}/elements/element_set_monolithic_triP_triP.cc
    ${srcdir}/elements/element_set_one_sided_lubrication.cc
    ${srcdir}/elements/element_set_one_sided_lubrication_gmsh.cc
    ${srcdir}/elements/element_set_peri.cc
    ${srcdir}/elements/element_set_peri_hex1.cc
    ${srcdir}/elements/element_set_peri_quad1.cc
    ${srcdir}/elements/element_set_peri_tet1.cc
    ${srcdir}/elements/element_set_plateRM.cc
    ${srcdir}/elements/element_set_shell_3D_gmsh.cc
    ${srcdir}/elements/element_set_shell_bulk_gmsh.cc
    ${srcdir}/elements/element_set_shell_inter_gmsh.cc
    ${srcdir}/elements/element_set_shellP.cc
    ${srcdir}/elements/element_set_shellP_shellP.cc
    ${srcdir}/elements/element_set_shellP_tetQ.cc
    ${srcdir}/elements/element_set_shellRM.cc
    ${srcdir}/elements/element_set_shellRM_nonlinear.cc
    ${srcdir}/elements/element_set_tet1.cc
    ${srcdir}/elements/element_set_tet2.cc
    ${srcdir}/elements/element_set_tetP.cc
    ${srcdir}/elements/element_set_tetP_shellQ.cc
    ${srcdir}/elements/element_set_tetP_tetP.cc
    ${srcdir}/elements/element_set_tri1.cc
    ${srcdir}/elements/element_set_tri1_tri1.cc
    ${srcdir}/elements/element_set_tri2.cc
    ${srcdir}/elements/element_set_triAxiP.cc
    ${srcdir}/elements/element_set_triP.cc
    ${srcdir}/elements/element_set_triP_triP.cc
    ${srcdir}/elements/element_set_weak_boundary_shell.cc
    ${srcdir}/elements/element_set_weak_boundary_shell_gmsh.cc
    ${srcdir}/elements/reference_seg_p.cc
    ${srcdir}/elements/reference_tri_p.cc
    ${srcdir}/elements/shellBasis.cc
    ${srcdir}/elements/variational_dg_element.cc
    ${srcdir}/extra/ribbon/fluid_loading_functor.cc
    ${srcdir}/extra/ribbon/fluid_loading_functor_ribbons.cc
    ${srcdir}/extra/ribbon/fluid_loading_update_manager.cc
    ${srcdir}/extra/ribbon/fluid_model.cc
    ${srcdir}/extra/ribbon/implicit_time_step_manager.cc
    ${srcdir}/extra/ribbon/manifold_with_boundary_input_data.cc
    ${srcdir}/extra/ribbon/problem_input_data.cc
    ${srcdir}/extra/ribbon/problem_manifolds_without_boundary_input_data.cc
    ${srcdir}/extra/ribbon/ribbon_auxiliary_functions.cc
    ${srcdir}/extra/ribbon/ribbon_bc_manager.cc
    ${srcdir}/extra/ribbon/ribbon_element_data.cc
    ${srcdir}/extra/ribbon/ribbon_input_data.cc
    ${srcdir}/extra/ribbon/ribbon_mesh_data.cc
    ${srcdir}/extra/ribbon/ribbons_mesh_data.cc
    ${srcdir}/extra/ribbon/single_ribbon_input_data.cc
    ${srcdir}/fem/band.cc
    ${srcdir}/fem/basis_functions_reference_segP.cc
    ${srcdir}/fem/basis_functions_reference_tetP.cc
    ${srcdir}/fem/basis_functions_reference_triP.cc
    ${srcdir}/fem/discretized_boundary.cc
    ${srcdir}/fem/dof_map.cc
    ${srcdir}/fem/f_band.f
    ${srcdir}/fem/function_space.cc
    ${srcdir}/fem/function_space_internal.cc
    ${srcdir}/fem/kite_macro_element.cc
    ${srcdir}/fem/lew_interface_gmsh.cc
    ${srcdir}/fem/linear_mapping.cc
    ${srcdir}/fem/reference_p_element.cc
    ${srcdir}/io/archivor.cc
    ${srcdir}/io/archoperator.cc
    ${srcdir}/io/cantera_to_pyre.cc
    ${srcdir}/io/gmsh/mesh_writer_gmsh.cc
    ${srcdir}/io/input_file.cc
    ${srcdir}/io/mesh_writer.cc
    ${srcdir}/io/shellRM_displacement_history.cc
    ${srcdir}/io/summit_message.cc
    ${srcdir}/io/txt/txt_writer.cc
    ${srcdir}/io/vtk/mesh_writer_vtk.cc
    ${srcdir}/io/vtk/mesh_writer_vtk_interface.cc
    ${srcdir}/io/vtk/mesh_writer_vtk_interface_monolithic.cc
    ${srcdir}/io/vtk/mesh_writer_vtk_lagrange.cc
    ${srcdir}/io/vtk/quad_point_writer_vtk.cc
    ${srcdir}/io/vtk/quad_point_writer_vtk_interface.cc
    ${srcdir}/io/vtk/quad_writer_vtk.cc
    ${srcdir}/io/yaml/yaml_input_file.cc
    ${srcdir}/materials/multiphysics/armero_simo/ArmeroSimo.cc
    ${srcdir}/materials/multiphysics/armero_simo/ArmeroSimoThermal.cc
    ${srcdir}/materials/advective_flow/advective_flow.cc
    ${srcdir}/materials/advective_flow/channel_advective_flow.cc
    ${srcdir}/materials/bcc_cuitino/bcc_cuitino.cc
    ${srcdir}/materials/bcc_cuitino/f_pan.f
    ${srcdir}/materials/bcc_cuitino/myerfc.cc
    ${srcdir}/materials/bcc_cuitino/xtal-bcc-explicit.f
    ${srcdir}/materials/bcc_cuitino/xtal-input-thermal.f
    ${srcdir}/materials/bcc_cuitino/xtal-update-thermal-all.f
    ${srcdir}/materials/bcc_cuitino/xtal-update-thermal-explicit-onebyone.f
    ${srcdir}/materials/beam/beam_KL_TF_elastic.cc
    ${srcdir}/materials/beam/beam_material.cc
    ${srcdir}/materials/beam/contact_beam_with_friction.cc
    ${srcdir}/materials/black_oil_model/isotropic_crack_medium_black_oil_model.cc
    ${srcdir}/materials/black_oil_model/isotropic_porous_medium_black_oil_model.cc
    ${srcdir}/materials/black_oil_model/parametric_isotropic_porous_medium_black_oil_model.cc
    ${srcdir}/materials/black_oil_model_material.cc
    ${srcdir}/materials/camclay/camclay.cc
    ${srcdir}/materials/camclay/camclay_power_law.cc
    ${srcdir}/materials/camclay/f_camclay.f
    ${srcdir}/materials/camclay/f_camclay_power_law.f
    ${srcdir}/materials/camclay/f_camclay_power_law_update.f
    ${srcdir}/materials/camclay/f_camclay_power_law_update_inc.f
    ${srcdir}/materials/camclay/f_camclay_update.f
    ${srcdir}/materials/camclay/f_camclay_update_inc.f
    ${srcdir}/materials/ceramic/ceramic.cc
    ${srcdir}/materials/ceramic/constitu_ceramic.f
    ${srcdir}/materials/ceramic/ode.f
    ${srcdir}/materials/channel_flow/channel_flow.cc
    ${srcdir}/materials/channel_flow/parametric_channel_flow.cc
    ${srcdir}/materials/channel_flow/parametric_power_law_flow.cc
    ${srcdir}/materials/channel_flow/parametric_slurry_flow.cc
    ${srcdir}/materials/channel_flow/slurry_flow.cc
    ${srcdir}/materials/channel_flow/slurry_flow_temperature_parametric.cc
    ${srcdir}/materials/cohesive-zone-model-dg/cohesive_zone_dg_utils.cc
    ${srcdir}/materials/cohesive-zone-model-dg/failure_criterion.cc
    ${srcdir}/materials/cohesive-zone-model-dg/max_effective_stress_criterion.cc
    ${srcdir}/materials/cohesive-zone-model-dg/mohr_coulomb_criterion.cc
    ${srcdir}/materials/cohesiveDG/cohesive_dg.cc
    ${srcdir}/materials/cohesiveDG/cohesive_dg_ductile.cc
    ${srcdir}/materials/cohesiveDG/cohesive_dg_fluid.cc
    ${srcdir}/materials/cohesiveDG/cohesive_dg_fluid_parametric.cc
    ${srcdir}/materials/cohesiveDG/cohesive_dg_fluid_poroelastic.cc
    ${srcdir}/materials/cohesiveDG/cohesive_dg_fluid_poroelastic_parametric.cc
    ${srcdir}/materials/cohesiveDG/cohesive_dg_MC.cc
    ${srcdir}/materials/cohesiveDG/cohesive_dg_parametric.cc
    ${srcdir}/materials/cohesiveDG/cohesive_dg_parametric_MC.cc
    ${srcdir}/materials/cohesiveDG/cohesive_dg_parametric_tsl.cc
    ${srcdir}/materials/cohesiveDG/cohesive_dg_uniform_pressure.cc
    ${srcdir}/materials/cohesiveDG/variational_czm.cc
    ${srcdir}/materials/contact_interface_dg.cc
    ${srcdir}/materials/damped_elastic/damped_elastic.cc
    ${srcdir}/materials/damped_elastic/parametric_damped_elastic.cc
    ${srcdir}/materials/elastic/anisotropic_electric.cc
    ${srcdir}/materials/elastic/elastic.cc
    ${srcdir}/materials/elastic/elastic_eos.cc
    ${srcdir}/materials/elastic/elastic_eos_BM.cc
    ${srcdir}/materials/elastic/elastic_eos_cavitation.cc
    ${srcdir}/materials/elastic/elastic_eos_Clifton.cc
    ${srcdir}/materials/elastic/elastic_eos_Glass.cc
    ${srcdir}/materials/elastic/elastic_eos_MG.cc
    ${srcdir}/materials/elastic/elastic_eos_Murnaghan.cc
    ${srcdir}/materials/elastic/elastic_eos_Tait.cc
    ${srcdir}/materials/elastic/elastic_mass_scale.cc
    ${srcdir}/materials/elastic/elastic_thermal.cc
    ${srcdir}/materials/elastic/pica.cc
    ${srcdir}/materials/elastic/poro_elastic_thermal.cc
    ${srcdir}/materials/elastic/thermochemoelastic.cc
    ${srcdir}/materials/exponential/eigen/eigenp.f
    ${srcdir}/materials/exponential/eigen/rg.f
    ${srcdir}/materials/exponential/exponential.f
    ${srcdir}/materials/exponential/logarithmic.f
    ${srcdir}/materials/exponential/spectral/eig33.f
    ${srcdir}/materials/exponential/spectral/exponential_spectral.f
    ${srcdir}/materials/exponential/spectral/logarithmic_spectral.f
    ${srcdir}/materials/exponential/taylor/exponential_taylor.f
    ${srcdir}/materials/exponential/taylor/logarithmic_taylor.f
    ${srcdir}/materials/gel_ISN/gel_ISN.cc
    ${srcdir}/materials/gent-compressible/gent_compressible.cc
    ${srcdir}/materials/gent-hyperelastic/gent-hyperelastic.cc
    ${srcdir}/materials/glass-Densification/f_glass.f
    ${srcdir}/materials/glass-Densification/f_glass_update.f
    ${srcdir}/materials/glass-Densification/f_glass_update_inc.f
    ${srcdir}/materials/glass-Densification/glass_camclay.cc
    ${srcdir}/materials/granular-compaction/granular-compaction-largeDef.cc
    ${srcdir}/materials/granular-compaction/granular-compaction-smallDef.cc
    ${srcdir}/materials/hyperelasticity/ogden_hyperfoam.cc
    ${srcdir}/materials/inelastic_eos/inelastic_eos_glass.cc
    ${srcdir}/materials/inelastic_eos/inelastic_eos_glass_coupledshear.cc
    ${srcdir}/materials/inelastic_eos/inelastic_eos_simple.cc
    ${srcdir}/materials/interface_dg.cc
    ${srcdir}/materials/J2_linear/J2_linear.cc
    ${srcdir}/materials/J2_plastic/f_J2_plastic_jc.f
    ${srcdir}/materials/J2_plastic/f_J2_plastic_jc_damage.f
    ${srcdir}/materials/J2_plastic/f_J2_plastic_jc_damage_update.f
    ${srcdir}/materials/J2_plastic/f_J2_plastic_jc_update.f
    ${srcdir}/materials/J2_plastic/f_J2_plastic_pow.f
    ${srcdir}/materials/J2_plastic/f_J2_plastic_pow_update.f
    ${srcdir}/materials/J2_plastic/f_J2_plastic_update.f
    ${srcdir}/materials/J2_plastic/J2_plastic.cc
    ${srcdir}/materials/J2_plastic/J2_plastic_jc.cc
    ${srcdir}/materials/J2_plastic/J2_plastic_jc_damage.cc
    ${srcdir}/materials/J2_plastic/J2_plastic_pow.cc
    ${srcdir}/materials/J2_plastic/J2_plastic_pow_deletion.cc
    ${srcdir}/materials/material.cc
    ${srcdir}/materials/material_consistency_test.cc
    ${srcdir}/materials/material_library.cc
    ${srcdir}/materials/mechanical_material.cc
    ${srcdir}/materials/multiphysics/linear_thermo_poro_elasticity/linear-thermo-poro-elasticity.cc
    ${srcdir}/materials/multiphysics/pilling_bedworth_swelling_heat_conducting_eyring_creep/PillingBedworthSwellingHeatConductingEyringCreep.cc
    ${srcdir}/materials/multiphysics/pilling_bedworth_swelling_heat_conducting_eyring_creep/PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusion.cc
    ${srcdir}/materials/multiphysics/pilling_bedworth_swelling_heat_conducting_eyring_creep/PillingBedworthSwellingHeatConductingEyringCreepReactionDiffusionMacroscopic.cc
    ${srcdir}/materials/multiphysics/ulm_concrete/ulm_concrete.cc
    ${srcdir}/materials/neohookean/neohookean.cc
    ${srcdir}/materials/neohookean/neohookean_compressible.cc
    ${srcdir}/materials/neohookean/neohookean_electric.cc
    ${srcdir}/materials/neohookean/neohookean_thermal.cc
    ${srcdir}/materials/parametric_elastic/parametric_elastic.cc
    ${srcdir}/materials/parametric_poroelastic/parametric_pore_pressure_poroelastic.cc
    ${srcdir}/materials/parametric_poroelastic/parametric_poroelastic.cc
    ${srcdir}/materials/parametric_sma/parametric_sma.cc
    ${srcdir}/materials/parametric_sma/rank4_rotate_para.cc
    ${srcdir}/materials/parametric_sma/sys_setup_para.cc
    ${srcdir}/materials/parametric_sma/update_exp_para_SMA.cc
    ${srcdir}/materials/parametrically_damped_elastic/parametrically_damped_elastic.cc
    ${srcdir}/materials/peridynamic/peridynamic_elastic.cc
    ${srcdir}/materials/peridynamic/peridynamic_elastic_modeI.cc
    ${srcdir}/materials/peridynamic/peridynamic_iso.cc
    ${srcdir}/materials/peridynamic/peridynamic_iso_modeI.cc
    ${srcdir}/materials/peridynamic/peridynamic_j2_linear.cc
    ${srcdir}/materials/permeable_flow/permeable_flow.cc
    ${srcdir}/materials/phaseTrans/phaseTrans.cc
    ${srcdir}/materials/poisson_material.cc
    ${srcdir}/materials/polyconvex/polyconvex.cc
    ${srcdir}/materials/polyconvex/polyconvexlibrary.cc
    ${srcdir}/materials/polyconvex_orthotropic/polyconvex_orthotropic.cc
    ${srcdir}/materials/polyconvex_orthotropic/polyconvexlibrary_orthotropic.cc
    ${srcdir}/materials/porous_medium_flow/isotropic_crack_medium_flow.cc
    ${srcdir}/materials/porous_medium_flow/isotropic_crack_medium_gas_flow.cc
    ${srcdir}/materials/porous_medium_flow/isotropic_porous_medium_flow.cc
    ${srcdir}/materials/porous_medium_flow/isotropic_porous_medium_gas_flow.cc
    ${srcdir}/materials/porous_medium_flow/parametric_isotropic_crack_medium_flow.cc
    ${srcdir}/materials/porous_medium_flow/parametric_isotropic_crack_medium_gas_flow.cc
    ${srcdir}/materials/porous_medium_flow_material.cc
    ${srcdir}/materials/porous_medium_gas_flow_material.cc
    ${srcdir}/materials/Prony_viscoelastic/Prony_viscoelastic.cc
    ${srcdir}/materials/reaction_diffusion/bessire_source.cc
    ${srcdir}/materials/reaction_diffusion/electro_elastic.cc
    ${srcdir}/materials/reaction_diffusion/euler_equations.cc
    ${srcdir}/materials/reaction_diffusion/FIAT_Pyrolysis.cc
    ${srcdir}/materials/reaction_diffusion/General_Pyrolysis.cc
    ${srcdir}/materials/reaction_diffusion/lin_conv_diff.cc
    ${srcdir}/materials/reaction_diffusion/linear_piezo.cc
    ${srcdir}/materials/reaction_diffusion/multispecies_diffusion.cc
    ${srcdir}/materials/reaction_diffusion/multispecies_reaction_diffusion.cc
    ${srcdir}/materials/reaction_diffusion/nonlinear_conduction.cc
    ${srcdir}/materials/reaction_diffusion/Nonlinear_Heat_Conduction_Table.cc
    ${srcdir}/materials/reaction_diffusion/nonlocal_damage.cc
    ${srcdir}/materials/reaction_diffusion/porous_medium_equation.cc
    ${srcdir}/materials/reaction_diffusion/rxndiff_consistency_test.cc
    ${srcdir}/materials/reaction_diffusion/SquaredPressure.cc
    ${srcdir}/materials/reaction_diffusion/TACOT.cc
    ${srcdir}/materials/reaction_diffusion/TorresHerrador.cc
    ${srcdir}/materials/reaction_diffusion/trick.cc
    ${srcdir}/materials/reaction_diffusion/variational_conduction.cc
    ${srcdir}/materials/reaction_diffusion_material.cc
    ${srcdir}/materials/seme/seme.cc
    ${srcdir}/materials/sevf/sevf.cc
    ${srcdir}/materials/shell/nl_shell_fabric.cc
    ${srcdir}/materials/shell/nl_shell_material.cc
    ${srcdir}/materials/shell/nl_shell_neoHookean.cc
    ${srcdir}/materials/shell/plate_interface.cc
    ${srcdir}/materials/shell/shell_auxiliary.cc
    ${srcdir}/materials/shell/shell_cohesive_exponential.cc
    ${srcdir}/materials/shell/shell_cohesive_linear.cc
    ${srcdir}/materials/shell/shell_elastic.cc
    ${srcdir}/materials/shell/shell_elasticRM.cc
    ${srcdir}/materials/shell/shell_material.cc
    ${srcdir}/materials/shell/shell_plastic.cc
    ${srcdir}/materials/shell/shell_plasticRM.cc
    ${srcdir}/materials/shell/vuel_UA1A2_3n_mem_Fpre.f
    ${srcdir}/materials/shell_interface.cc
    ${srcdir}/materials/sma/rank4_rotate.cc
    ${srcdir}/materials/sma/SMA_elastic.cc
    ${srcdir}/materials/sma/sys_setup.cc
    ${srcdir}/materials/sma/update_exp_SMA.cc
    ${srcdir}/materials/smc/rank4_rotate_SMC.cc
    ${srcdir}/materials/smc/SMC.cc
    ${srcdir}/materials/smc/sys_setup_SMC.cc
    ${srcdir}/materials/smc/update_exp_SMC.cc
    ${srcdir}/materials/stokeslet_flow.cc
    ${srcdir}/materials/thermal_model/channel_thermal_model.cc
    ${srcdir}/materials/thermal_model/isotropic_bulk_medium_thermal_model.cc
    ${srcdir}/materials/thermal_model/isotropic_crack_medium_thermal_model.cc
    ${srcdir}/materials/thermal_model/parametric_isotropic_crack_medium_thermal_model.cc
    ${srcdir}/materials/thermal_model_material.cc
    ${srcdir}/materials/transport_material.cc
    ${srcdir}/materials/uhmwp/bergstrom_hybrid.cc
    ${srcdir}/materials/upwind_interface_dg.cc
    ${srcdir}/materials/vumat/brain-tissue-MIT/f_constitu_brain_tissue.f
    ${srcdir}/materials/vumat/brain-tissue-MIT/vumat_brain_tissue.cc
    ${srcdir}/materials/vumat/cortical/constitutive/f_constitu_cortical.f
    ${srcdir}/materials/vumat/cortical/constitutive/f_constitu_cortical_aniso.f
    ${srcdir}/materials/vumat/cortical/homogeneous_vumat_cortical.cc
    ${srcdir}/materials/vumat/cortical/homogeneous_vumat_cortical_aniso.cc
    ${srcdir}/materials/vumat/cortical/homogeneous_vumat_cortical_damage.cc
    ${srcdir}/materials/vumat/cortical/parametric_vumat_cortical.cc
    ${srcdir}/materials/vumat/cortical/parametric_vumat_cortical_aniso.cc
    ${srcdir}/materials/vumat/cortical/parametric_vumat_cortical_aniso_damage.cc
    ${srcdir}/materials/vumat/cortical/parametric_vumat_cortical_damage.cc
    ${srcdir}/materials/vumat/cortical/vumat_cortical.cc
    ${srcdir}/materials/vumat/f_functions.f
    ${srcdir}/materials/vumat/ice-crrel/Ice_UMAT.f
    ${srcdir}/materials/vumat/ice-crrel/umat_ice.cc
    ${srcdir}/materials/vumat/neohookean/f_vumat_nh.f
    ${srcdir}/materials/vumat/neohookean/vumat_neohookean.cc
    ${srcdir}/materials/vumat/polyurea-MIT/f_constitu_polyurea_mit.f
    ${srcdir}/materials/vumat/polyurea-MIT/vumat_polyurea.cc
    ${srcdir}/materials/multiphysics/pyrolyzing_poroelastic_fracture/pyrolyzing_poroelastic_fracture_mechanics.cc
    ${srcdir}/mathlib/eig3.cc
    ${srcdir}/mathlib/functor.cc
    ${srcdir}/mathlib/mat2.cc
    ${srcdir}/mathlib/mat3.cc
    ${srcdir}/mathlib/mathlib.cc
    ${srcdir}/mathlib/point.cc
    ${srcdir}/mathlib/quadrature_library.cc
    ${srcdir}/mathlib/quadrature_rule.cc
    ${srcdir}/mathlib/sparse/sparse_crs.cc
    ${srcdir}/mathlib/sparse/sparse_matrix.cc
    ${srcdir}/mathlib/sparse/sparse_skyline.cc
    ${srcdir}/mathlib/sparse/sparse_solver.cc
    ${srcdir}/mathlib/sparse/sparse_solver_adlib.cc
    ${srcdir}/mathlib/spline.cc
    ${srcdir}/mathlib/tritetP.cc
    ${srcdir}/mesh/boundary.cc
    ${srcdir}/mesh/boundary_graph.cc
    ${srcdir}/mesh/bounded_cylinder_object.cc
    ${srcdir}/mesh/bulk_boundary.cc
    ${srcdir}/mesh/bulk_element_internal_boundary.cc
    ${srcdir}/mesh/bulk_element_on_boundary.cc
    ${srcdir}/mesh/coloring_boundary.cc
    ${srcdir}/mesh/compare_mesh_entities.cc
    ${srcdir}/mesh/cylinder_object.cc
    ${srcdir}/mesh/edge.cc
    ${srcdir}/mesh/element_by_element_partitioner.cc
    ${srcdir}/mesh/equation_boundary.cc
    ${srcdir}/mesh/extracted_3D_boundary.cc
    ${srcdir}/mesh/extracted_edge_boundary.cc
    ${srcdir}/mesh/extracted_face_boundary.cc
    ${srcdir}/mesh/face.cc
    ${srcdir}/mesh/geom_object.cc
    ${srcdir}/mesh/hyperrectangle_object.cc
    ${srcdir}/mesh/int_ext_boundary.cc
    ${srcdir}/mesh/internal_boundary.cc
    ${srcdir}/mesh/line_object.cc
    ${srcdir}/mesh/map_color_to_mesh_entities.cc
    ${srcdir}/mesh/mesh.cc
    ${srcdir}/mesh/mesh_entity.cc
    ${srcdir}/mesh/mesh_entity_iterator.cc
    ${srcdir}/mesh/mesh_geometry.cc
    ${srcdir}/mesh/mesh_topology.cc
    ${srcdir}/mesh/meshGmsh.cc
    ${srcdir}/mesh/metis_partitioner.cc
    ${srcdir}/mesh/outer_boundary.cc
    ${srcdir}/mesh/parmeshreaderh5.cc
    ${srcdir}/mesh/parmeshreadertext.cc
    ${srcdir}/mesh/parmetis_partitioner.cc
    ${srcdir}/mesh/parpartition_boundary.cc
    ${srcdir}/mesh/parpartitioner.cc
    ${srcdir}/mesh/partition_boundary.cc
    ${srcdir}/mesh/partition_boundary_gmsh.cc
    ${srcdir}/mesh/partition_subdivided_boundary.cc
    ${srcdir}/mesh/partitioner.cc
    ${srcdir}/mesh/plane_object.cc
    ${srcdir}/mesh/sphere_object.cc
    ${srcdir}/mesh/tetra.cc
    ${srcdir}/parallel/comm_map.cc
    ${srcdir}/parallel/communication_manager.cc
    ${srcdir}/parallel/communicator.cc
    ${srcdir}/parallel/debug.cc
    ${srcdir}/parallel/ghost_cell_nodal_communication_manager.cc
    ${srcdir}/parallel/internal_nodal_communication_manager.cc
    ${srcdir}/parallel/mpi_communicator.cc
    ${srcdir}/parallel/mpi_summit.cc
    ${srcdir}/parallel/nodal_communication_manager.cc
    ${srcdir}/parallel/nodal_communicator.cc
    ${srcdir}/parallel/particle_communication_manager.cc
    ${srcdir}/parallel/particle_communicator.cc
    ${srcdir}/parallel/quad_comm_map.cc
    ${srcdir}/parallel/quadrature_communication_manager.cc
    ${srcdir}/parallel/quadrature_communicator.cc
    ${srcdir}/restart/checkpoint.cc
    ${srcdir}/restart/checkpoint_error.cc
    ${srcdir}/restart/checkpoint_reader.cc
    ${srcdir}/restart/checkpoint_writer.cc
    ${srcdir}/restart/DataSet.cc
    ${srcdir}/restart/Group.cc
    ${srcdir}/restart/H5Checkpoint.cc
    ${srcdir}/restart/H5DataSet.cc
    ${srcdir}/restart/H5File.cc
    ${srcdir}/restart/H5Group.cc
    ${srcdir}/restart/summit_restart.cc
    ${srcdir}/schemes/dynamicMechanicalScheme.cc
    ${srcdir}/schemes/dynamicRelaxationMechanicalScheme.cc
    ${srcdir}/schemes/mechanicalScheme.cc
    ${srcdir}/schemes/scheme.cc
    ${srcdir}/schemes/staticMechanicalScheme.cc
    ${srcdir}/solvers/coupled_solver_through_internal_boundary.cc
    ${srcdir}/solvers/coupled_solver_through_internal_boundary_dynamics.cc
    ${srcdir}/solvers/coupled_solver_through_internal_boundary_explicit_explicit.cc
    ${srcdir}/solvers/coupled_solver_through_internal_boundary_front_tracking_dynamics.cc
    ${srcdir}/solvers/coupled_solver_through_internal_boundary_statics.cc
    ${srcdir}/solvers/coupled_solver_through_internal_boundary_statics_explicit.cc
    ${srcdir}/solvers/custom_newmark_step.cc
    ${srcdir}/solvers/damped_explicit_newmark_integrator_block_mass.cc
    ${srcdir}/solvers/damped_explicit_newmark_integrator_lump_mass.cc
    ${srcdir}/solvers/dynamic_relaxation.cc
    ${srcdir}/solvers/eigenvalues_solver.cc
    ${srcdir}/solvers/explicit_newmark_integrator.cc
    ${srcdir}/solvers/forward_euler_integrator.cc
    ${srcdir}/solvers/generalized_trapezoidal_family.cc
    ${srcdir}/solvers/impes_solver.cc
    ${srcdir}/solvers/implicit_dynamic_relaxation.cc
    ${srcdir}/solvers/implicit_newmark_integrator.cc
    ${srcdir}/solvers/implicit_newmark_integrator_with_fluid_loading.cc
    ${srcdir}/solvers/implicit_reaction_integration.cc
    ${srcdir}/solvers/linear_solver.cc
    ${srcdir}/solvers/monolithic_arclength_solver_petsc.cc
    ${srcdir}/solvers/monolithic_solver_petsc.cc
    ${srcdir}/solvers/newton_solver.cc
    ${srcdir}/solvers/newton_solver_line_search.cc
    ${srcdir}/solvers/parallel_arclength_solver_crisfield.cc
    ${srcdir}/solvers/peridynamics_integrator.cc
    ${srcdir}/solvers/petsc_ncg_solver.cc
    ${srcdir}/solvers/reaction_diffusion_solver.cc
    ${srcdir}/solvers/rk4.cc
    ${srcdir}/solvers/se_solver.cc
    ${srcdir}/solvers/shell_explicit_newmark_integrator.cc
    ${srcdir}/solvers/shell_ncg_solver_petsc.cc
    ${srcdir}/solvers/snes_solver.cc
    ${srcdir}/solvers/solver.cc
    ${srcdir}/solvers/staggered_solver_petsc.cc
    ${srcdir}/solvers/staggered_solver_petsc_front_tracking.cc
    ${srcdir}/solvers/staggered_solver_proppant_transport_petsc.cc
    ${srcdir}/solvers/static_solver.cc
    ${srcdir}/solvers/stiffness/snes_stiffness_parallel_PETSc.cc
    ${srcdir}/solvers/stiffness/stiffness.cc
    ${srcdir}/solvers/stiffness/stiffness_monolithic.cc
    ${srcdir}/solvers/stiffness/stiffness_monolithic_parallel_PETSc.cc
    ${srcdir}/solvers/stiffness/stiffness_monolithic_PETSc.cc
    ${srcdir}/solvers/stiffness/stiffnessADLIB.cc
    ${srcdir}/solvers/stiffness/stiffnessParallelPETSc.cc
    ${srcdir}/solvers/stiffness/stiffnessPETSc.cc
    ${srcdir}/solvers/summit_ncg_solver.cc
    ${srcdir}/solvers/thermal_model_solver.cc
    ${srcdir}/systems/black_oil_model_system.cc
    ${srcdir}/systems/contact_system.cc
    ${srcdir}/systems/diffusion_system.cc
    ${srcdir}/systems/dynamics_system.cc
    ${srcdir}/systems/matrix_free_system.cc
    ${srcdir}/systems/monolithic_system.cc
    ${srcdir}/systems/multi_systems_writer.cc
    ${srcdir}/systems/nonlinear_shell_dynamics_system.cc
    ${srcdir}/systems/nonlinear_shell_statics_system.cc
    ${srcdir}/systems/nonlinear_system.cc
    ${srcdir}/systems/peridynamics_system.cc
    ${srcdir}/systems/petsc_ncg_system.cc
    ${srcdir}/systems/reaction_diffusion.cc
    ${srcdir}/systems/sevf_system.cc
    ${srcdir}/systems/statics_system.cc
    ${srcdir}/systems/system.cc
    ${srcdir}/systems/system_writer.cc
    ${srcdir}/systems/thermal_model_system.cc
    ${srcdir}/utils/backtrace_util.cc
    ${srcdir}/utils/ostream_overload.cc
    ${srcdir}/utils/spatial_material_sort.cc
    ${srcdir}/utils/spatial_sort.cc
    ${srcdir}/utils/util.cc
    ${srcdir}/weakforms/axisymmetric_mechanics_region.cc
    ${srcdir}/weakforms/beam_contact_region.cc
    ${srcdir}/weakforms/beam_KL_TF_region.cc
    ${srcdir}/weakforms/black_oil_model_bulk_region.cc
    ${srcdir}/weakforms/black_oil_model_manifold_region.cc
    ${srcdir}/weakforms/black_oil_model_weak_form.cc
    ${srcdir}/weakforms/boundary_integral_fluid_region.cc
    ${srcdir}/weakforms/boundary_plateRM_region.cc
    ${srcdir}/weakforms/boundary_shellRM_region.cc
    ${srcdir}/weakforms/channel_advection_region.cc
    ${srcdir}/weakforms/channel_advection_weak_form.cc
    ${srcdir}/weakforms/channel_region.cc
    ${srcdir}/weakforms/channel_thermal_region.cc
    ${srcdir}/weakforms/common_interface_mechanics_region.cc
    ${srcdir}/weakforms/common_interface_poisson_region.cc
    ${srcdir}/weakforms/common_interface_transport_region.cc
    ${srcdir}/weakforms/dirichlet_channel_advection_region.cc
    ${srcdir}/weakforms/dirichlet_channel_thermal_region.cc
    ${srcdir}/weakforms/dirichlet_fabric_region.cc
    ${srcdir}/weakforms/dirichlet_hydro_mechanics_region.cc
    ${srcdir}/weakforms/dirichlet_linear_shell_region.cc
    ${srcdir}/weakforms/dirichlet_mechanics_region.cc
    ${srcdir}/weakforms/dirichlet_non_linear_shell_region.cc
    ${srcdir}/weakforms/dummy_region.cc
    ${srcdir}/weakforms/extended_dirichlet_hydro_mechanics_region.cc
    ${srcdir}/weakforms/extended_dirichlet_hydro_mechanics_region_implicit.cc
    ${srcdir}/weakforms/extended_hydro_mechanics_region.cc
    ${srcdir}/weakforms/extended_hydro_mechanics_region_implicit.cc
    ${srcdir}/weakforms/extended_poisson_manifold_region.cc
    ${srcdir}/weakforms/extended_poisson_manifold_region_artificial_compressibility.cc
    ${srcdir}/weakforms/fabric_region.cc
    ${srcdir}/weakforms/fluid_weak_form.cc
    ${srcdir}/weakforms/hydro_mechanics_region.cc
    ${srcdir}/weakforms/interface_3D_shell_region.cc
    ${srcdir}/weakforms/interface_fabric_region.cc
    ${srcdir}/weakforms/interface_linear_shell_region.cc
    ${srcdir}/weakforms/interface_mechanics_region.cc
    ${srcdir}/weakforms/interface_non_linear_shell_region.cc
    ${srcdir}/weakforms/interface_plateRM_region.cc
    ${srcdir}/weakforms/interface_shell_3D_region.cc
    ${srcdir}/weakforms/interface_shellRM_nonlinear_region.cc
    ${srcdir}/weakforms/interface_shellRM_region.cc
    ${srcdir}/weakforms/linear_shell_region.cc
    ${srcdir}/weakforms/mechanics_region.cc
    ${srcdir}/weakforms/mechanics_weak_form.cc
    ${srcdir}/weakforms/monolithic_bulk_interface_weak_form.cc
    ${srcdir}/weakforms/neighbor_finder.cc
    ${srcdir}/weakforms/neumann_region.cc
    ${srcdir}/weakforms/non_linear_shell_region.cc
    ${srcdir}/weakforms/peridynamics_ghost_region.cc
    ${srcdir}/weakforms/peridynamics_region.cc
    ${srcdir}/weakforms/peridynamics_region_modeI.cc
    ${srcdir}/weakforms/peridynamics_weak_form.cc
    ${srcdir}/weakforms/plateRM_region.cc
    ${srcdir}/weakforms/poisson_manifold_region.cc
    ${srcdir}/weakforms/poisson_manifold_weak_form.cc
    ${srcdir}/weakforms/reaction_diffusion_interface_region.cc
    ${srcdir}/weakforms/reaction_diffusion_interface_region_full_butterfly.cc
    ${srcdir}/weakforms/reaction_diffusion_region.cc
    ${srcdir}/weakforms/reaction_diffusion_weak_form.cc
    ${srcdir}/weakforms/region.cc
    ${srcdir}/weakforms/sevf_region.cc
    ${srcdir}/weakforms/sevf_weak_form.cc
    ${srcdir}/weakforms/shellRM_nonlinear_region.cc
    ${srcdir}/weakforms/shellRM_region.cc
    ${srcdir}/weakforms/singlephase_flow_bulk_region.cc
    ${srcdir}/weakforms/singlephase_flow_manifold_region.cc
    ${srcdir}/weakforms/singlephase_flow_weak_form.cc
    ${srcdir}/weakforms/stressed_interface.cc
    ${srcdir}/weakforms/thermal_model_bulk_region.cc
    ${srcdir}/weakforms/thermal_model_manifold_region.cc
    ${srcdir}/weakforms/thermal_model_weak_form.cc
    ${srcdir}/weakforms/transport_manifold_region.cc
    ${srcdir}/weakforms/transport_manifold_weak_form.cc
    ${srcdir}/weakforms/weak_form.cc
  )
  # and the link dependencies
  target_link_libraries(
    summit PUBLIC
    pyre::pyre
    tetra
    )
  set_target_properties(summit
    PROPERTIES
    ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/lib"
    )

  # install all the summit headers
  install(
    DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/src/
    DESTINATION ${SUMMIT_DEST_INCLUDE}
    FILES_MATCHING PATTERN *.h PATTERN *.icc
    )
  # libsummit and libjournal
  install(
    TARGETS summit tetra
    EXPORT summit-targets
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    )
  # all done
endfunction(summit_summitLib)

# build the summit python extension
function(summit_summitModule)
  # summit
  Python_add_library(summitmodule MODULE)
  # turn on the core macro
  set_target_properties(summitmodule PROPERTIES COMPILE_DEFINITIONS SUMMIT_CORE)
  # adjust the name to match what python expects
  set_target_properties(summitmodule PROPERTIES LIBRARY_OUTPUT_NAME summit)
  set_target_properties(summitmodule PROPERTIES SUFFIX ${PYTHON3_SUFFIX})
  # set the libraries to link against
  target_link_libraries(summitmodule PRIVATE summit tetra ${SUMMIT_EXTERN_LIBS})
  # add the sources
  target_sources(summitmodule PRIVATE
    extensions/summit/summit.cc
    extensions/summit/boundaryconditions.cc
    extensions/summit/capsulecheck.cc
    extensions/summit/exceptions.cc
    extensions/summit/fem.cc
    extensions/summit/io.cc
    extensions/summit/materials.cc
    extensions/summit/mesh.cc
    extensions/summit/metadata.cc
    extensions/summit/parallel.cc
    extensions/summit/schemes.cc
    extensions/summit/solvers.cc
    extensions/summit/systems.cc
    )
  # install
  install(
    TARGETS summitmodule
    LIBRARY
    DESTINATION ${SUMMIT_DEST_PACKAGES}/summit/extensions
    )
endfunction(summit_summitModule)

# build the summit package
function(summit_summitPackage)
  # install the sources straight from the source directory
  message(STATUS "SUMMIT_DEST_PACKAGES: ${SUMMIT_DEST_PACKAGES}")
  install(
    DIRECTORY python/
    DESTINATION ${SUMMIT_DEST_PACKAGES}/summit
    FILES_MATCHING PATTERN *.py
    )
  # all done
endfunction(summit_summitPackage)

