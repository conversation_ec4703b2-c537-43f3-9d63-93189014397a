#

# -*- cmake -*-
#
# michael a.g. a<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
# (c) 1998-2021 all rights reserved
#

# the function that lists explicitly the tests to be run and effects their implementation
function(summit_summitTests)
  # after the cc file must be a single string with space-separated wildcard
  # expressions of test clean up files, e.g. "*.h5 *.csv"
  
  summit_test_driver(TEST/serial/archivor/test_archivor.cc "" "*.csv")
  summit_test_driver(TEST/serial/axi-dynamics/3dMesh/3d-dynamics.cc "" "*.csv *.dat")
  summit_test_driver(TEST/serial/axi-dynamics/axi-dynamics.cc "" "*.csv")
  summit_test_driver(TEST/serial/axisymmetric/axisymmetric.cc "" "foo")
  summit_test_driver(TEST/serial/beam_BTF/beam_BTF.cc "" "tip_displacement_file")
  summit_test_driver(TEST/serial/beam_gravity/beam_gravity.cc "" "*.dat *.msh input")
  summit_test_driver(TEST/serial/beam_gravity/beam_gravity_shellRM_body_force.cc "" "*.dat *.msh input")
  summit_test_driver(TEST/serial/beam_gravity/beam_gravity_shellRM_concentrated_load.cc "" "*.dat *.msh input")
  summit_test_driver(TEST/serial/body-forces/barHangingUnderOwnWeight.cc "" "")
  summit_test_driver(TEST/serial/body-forces/body-forces-confined.cc "" "")
  summit_test_driver(TEST/serial/body-forces/body-forces-no-confined.cc "" "")
  summit_test_driver(TEST/serial/body-forces/body-forces.cc "" "")
  summit_test_driver(TEST/serial/body-forces/test_unconfined-body-forces.cc "" "")
  summit_test_driver(TEST/serial/boundary/test-boundary.cc "" "")
  summit_test_driver(TEST/serial/channel-advection/lubrication-advection/test-lubrication-advection.cc "" "")
  summit_test_driver(TEST/serial/channel-advection/simple-advection/test-simple-advection.cc "" "")
  summit_test_driver(TEST/serial/channel-advection/step-advection/test-channel-advection.cc "" "")
  summit_test_driver(TEST/serial/channel-flow-monolithic/channel-flow-monolithic.cc "" "")
  summit_test_driver(TEST/serial/channel-heat-transfer/diffusion-advection-robin/test-simple-channel-heat-transfer-robin.cc "" "")
  summit_test_driver(TEST/serial/channel-heat-transfer/pure-advection/test-simple-channel-heat-transfer-advection.cc "" "")
  summit_test_driver(TEST/serial/channel-heat-transfer/pure-diffusion/test-simple-channel-heat-transfer.cc "" "")
  summit_test_driver(TEST/serial/comparisonDirectIterativePETScSolvers/comparison-PETSc-Solvers.cc "" "")
  summit_test_driver(TEST/serial/cube-stretch/cube-stretch-with-tetra.cc "" "")
  summit_test_driver(TEST/serial/cube-stretch/cube-stretch.cc "" "")
  summit_test_driver(TEST/serial/cube-stretch/user-interfaces/pureCpp/cube-stretch-with-tetra-pureCpp.cc "" "")
  summit_test_driver(TEST/serial/cube-stretch/user-interfaces/scheme/cube-stretch-with-tetra-scheme.cc "" "")
  summit_test_driver(TEST/serial/dg-multi-materials/mapElement2Material.cc "" "")
  summit_test_driver(TEST/serial/disconnected_mesh/disconnected_mesh.cc "" "*.dat *.msh input")
  summit_test_driver(TEST/serial/falling_manifold_with_boundary/falling_manifold_with_boundary.cc "" "*.dat output_checkpoints")
  summit_test_driver(TEST/serial/falling_manifolds_without_boundary/falling_manifolds_without_boundary.cc "" "*.dat")
  summit_test_driver(TEST/serial/falling_ribbon/falling_ribbon.cc "" "*.msh *.dat output*")
  summit_test_driver(TEST/serial/high-order/high-order_connectivity-mesh.cc "tmp/square_4elem_TriP_CG.msh" "")
  summit_test_driver(TEST/serial/high-order/high-order_connectivity_tet.cc "3 1" "")
  summit_test_driver(TEST/serial/high-order/high-order_connectivity_tet2.cc "1" "")
  summit_test_driver(TEST/serial/high-order/high-order_connectivity_tri.cc "3 1" "")
  summit_test_driver(TEST/serial/high-order/high-order_connectivity_tri2.cc "1" "")
  summit_test_driver(TEST/serial/high-order/high-order_functionSpace_DG_discretization.cc "" "")
  summit_test_driver(TEST/serial/high-order/high-order_node-coordinates_tet.cc "3 0" "")
  summit_test_driver(TEST/serial/high-order/high-order_node-coordinates_tri.cc "" "")
  summit_test_driver(TEST/serial/high-order/high-order_nodes-elements-DG.cc "" "")
  summit_test_driver(TEST/serial/high-order/high-order_prism_tet.cc "" "")
  summit_test_driver(TEST/serial/hydraulicFracture-comparisonMonolithicVsDecoupled/test-monolithic-vs-decoupled.cc "" "")
  summit_test_driver(TEST/serial/implicit/implicit.cc "" "implicit-test-tet-0000")
  summit_test_driver(TEST/serial/implicit_newmark/uniform_velocity.cc "" "")
  summit_test_driver(TEST/serial/kubc/2D/kubc-2D.cc "" "")
  summit_test_driver(TEST/serial/kubc/3D/kubc-3D.cc "" "")
  summit_test_driver(TEST/serial/mesh-connected-components/main-connected-components.cc "spherePlateContact.summit 2" "")
  summit_test_driver(TEST/serial/mesh-connected-components/test-connected-components.cc "" "")
  summit_test_driver(TEST/serial/mesh-partitioning/test-mesh-partitioning.cc "" "")
  summit_test_driver(TEST/serial/mesh-write/main.cc "" "")
  summit_test_driver(TEST/serial/multi-element-sets/multi-element-sets.cc "" "")
  summit_test_driver(TEST/serial/parametric-material/test-parametric-material.cc "" "")
  summit_test_driver(TEST/serial/patch/patch-serial.cc "" "")
  summit_test_driver(TEST/serial/patch2d/patch2d-disp-compareCG-DG.cc "" "")
  summit_test_driver(TEST/serial/patch2d/patch2d-disp.cc "" "")
  summit_test_driver(TEST/serial/patch2d/patch2d-disp-recession.cc "" "")
  summit_test_driver(TEST/serial/patch2d/patch2d-force.cc "" "")
  summit_test_driver(TEST/serial/patch2d/patch2d-force-recession.cc "" "")
  summit_test_driver(TEST/serial/patch3d/patch3d-disp-compareCG-DG.cc "" "")
  summit_test_driver(TEST/serial/patch3d/patch3d-disp.cc "" "")
  summit_test_driver(TEST/serial/patch3d/patch3d-disp-recession.cc "" "")
  summit_test_driver(TEST/serial/patchDG/patchDG-serial.cc "" "")
  summit_test_driver(TEST/serial/patchDG/unconfined-imposed-displacement.cc "" "")
  summit_test_driver(TEST/serial/patchDG/unconfined-top-face-load.cc "" "")
  summit_test_driver(TEST/serial/peri/peri2D.cc "" "")
  summit_test_driver(TEST/serial/permeaFlow_inclinedPad_DirichletBCs/permeaFlow-inclinedPad-1DManifold-DirichletBC.cc "" "")
  summit_test_driver(TEST/serial/permeaFlow_inclinedPad_DirichletBCs/permeaFlow-inclinedPad-2DManifold-DirichletBC.cc "" "")
  summit_test_driver(TEST/serial/permeaFlow_inclinedPad_NeumannBCs/permeaFlow-inclinedPad-2DManifold-NeumannBC.cc "" "")
  summit_test_driver(TEST/serial/plate/plate-clamped-serial.cc "" "*.csv")
  summit_test_driver(TEST/serial/plate/plate-serial.cc "" "*.csv")
  summit_test_driver(TEST/serial/semi-infinite-crack/test-semi-infinite-crack.cc "" "")
  summit_test_driver(TEST/serial/stokeslet-beam/stokeslet_beam.cc "" "")
  summit_test_driver(TEST/serial/superelastic-coupled/test-superelastic-coupled.cc "" "force-elong.txt")
  summit_test_driver(TEST/serial/taylor/taylor2D.cc "" "")
  summit_test_driver(TEST/serial/test-SRT/main.cc "" "")
  summit_test_driver(TEST/serial/tetra-parametric-mat/2tets/test-tetra-parametric-mat.cc "" "*.msh parametric.mat")
  summit_test_driver(TEST/serial/tetra-parametric-mat/rock/test-tetra-parametric-mat-rock.cc "" "*.msh parametric.mat")
  summit_test_driver(TEST/serial/transport_step_profile_1D/step.cc "" "")
  summit_test_driver(TEST/serial/waveDG/test-waveDG.cc "" "")

  #summit_test_driver_mpi(2 TEST/parallel-mpi/para-shell/cpp/para-shell.cc --mesh ../beam-4elm.msh --constitutive ../materials.dat)
  summit_test_driver_mpi(2 TEST/parallel-mpi/hydraulic-fracture-monolithic/test-parallel-monolithic.cc "" "*.h5")
  summit_test_driver_mpi(2 TEST/parallel-mpi/manifold-com-map/twoPartitionsWithSubdivision.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/manifold-com-map/twoPartitionsWithoutSubdivision.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/mpi-test/test-mpi-metis-4Tet1.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/mpi-test/test-mpi-metis-4Tet2.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/mpi-test/test-mpi-metis-cubeTet1CG.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/mpi-test/test-mpi-metis-cubeTet2CG.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/mpi-test/test-reduce-2Tet1CG.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/para-DG-multi-material/para-DG-multi-material.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/para-setting-param/para-setting-param.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/para-shell/cpp/para-shell.cc "--mesh ../beam.msh --constitutive ../materials.dat" "*.csv")
  summit_test_driver_mpi(2 TEST/parallel-mpi/parallel-Newton/parallel_newton_test.cc "" "strain-stress")
  summit_test_driver_mpi(2 TEST/parallel-mpi/parallel-implicit/bar_test.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/parallel-mesh-connected-components/test-connected-components-parallel.cc "spherePlateContact.summit 2" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/parallel-petsc/PETScKSP_uniaxialTension_with_CG.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/parallel-petsc/PETScKSP_uniaxialTension_with_DG.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/parallel-petsc/PETScSNES_uniaxialTension_with_CG.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/parallel-petsc/PETScSNES_uniaxialTension_with_DG.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/patch/patch.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/petsc-manifold/petsc-manifold.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/petsc-mech/petsc-mech-mpi.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/pure-bending-2D/test-2D-bending.cc "--mesh ./beam-finer.msh --constitutive ./materials.dat" "*.csv")
  summit_test_driver_mpi(2 TEST/parallel-mpi/single-phase-flow/cube-implicit/cube-pressure-bc-backward.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/single-phase-flow/cube-implicit/cube-pressure-bc-trapezoidal.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/single-phase-flow/cube/cube-flow-bc.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/tetra-parametric-mat/tetra-param.cc "" "mesh.msh parametric.mat")

  if(WITH_HDF5)
      summit_test_driver_mpi(2 TEST/parallel-mpi/restart-static/restart-static.cc "" "*.h5")
  endif(WITH_HDF5)

  if(WITH_GMSH)
    summit_test_driver(TEST/serial/shellRM-patch/shellRM-bending-patch.cc "" "")
    summit_test_driver(TEST/serial/shellRM-patch/shellRM-membrane-patch.cc "" "")
  endif(WITH_GMSH)

  # summit_test_driver_mpi(2 TEST/parallel-mpi/parallel-DG/parallel-DG.cc --mesh ./beam-4elm.msh --constitutive ./materials.dat)
  # summit_test_driver_mpi(4 TEST/parallel-mpi/channel-advection/heat-transfer/test-parallel-channel-heat-transfer.cc "./" "*.h5")
  #summit_test_driver_mpi(2 TEST/parallel-mpi/channel-advection/lubrication-advection/test-parallel-lubrication-advection.cc "" "") # this one is failing after a long run (also in mm)
  summit_test_driver_mpi(2 TEST/parallel-mpi/arclength-solver/arclength-solver.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/communication-maps/2D/comm-maps-2Tri.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/communication-maps/3D/comm-maps-2Tets.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/communication-maps/cube-mesh-with-tetra/comm-maps-cube-with-tetra.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/communication-maps/cube-mesh/comm-maps-cube.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/communication-maps/square-mesh/comm-maps-square.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/dg-material-wise/discretizeTetP_byMaterials.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/dr-mech/dr-mech-mpi.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/mpi-stl/mpistl.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/para-cohesive-DG/para-cohesive-DG_2D.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/para-cohesive-DG/para-cohesive-DG_3D.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/para-coupled/para-coupled.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/parallel-DG/parallel-DG.cc "--mesh ./beam.msh --constitutive ./materials.dat" "*.csv")
  summit_test_driver_mpi(2 TEST/parallel-mpi/patch-test-czm/patch-test-czm.cc "" "*.csv")
  summit_test_driver_mpi(2 TEST/parallel-mpi/patch-test-czm/patch-test-recontacting.cc "" "*.csv")
  summit_test_driver_mpi(2 TEST/parallel-mpi/shock3D/test-shock3D-mpi.cc "" "")
  summit_test_driver_mpi(2 TEST/parallel-mpi/tetra-with-metis/reduce/test-tetra-elByElPartitioner-2Tet.cc "" "*.dat")
  summit_test_driver_mpi(2 TEST/parallel-mpi/tetra-with-metis/reduce/test-tetra-metis-4Tets.cc "" "*.dat")
  summit_test_driver_mpi(2 TEST/parallel-mpi/tetra-with-metis/test-tetra-metis-4Tet1.cc "" "*.dat")
  summit_test_driver_mpi(2 TEST/parallel-mpi/tetra-with-metis/test-tetra-metis.cc "" "*.dat")
  summit_test_driver_mpi(2 TEST/parallel-mpi/uniform-strain-rate/test-parallel-uniform-strain-rate.cc "" "*.h5")
  summit_test_driver_mpi(2 TEST/parallel-mpi/uniform-strain-rate/with-tetra/uniform-strain-rate.cc "" "")
  summit_test_driver_mpi(2 TEST/unit/boundaryconditions/element_boundary_conditions_mpi.cc "" "")
  summit_test_driver_mpi(4 TEST/unit/boundaryconditions/neumann_boundary_conditions_test_parallel.cc "" "")
  
  summit_test_driver(TEST/unit/elements/element_quadrature_field_test.cc "" "")
  summit_test_driver(TEST/unit/elements/element_set_TetP_TetP_test.cc "" "")
  summit_test_driver(TEST/unit/elements/element_set_TetP_deep_copy_test.cc "" "")
  summit_test_driver(TEST/unit/elements/element_set_TetP_test.cc "" "")
  summit_test_driver(TEST/unit/elements/element_set_Tri1_Tri1_test.cc "" "")
  summit_test_driver(TEST/unit/elements/element_set_TriP_TriP_deep_copy_test.cc "" "")
  summit_test_driver(TEST/unit/elements/element_set_TriP_TriP_test.cc "" "")
  summit_test_driver(TEST/unit/elements/element_set_TriP_deep_copy_test.cc "" "")
  summit_test_driver(TEST/unit/elements/element_set_TriP_test.cc "" "")
  summit_test_driver(TEST/unit/elements/element_set_boundary_test.cc "" "")
  summit_test_driver(TEST/unit/elements/element_set_manifold_SegP_test.cc "" "")
  summit_test_driver(TEST/unit/elements/element_set_manifold_TriP_test.cc "" "")
  summit_test_driver(TEST/unit/elements/element_set_monolithic_test.cc "" "")
  summit_test_driver(TEST/unit/utils/math-util_test.cc "" "")
  summit_test_driver(TEST/unit/utils/saveFile_test.cc "" "")
  summit_test_driver(TEST/unit/utils/sorted-array-util_test.cc "" "")
  summit_test_driver(TEST/unit/utils/spatial_sort_test.cc "" "")
  summit_test_driver(TEST/unit/utils/string-util_test.cc "" "")
  summit_test_driver(TEST/unit/utils/utils_test.cc "" "")
  summit_test_driver(TEST/unit/weakforms/axisymmetric_region_test.cc "" "")
  summit_test_driver(TEST/unit/weakforms/beam_contact_region_test.cc "" "")
  summit_test_driver(TEST/unit/weakforms/boundary_integral_fluid_region_test.cc "" "")
  summit_test_driver(TEST/unit/weakforms/elasticity_tensor_test.cc "" "")
  summit_test_driver(TEST/unit/weakforms/interface_region_test.cc "" "")
  summit_test_driver(TEST/unit/weakforms/interface_region_test_3d.cc "" "")
  summit_test_driver(TEST/unit/weakforms/interface_region_test_fullStabilizationTerm.cc "" "")
  summit_test_driver(TEST/unit/weakforms/mechanics_region_test.cc "" "")

  if(WITH_HDF5)
    summit_test_driver(TEST/unit/restart/test-restart-explicit-dynamics.cc "" "")
    summit_test_driver(TEST/unit/weakforms/poisson_manifold_region_test.cc "" "")
    summit_test_driver(TEST/unit/weakforms/shell_regions_test.cc "" "")
  endif(WITH_HDF5)
  
  #summit_test_driver(TEST/unit/materials/granular-compaction/Hydrostatic/hydrostatic.cc "" "") # never worked, apparently
  summit_test_driver(TEST/unit/boundaryconditions/element_boundary_conditions.cc "" "")
  summit_test_driver(TEST/unit/boundaryconditions/nodal_boundary_conditions.cc "" "")
  summit_test_driver(TEST/unit/boundaryconditions/neumann_boundary_conditions_test.cc "" "")
  summit_test_driver(TEST/unit/fem/basis_functions_reference_SegP.cc "" "")
  summit_test_driver(TEST/unit/fem/basis_functions_reference_TetP.cc "" "")
  summit_test_driver(TEST/unit/fem/basis_functions_reference_TriP.cc "" "")
  summit_test_driver(TEST/unit/fem/discretizeTri1DG.cc "" "")
  summit_test_driver(TEST/unit/fem/discretized_boundary.cc "" "")
  summit_test_driver(TEST/unit/fem/function_space_copy_test.cc "" "")
  summit_test_driver(TEST/unit/fem/move_mesh_function_space.cc "" "")
  summit_test_driver(TEST/unit/fem/nodal_field.cc "" "")
  summit_test_driver(TEST/unit/fem/reference_element_coordinates.cc "" "")
  summit_test_driver(TEST/unit/io/vtk/mesh_writer_vtk.cc "" "")
  summit_test_driver(TEST/unit/io/vtk/mesh_writer_vtk_lagrange.cc "" "")
  if (WITH_YAML_CPP)
    summit_test_driver(TEST/unit/io/yaml/input_reader_yaml.cc "--input input.yaml" "")
  endif(WITH_YAML_CPP)
  summit_test_driver(TEST/unit/materials/J2_linear/j2Linear.cc "" "")
  summit_test_driver(TEST/unit/materials/J2_plastic/ConsistencyTest/j2_plastic_jc_consistency_test.cc "" "")
  summit_test_driver(TEST/unit/materials/multiphysics/ulm_concrete/ulm_concrete_consistency_test.cc "" "")
  summit_test_driver(TEST/unit/materials/multiphysics/pilling_bedworth_swelling_heat_conducting_eyring_creep/pilling_bedworth_swelling_heat_conducting_eyring_creep_inelastic_flow_utilities_test.cc "" "")
  summit_test_driver(TEST/unit/materials/J2_plastic/UniformStrainRate/JC_Uniaxial_Test/J2_Plasticity_JC_UniaxialTest.cc "" "")
  summit_test_driver(TEST/unit/materials/J2_plastic/UniformStrainRate/JC_Damage_Uniaxial_Test/J2_Plasticity_JC_Damage_UniaxialTest.cc "" "")
  summit_test_driver(TEST/unit/materials/J2_plastic/UniformStrainRate/Pow_Uniaxial_Test/J2_Plasticity_Pow_UniaxialTest.cc "" "")
  summit_test_driver(TEST/unit/materials/J2_plastic/UnitTest_J2PlasticJC.cc "" "")
  summit_test_driver(TEST/unit/materials/J2_plastic/jc_read_material_test.cc "" "")
  summit_test_driver(TEST/unit/materials/Prony_viscoelastic/test-material_test_Prony.cc "" "")
  summit_test_driver(TEST/unit/materials/adiabatic_mechanics/ArmeroSimo_test.cc "" "")
  summit_test_driver(TEST/unit/materials/bcc_cuitino/bcc_cuitino.cc "" "")
  summit_test_driver(TEST/unit/materials/camclay/camclay.cc "" "")
  summit_test_driver(TEST/unit/materials/ceramic/test_ceramic.cc "" "")
  summit_test_driver(TEST/unit/materials/cohesiveDG/cohesiveBrittleMaterial_test.cc "" "")
  summit_test_driver(TEST/unit/materials/elastic/ELASTIC_EOS_GLASS_TEST/elastic_eos_glass_test.cc "" "")
  summit_test_driver(TEST/unit/materials/elastic/elastic_test.cc "" "")
  summit_test_driver(TEST/unit/materials/exponential/eig33_test.cc "" "")
  summit_test_driver(TEST/unit/materials/gel_ISN/test-material_test_gelISN.cc "" "")
  summit_test_driver(TEST/unit/materials/gent-compressible/gent_consistency_test.cc "" "")
  summit_test_driver(TEST/unit/materials/gent-hyperelastic/gent_test.cc "" "")
  summit_test_driver(TEST/unit/materials/glass-Densification/Compression-Behavior/Compression-Levels/compression_levels.cc "" "")
  summit_test_driver(TEST/unit/materials/glass-Densification/Compression-Behavior/compression.cc "" "")
  summit_test_driver(TEST/unit/materials/glass-Densification/CyclicLoading/glass-Densification_CyclicLoading.cc "" "")
  summit_test_driver(TEST/unit/materials/glass-Densification/Pressure-Density/pressure-density.cc "" "")
  summit_test_driver(TEST/unit/materials/glass-Densification/Pressure-Shear/pressure_shear.cc "" "")
  summit_test_driver(TEST/unit/materials/glass-Densification/Shear-Pressure/WithUnloading/shear_pressure_WithUnloading.cc "" "")
  summit_test_driver(TEST/unit/materials/glass-Densification/Shear-Pressure/shear_pressure.cc "" "")
  summit_test_driver(TEST/unit/materials/glass-Densification/ConsistencyTest/glass_consistency_test.cc "" "")
  summit_test_driver(TEST/unit/materials/granular-compaction/Shear/shear.cc "" "")
  summit_test_driver(TEST/unit/materials/hyperelasticity/ogden_hyperfoam.cc "" "")
  summit_test_driver(TEST/unit/materials/inelastic_eos/BeckerGlassTest/Becker_glass.cc "" "")
  summit_test_driver(TEST/unit/materials/inelastic_eos/SimpleGlassTest/simple_glass.cc "" "")
  summit_test_driver(TEST/unit/materials/neohookean/neohookean_test.cc "" "")
  summit_test_driver(TEST/unit/materials/neohookean/test-material_test_nh.cc "" "")
  summit_test_driver(TEST/unit/materials/parametric_elastic/parametric_elastic_test.cc "" "")
  summit_test_driver(TEST/unit/materials/parametric_poroelastic/parametric_poroelastic_test.cc "" "")
  summit_test_driver(TEST/unit/materials/polyconvex/polyconvex_test.cc "" "")
  summit_test_driver(TEST/unit/materials/polyconvex_orthotropic/polyconvexOrthotropic_test.cc "" "")
  summit_test_driver(TEST/unit/materials/reaction_diffusion/ArmeroSimoThermal/ArmeroSimoThermal_test.cc "" "")
  
  if (WITH_CANTERA)
    summit_test_driver(TEST/unit/materials/reaction_diffusion/multispecies_diffusion_consistency_test.cc "" "")
    summit_test_driver(TEST/unit/materials/reaction_diffusion/multispecies_reaction_diffusion_consistency_test.cc "" "")
  endif(WITH_CANTERA)
  
  summit_test_driver(TEST/unit/materials/reaction_diffusion/nonlinear_conduction_consistency_test.cc "" "")
  summit_test_driver(TEST/unit/materials/shell/test_fabric.cc "" "")
  summit_test_driver(TEST/unit/materials/sma/sma.cc "" "")
  summit_test_driver(TEST/unit/materials/smc/smc_test.cc "" "")
  summit_test_driver(TEST/unit/materials/uhmwp/uhmwp_test.cc "" "")
  summit_test_driver(TEST/unit/materials/vumat/cortical/test_vumat_cortical.cc "" "")
  summit_test_driver(TEST/unit/materials/vumat/cortical/test_vumat_cortical_damage.cc "" "")
  summit_test_driver(TEST/unit/materials/vumat/ice-crrel/test-umat_ice.cc "" "")
  summit_test_driver(TEST/unit/materials/vumat/neohookean/vumat_neohookean.cc "" "")
  summit_test_driver(TEST/unit/mathlib/cholesky_decomposition.cc "" "")
  summit_test_driver(TEST/unit/mathlib/eig.cc "" "")
  summit_test_driver(TEST/unit/mathlib/quadrature.cc "" "")
  summit_test_driver(TEST/unit/mathlib/sparse/sparse_solver_adlib.cc "" "")
  summit_test_driver(TEST/unit/mesh/boundary_graph_test.cc "" "")
  summit_test_driver(TEST/unit/mesh/boundary_test.cc "" "")
  summit_test_driver(TEST/unit/mesh/compare_mesh_entities_test.cc "" "")
  summit_test_driver(TEST/unit/mesh/filtered_boundary_test.cc "" "")
  summit_test_driver(TEST/unit/mesh/geom_object_test.cc "" "")
  summit_test_driver(TEST/unit/mesh/internal_boundary_test.cc "" "")
  summit_test_driver(TEST/unit/mesh/map_color_to_mesh_entities_test.cc "" "")
  summit_test_driver(TEST/unit/mesh/mesh_entity_test.cc "" "")
  summit_test_driver(TEST/unit/mesh/mesh_test.cc "" "")
  summit_test_driver(TEST/unit/mesh/partition_boundary_test.cc "" "")
  summit_test_driver(TEST/unit/mesh/partitioner_test.cc "" "")
  summit_test_driver(TEST/unit/mesh/property_sheet_read_test.cc "" "")
  summit_test_driver(TEST/unit/parallel/communication_manager.cc "" "")
  summit_test_driver(TEST/unit/solvers/coupled_solver_through_internal_boundary_dynamics_test.cc "" "")
  summit_test_driver(TEST/unit/solvers/coupled_solver_through_internal_boundary_statics_test.cc "" "")
  summit_test_driver(TEST/unit/solvers/stiffness/stiffness_monolithic_test.cc "" "")
  summit_test_driver(TEST/unit/solvers/stiffness/stiffness_test.cc "" "")
  summit_test_driver(TEST/unit/systems/mesh_recession_static_system.cc "" "")
  summit_test_driver(TEST/unit/systems/system-writer-test.cc "" "")
  summit_test_driver_mpi(2 TEST/unit/mesh/parpartition/test-parpartition.cc "" "")
  summit_test_driver_mpi(2 TEST/unit/parallel/communicator.cc "" "")
  summit_test_driver_mpi(2 TEST/unit/parallel/mpi_summit.cc "" "")
  summit_test_driver_mpi(3 TEST/unit/parallel/quadrature_communicator.cc "" "")
  
  if(WITH_SFC)
    summit_test_driver(TEST/serial/contact/contact_surface_test.cc "" "")
    summit_test_driver(TEST/serial/contact/contact_solver_test.cc "" "")

    summit_test_driver_mpi(2 TEST/parallel-mpi/contact/contact_parallel_test.cc "" "")
  endif(WITH_SFC)
  
  summit_test_driver(TEST/serial/taylor-axi/taylor-axi.cc "" "")

  if(WITH_GMSH AND WITH_PETSC)
      summit_test_driver(TEST/serial/ribbon_twist/ribbon_twist.cc "" "")
  endif(WITH_GMSH AND WITH_PETSC)

  summit_test_driver(TEST/serial/channel-flow/channel-flow.cc "" "")
  summit_test_driver(TEST/serial/damping/constant-damping-dynamic-equilibrium-problem.cc "" "")
  summit_test_driver(TEST/serial/damping/sanity-check.cc "" "")
  summit_test_driver(TEST/serial/dg-material-wise/test-discretizeTetP_byMaterials.cc "" "")
  summit_test_driver(TEST/serial/material-test/test-material_test_vumat_polyurea.cc "" "")
  summit_test_driver(TEST/serial/mesh-import/from_mesh-file/mesh-import-from-mesh-file.cc "" "")
  summit_test_driver(TEST/serial/mesh-import/from_topology-geometry/mesh-import-from-topology-geometry.cc "" "")
  summit_test_driver(TEST/serial/rod-draining/rod-draining.cc "" "") #compiles and runs but gives off-results
  summit_test_driver(TEST/serial/shock2D/test-shock2D.cc "" "output-0000")
  summit_test_driver(TEST/serial/tetra/test-tetra-with-fields.cc "" "*.msh")
  summit_test_driver(TEST/serial/tetra/test-tetra.cc "" "*.msh")
  summit_test_driver(TEST/serial/uniform-strain-rate/2D/uniform-strain-rate-4Tri1CG.cc "" "")
  summit_test_driver(TEST/serial/uniform-strain-rate/2D/uniform-strain-rate-8Tri1CG.cc "" "")
  summit_test_driver(TEST/serial/uniform-strain-rate/3D/uniform-strain-rate-3D.cc "" "")

  # broken tests
  if(0)
    
    summit_test_driver(TEST/serial/3D-to-shell-wave-beam/3D-to-shell-wave-beam.cc "" "")
    summit_test_driver(TEST/serial/falling_ribbons/falling_ribbons.cc "" "*.dat") # seems like worth rescuing at some point
    summit_test_driver(TEST/serial/high-order/tmp/connectivity-mesh.cc "" "")
    summit_test_driver(TEST/serial/high-order/tmp/connectivity-tet.cc "" "")
    summit_test_driver(TEST/serial/high-order/tmp/connectivity-tet2.cc "" "")
    summit_test_driver(TEST/serial/high-order/tmp/connectivity-tri.cc "" "") # compile and run but need mesh arguments
    summit_test_driver(TEST/serial/high-order/tmp/connectivity-tri2.cc "" "")
    summit_test_driver(TEST/serial/high-order/tmp/functionSpace_DG_discretization.cc "" "")
    summit_test_driver(TEST/serial/implicit_newmark/uniform_strain_rate.cc "" "")
    summit_test_driver(TEST/serial/kubc/2D/2-elements/test-kubc-2D-2elements.cc "" "")
    summit_test_driver(TEST/serial/recontact-under-compression/recontact-under-compression-2D.cc "" "")
    summit_test_driver(TEST/serial/recontact-under-compression/recontact-under-compression-3D.cc "" "")
    summit_test_driver(TEST/serial/taylor/test-shock2D.cc "" "")
    summit_test_driver(TEST/serial/visu-mesh/visu-mesh.cc "" "") # builds but needs attention
    summit_test_driver(TEST/unit/materials/J2_plastic/UniaxialTests/JC_Damage_Uniaxial_Test/J2_Plasticity_JC_Damage_UniaxialTest.cc "" "")
    summit_test_driver(TEST/unit/materials/J2_plastic/UniaxialTests/Pow_Uniaxial_Test/J2_Plasticity_Pow_UniaxialTest.cc "" "")
    summit_test_driver(TEST/unit/materials/J2_plastic/jc_test.cc "" "")
    summit_test_driver(TEST/unit/materials/elastic/elastic_eos_test.cc "" "")

    summit_test_driver_mpi(2 TEST/parallel-mpi/mpi-test/test-mpi-8Tet.cc "" "")
    summit_test_driver_mpi(2 TEST/parallel-mpi/rod-draining/rod-draining.cc "" "")
    summit_test_driver_mpi(2 TEST/parallel-mpi/shock3D/test-restart.cc "" "")
    summit_test_driver_mpi(2 TEST/parallel-mpi/shock3D/with-tetra/test-shock3D-mpi-tetra.cc "" "")

    summit_test_driver(TEST/python/taylor/taylor2D.cc "" "")

  endif(0)

  summit_test_python(TEST/python/fem/fem-create.py)
  summit_test_python(TEST/python/fem/fem-discretize.py)
  summit_test_python(TEST/python/fem/fem-sanity.py)
  summit_test_python(TEST/python/fem/fem-tuplecheck.py)
  summit_test_python(TEST/python/fem/fem-write.py)
  summit_test_python(TEST/python/implicit/python-implicit.py ".")
  summit_test_python(TEST/python/materials/materials-read.py)
  summit_test_python(TEST/python/materials/materials-sanity.py)
  summit_test_python(TEST/python/mesh/mesh-gmsh.py)
  summit_test_python(TEST/python/mesh/mesh-read.py "patch1_Tri1CG.msh")
  summit_test_python(TEST/python/mesh/mesh-sanity.py)
  summit_test_python(TEST/python/newton/newton.py)
  summit_test_python(TEST/python/parallel/parallel-read.py)
  summit_test_python(TEST/python/parallel/parallel-sanity.py)
  summit_test_python(TEST/python/patch2d/patch2d.py "./")
  summit_test_python(TEST/python/patch3d/patch3d.py "./")
  summit_test_python(TEST/python/taylor/taylor.py "./")
  summit_test_python(TEST/python/vtk/vtkTest.py "./")
  summit_test_python(TEST/python/waveShell/shell.py --mesh beam.msh --constitutive materials.dat)
  
endfunction(summit_summitTests)

# register a test based on a compiled driver, comm
function(summit_test_driver testfile command_arguments test_products)

  # split
  get_filename_component(path ${testfile} DIRECTORY)
  get_filename_component(base ${testfile} NAME_WE)

  # replace path separators with dots
  string(REPLACE "/" "." stem ${path})

  # buld the target 
  set(target "${stem}.${base}")

  #message(STATUS "test file: ${testfile}")
  #message(STATUS "test path: ${path}")
  #message(STATUS "test base: ${base}")
  #message(STATUS "stem: ${stem}")

  # schedule it to be compiled
  add_executable(${target} ${testfile})
  
  # link against my libraries and external dependencies, this adds both the libraries and their
  # locations  to the link line
  #message(STATUS "SUMMIT_EXTERN_LIBS: ${SUMMIT_EXTERN_LIBS}")
  target_link_libraries(${target} PUBLIC summit tetra pyre::pyre ${SUMMIT_EXTERN_LIBS})

  # define test clean up products, some usual summit default products, optional assumed to be
  # provided in ${ARGN}
  set(test_products "*.vtu *.pvtu ${test_products}")
  set(clean_test_products "rm -rf ${test_products}")
  
  # make it a test case
  #message(STATUS "Target (serial): ${target}")
  add_test(
    NAME ${target}
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}/${path}
    COMMAND sh -c "${clean_test_products}; ${RUNTIME_OUTPUT_DIRECTORY}/${target} ${command_arguments} && ${clean_test_products}"
    )

  set_target_properties( ${target}
    PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    )
  
  # message(STATUS "Added test driver: ${target} to the pile")
  # all done
endfunction(summit_test_driver)

# register a parallel test case based on a compiled driver
function(summit_test_driver_mpi slots testfile command_arguments test_products)
  # split
  get_filename_component(path ${testfile} DIRECTORY)
  get_filename_component(base ${testfile} NAME_WE)

  # replace path separators with dots
  string(REPLACE "/" "." stem ${path})

  # buld the target 
  set(target "${stem}.${base}")

  # schedule it to be compiled
  add_executable(${target} ${testfile})
  # with some macros
  target_compile_definitions(${target} PRIVATE SUMMIT_CORE WITH_MPI)
  # link against my libraries
  target_link_libraries(${target} PUBLIC summit tetra pyre::pyre ${SUMMIT_EXTERN_LIBS} MPI::MPI_CXX)

  # define test clean up products, some usual summit default products, optional assumed to be
  # provided in ${test_products}
  set(test_products "*.vtu *.pvtu ${test_products}")
  set(clean_test_products "rm -rf ${test_products}")
  
  # make it a test case
  #message(STATUS "CMAKE_SOURCE_DIR: ${CMAKE_SOURCE_DIR}, path: ${path}, RUNTIME_OUTPUT_DIRECTORY: ${RUNTIME_OUTPUT_DIRECTORY}")
  add_test(
    NAME ${target}
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}/${path}
    COMMAND
    sh -c
    "${MPIEXEC_EXECUTABLE} ${MPIEXEC_NUMPROC_FLAG} ${slots} ${MPIEXEC_PREFLAGS} ${RUNTIME_OUTPUT_DIRECTORY}/${target} ${MPIEXEC_POSTFLAGS} ${command_arguments} && ${clean_test_products}"
    )
  #message(STATUS "Target (mpi): ${target}")

  set_target_properties( ${target}
    PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
    )
  # all done
endfunction(summit_test_driver_mpi)

# register a test based on a compiled driver, comm
function(summit_test_python testfile)

  # get the relative path to the test case local directory so we can set the working dir
  get_filename_component(path ${testfile} DIRECTORY)
  # we run the test cases in their local directory, so we need the base name
  get_filename_component(base ${testfile} NAME)

  # replace path separators with dots
  string(REPLACE "/" "." stem ${path})

  # buld the target 
  set(target "${stem}.${base}")

  #message(STATUS "test file: ${testfile}")
  #message(STATUS "test path: ${path}")
  #message(STATUS "test base: ${base}")
  #message(STATUS "stem: ${stem}")
  
  # define test clean up products, some usual summit default products, don't know how to handle
  # optional ones at this point, as python doesn't like quoting arguments
  set(test_products "*.vtu *.pvtu *.csv")
  set(clean_test_products "rm -rf ${test_products}")

  # make it a test case
  # schedule it to be compiled
  #add_executable(${target} ${testfile})
  #message(STATUS "Target (serial): ${target}")

  add_test(
    NAME ${target}
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}/${path}
    COMMAND ${Python_EXECUTABLE} ${base} ${ARGN}
    )

  # register the runtime environment requirements
  set_property(TEST ${target} PROPERTY ENVIRONMENT
    PYTHONPATH=${SUMMIT_DEST_FULL_PACKAGES}
    )
  # launch from the location of the testcase
  #set_property(TEST ${target} PROPERTY
  #  WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}/${path}
  #  )
  #set_target_properties(${target}
  #  PROPERTIES
  #  RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}/bin"
  #  )
  
  #message(STATUS "Added python test: ${target} to the pile")
  # all done
endfunction(summit_test_python)

